// Copyright (C) 2017 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    default_applicable_licenses: ["bootable_recovery_applypatch_license"],
}

// Added automatically by a large-scale-change
// See: http://go/android-license-faq
license {
    name: "bootable_recovery_applypatch_license",
    visibility: [":__subpackages__"],
    license_kinds: [
        "SPDX-license-identifier-Apache-2.0",
    ],
    license_text: [
        "NOTICE",
    ],
}

cc_defaults {
    name: "applypatch_defaults",

    cflags: [
        "-D_FILE_OFFSET_BITS=64",
        "-DZLIB_CONST",
        "-Wall",
        "-Werror",
    ],

    local_include_dirs: [
        "include",
    ],
}

cc_library_static {
    name: "libapplypatch",

    host_supported: true,
    vendor_available: true,

    defaults: [
        "applypatch_defaults",
    ],

    srcs: [
        "applypatch.cpp",
        "bspatch.cpp",
        "freecache.cpp",
        "imgpatch.cpp",
    ],

    export_include_dirs: [
        "include",
    ],

    static_libs: [
        "libbase",
        "libbspatch",
        "libbz",
        "libedify",
        "libotautil",
        "libz_stable",
    ],

    shared_libs: [
        "libcrypto",
    ],

    target: {
        darwin: {
            enabled: false,
        },
    },
}

cc_library_static {
    name: "libapplypatch_modes",
    vendor_available: true,

    defaults: [
        "applypatch_defaults",
    ],

    srcs: [
        "applypatch_modes.cpp",
    ],

    static_libs: [
        "libapplypatch",
        "libbase",
        "libedify",
        "libotautil",
    ],

    shared_libs: [
        "libcrypto",
    ],
}

cc_binary {
    name: "applypatch",
    vendor: true,

    defaults: [
        "applypatch_defaults",
    ],

    srcs: [
        "applypatch_main.cpp",
    ],

    static_libs: [
        "libapplypatch_modes",
        "libapplypatch",
        "libedify",
        "libotautil",

        // External dependencies.
        "libbspatch",
        "libbrotli",
        "libbz",
    ],

    shared_libs: [
        "libbase",
        "libcrypto",
        "liblog",
        "libz_stable",
        "libziparchive",
    ],

    init_rc: [
        "vendor_flash_recovery.rc",
    ],
}

cc_library_static {
    name: "libimgdiff",
    host_supported: true,
    defaults: [
        "applypatch_defaults",
    ],

    srcs: [
        "imgdiff.cpp",
    ],

    export_include_dirs: [
        "include",
    ],

    static_libs: [
        "libbase",
        "libbsdiff",
        "libdivsufsort",
        "libdivsufsort64",
        "liblog",
        "libotautil",
        "libutils",
        "libz_stable",
        "libziparchive",
    ],
}

cc_binary_host {
    name: "imgdiff",
    srcs: [
        "imgdiff_main.cpp",
    ],

    defaults: [
        "applypatch_defaults",
    ],

    static_libs: [
        "libimgdiff",
        "libotautil",
        "libbsdiff",
        "libdivsufsort",
        "libdivsufsort64",
        "libziparchive",
        "libbase",
        "libutils",
        "liblog",
        "libbrotli",
        "libbz",
        "libz_stable",
    ],
}
