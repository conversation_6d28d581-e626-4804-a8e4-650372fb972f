/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _APPLYPATCH_IMGPATCH_H
#define _APPLYPATCH_IMGPATCH_H

#include <sys/types.h>

#include <functional>

using SinkFn = std::function<size_t(const unsigned char*, size_t)>;

int ApplyImagePatch(const unsigned char* old_data, size_t old_size, const unsigned char* patch_data,
                    size_t patch_size, SinkFn sink);

#endif  // _APPLYPATCH_IMGPATCH_H
