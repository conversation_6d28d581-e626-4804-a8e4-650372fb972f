// Copyright (C) 2019 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "bootable_recovery_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["bootable_recovery_license"],
}

cc_defaults {
    name: "librecovery_utils_defaults",

    defaults: [
        "recovery_defaults",
    ],

    shared_libs: [
        "android.hardware.health@2.0",
        "libbase",
        "libext4_utils",
        "libfs_mgr",
        "libhidlbase",
        "libselinux",
        "libutils",
    ],

    static_libs: [
        "libotautil",

        // External dependencies.
        "libfstab",
        "libhealthhalutils",
    ],
}

// A utility lib that's local to recovery (in contrast, libotautil is exposed to device-specific
// recovery_ui lib as well as device-specific updater).
cc_library_static {
    name: "librecovery_utils",

    recovery_available: true,

    defaults: [
        "librecovery_utils_defaults",
    ],

    srcs: [
        "battery_utils.cpp",
        "logging.cpp",
        "parse_install_logs.cpp",
        "roots.cpp",
        "thermalutil.cpp",
    ],

    header_libs: [
        "libvold_headers",
    ],

    export_include_dirs: [
        "include",
    ],

    export_static_lib_headers: [
        // roots.h includes <fstab/fstab.h>.
        "libfstab",
    ],

    // Should avoid exposing to the libs that might be used in device-specific codes (e.g.
    // libedify, libotautil, librecovery_ui).
    visibility: [
        "//bootable/recovery",
        "//bootable/recovery/install",
        "//bootable/recovery/minadbd",
        "//bootable/recovery/tests",
    ],
}
