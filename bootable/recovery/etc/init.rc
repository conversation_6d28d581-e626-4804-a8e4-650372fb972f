import /init.recovery.${ro.hardware}.rc

on early-init
    # Set the security context of /postinstall if present.
    restorecon /postinstall

    # Copy prebuilt ld.config.txt into linkerconfig directory
    copy /system/etc/ld.config.txt /linkerconfig/ld.config.txt
    chmod 444 /linkerconfig/ld.config.txt

    start ueventd

    setprop sys.usb.configfs 0

on init
    export ANDROID_ROOT /system
    export ANDROID_DATA /data
    export EXTERNAL_STORAGE /sdcard

    symlink /proc/self/fd/0 /dev/stdin
    symlink /proc/self/fd/1 /dev/stdout
    symlink /proc/self/fd/2 /dev/stderr

    symlink /system/bin /bin
    symlink /system/etc /etc

    mkdir /sdcard
    mkdir /system
    mkdir /data
    mkdir /cache
    mkdir /sideload
    mkdir /mnt/system
    mount tmpfs tmpfs /tmp

    chown root shell /tmp
    chmod 0775 /tmp

    write /proc/sys/kernel/panic_on_oops 1
    write /proc/sys/vm/max_map_count 1000000

on boot
    ifup lo
    hostname localhost
    domainname localdomain

    class_start default

on firmware_mounts_complete
   rm /dev/.booting

# Mount filesystems and start core system services.
on late-init
    trigger early-fs
    trigger fs
    trigger post-fs
    trigger post-fs-data

    # Remove a file to wake up anything waiting for firmware
    trigger firmware_mounts_complete

    trigger early-boot
    trigger boot

service ueventd /system/bin/ueventd
    critical
    seclabel u:r:ueventd:s0

service charger /system/bin/charger
    critical
    seclabel u:r:charger:s0

service recovery /system/bin/recovery
    socket recovery stream 422 system system
    seclabel u:r:recovery:s0

service adbd /system/bin/adbd --root_seclabel=u:r:su:s0 --device_banner=recovery
    disabled
    socket adbd stream 660 system system
    seclabel u:r:adbd:s0

service fastbootd /system/bin/fastbootd
    disabled
    group system
    seclabel u:r:fastbootd:s0

# Restart adbd so it can run as root
on property:service.adb.root=1
    restart adbd

on fs && property:sys.usb.configfs=1
    mount configfs none /config
    mkdir /config/usb_gadget/g1 0770 shell shell
    write /config/usb_gadget/g1/idVendor 0x18D1
    mkdir /config/usb_gadget/g1/strings/0x409 0770
    write /config/usb_gadget/g1/strings/0x409/serialnumber ${ro.serialno}
    write /config/usb_gadget/g1/strings/0x409/manufacturer ${ro.product.manufacturer}
    write /config/usb_gadget/g1/strings/0x409/product ${ro.product.model}
    mkdir /config/usb_gadget/g1/functions/ffs.adb
    mkdir /config/usb_gadget/g1/functions/ffs.fastboot
    mkdir /config/usb_gadget/g1/configs/b.1 0777 shell shell
    mkdir /config/usb_gadget/g1/configs/b.1/strings/0x409 0770 shell shell

on fs && property:sys.usb.configfs=0
    write /sys/class/android_usb/android0/f_ffs/aliases adb,fastboot
    write /sys/class/android_usb/android0/idVendor 18D1
    write /sys/class/android_usb/android0/iManufacturer ${ro.product.manufacturer}
    write /sys/class/android_usb/android0/iProduct ${ro.product.model}
    write /sys/class/android_usb/android0/iSerial ${ro.serialno}

on fs
    mkdir /dev/usb-ffs 0775 shell shell
    mkdir /dev/usb-ffs/adb 0770 shell shell
    mount functionfs adb /dev/usb-ffs/adb uid=2000,gid=2000
    mkdir /dev/usb-ffs/fastboot 0770 system system
    mount functionfs fastboot /dev/usb-ffs/fastboot rmode=0770,fmode=0660,uid=1000,gid=1000

on property:sys.usb.config=adb
    start adbd

on property:sys.usb.config=fastboot
    start fastbootd

on property:sys.usb.config=none && property:sys.usb.configfs=0
    stop adbd
    stop fastbootd
    write /sys/class/android_usb/android0/enable 0
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=adb && property:sys.usb.configfs=0
    write /sys/class/android_usb/android0/idProduct D001
    write /sys/class/android_usb/android0/functions adb
    write /sys/class/android_usb/android0/enable 1
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=sideload && property:sys.usb.configfs=0
    write /sys/class/android_usb/android0/idProduct D001
    write /sys/class/android_usb/android0/functions adb
    write /sys/class/android_usb/android0/enable 1
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=fastboot && property:sys.usb.configfs=0
    write /sys/class/android_usb/android0/idProduct 4EE0
    write /sys/class/android_usb/android0/functions fastboot
    write /sys/class/android_usb/android0/enable 1
    setprop sys.usb.state ${sys.usb.config}

# Configfs triggers
on property:sys.usb.config=none && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/UDC "none"
    stop adbd
    stop fastbootd
    setprop sys.usb.ffs.ready 0
    rm /config/usb_gadget/g1/configs/b.1/f1
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=sideload && property:sys.usb.ffs.ready=1 && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idProduct 0xD001
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "adb"
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f1
    write /config/usb_gadget/g1/UDC ${sys.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=adb && property:sys.usb.ffs.ready=1 && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idProduct 0xD001
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "adb"
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f1
    write /config/usb_gadget/g1/UDC ${sys.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=fastboot && property:sys.usb.ffs.ready=1 && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idProduct 0x4EE0
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "fastboot"
    symlink /config/usb_gadget/g1/functions/ffs.fastboot /config/usb_gadget/g1/configs/b.1/f1
    write /config/usb_gadget/g1/UDC ${sys.usb.controller}
    setprop sys.usb.state ${sys.usb.config}
