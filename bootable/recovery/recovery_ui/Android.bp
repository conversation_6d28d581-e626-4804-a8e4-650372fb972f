// Copyright (C) 2019 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "bootable_recovery_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["bootable_recovery_license"],
}

cc_library {
    name: "librecovery_ui",
    recovery_available: true,

    defaults: [
        "recovery_defaults",
    ],

    srcs: [
        "device.cpp",
        "ethernet_device.cpp",
        "ethernet_ui.cpp",
        "screen_ui.cpp",
        "stub_ui.cpp",
        "ui.cpp",
        "vr_ui.cpp",
        "wear_ui.cpp",
    ],

    export_include_dirs: ["include"],

    static_libs: [
        "libminui",
        "libotautil",
    ],

    shared_libs: [
        "libbase",
        "libpng",
        "libz",
    ],
}

// Generic device that uses ScreenRecoveryUI.
cc_library_static {
    name: "librecovery_ui_default",
    recovery_available: true,

    defaults: [
        "recovery_defaults",
    ],

    srcs: [
        "default_device.cpp",
    ],

    export_include_dirs: ["include"],
}

// The default wear device that uses WearRecoveryUI.
cc_library_static {
    name: "librecovery_ui_wear",
    recovery_available: true,

    defaults: [
        "recovery_defaults",
    ],

    srcs: [
        "wear_device.cpp",
    ],

    export_include_dirs: ["include"],
}

// The default VR device that uses VrRecoveryUI.
cc_library_static {
    name: "librecovery_ui_vr",
    recovery_available: true,

    defaults: [
        "recovery_defaults",
    ],

    srcs: [
        "vr_device.cpp",
    ],

    export_include_dirs: ["include"],
}

// The default device that uses EthernetRecoveryUI.
cc_library_static {
    name: "librecovery_ui_ethernet",
    recovery_available: true,

    defaults: [
        "recovery_defaults",
    ],

    srcs: [
        "default_ethernet_device.cpp",
    ],

    shared_libs: [
        "libbase",
    ],

    export_include_dirs: ["include"],
}
