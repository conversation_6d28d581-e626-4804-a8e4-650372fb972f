<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2018 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:padding="4dip"
    android:gravity="center_horizontal"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewBuildtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Current Build:" />

            <TextView
                android:id="@+id/textViewBuild"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/unknown" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="40dp" />

            <TextView
                android:id="@+id/textView4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Apply an update" />

            <TextView
                android:id="@+id/textViewConfigsDirHint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="Config files located in NULL"
                android:textColor="#777"
                android:textSize="10sp"
                android:textStyle="italic" />

            <Spinner
                android:id="@+id/spinnerConfigs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/buttonReload"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onReloadClick"
                    android:text="Reload" />

                <Button
                    android:id="@+id/buttonViewConfig"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onViewConfigClick"
                    android:text="View config" />

                <Button
                    android:id="@+id/buttonApplyConfig"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onApplyConfigClick"
                    android:text="Apply" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textView3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Updater state:" />

                <TextView
                    android:id="@+id/textViewUpdaterState"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:text="@string/unknown" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Engine status:" />

                <TextView
                    android:id="@+id/textViewEngineStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:text="@string/unknown" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Engine error:" />

                <TextView
                    android:id="@+id/textViewEngineErrorCode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:text="@string/unknown" />
            </LinearLayout>


            <ProgressBar
                android:id="@+id/progressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_marginTop="8dp"
                android:min="0"
                android:max="100"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/buttonStop"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onStopClick"
                    android:text="Stop" />

                <Button
                    android:id="@+id/buttonReset"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onResetClick"
                    android:text="Reset" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/buttonSuspend"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onSuspendClick"
                    android:text="Suspend" />

                <Button
                    android:id="@+id/buttonResume"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:onClick="onResumeClick"
                    android:text="Resume" />
            </LinearLayout>

            <TextView
                android:id="@+id/textViewUpdateInfo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:textColor="#777"
                android:textSize="10sp"
                android:textStyle="italic"
                android:text="@string/finish_update_info" />

            <Button
                android:id="@+id/buttonSwitchSlot"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:onClick="onSwitchSlotClick"
                android:text="@string/switch_slot" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
