// Copyright (C) 2019 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "bootable_recovery_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["bootable_recovery_license"],
}

cc_defaults {
    name: "libinstall_defaults",

    defaults: [
        "recovery_defaults",
        "libspl_check_defaults",
    ],

    shared_libs: [
        "libbase",
        "libbootloader_message",
        "libcrypto",
        "libext4_utils",
        "libfs_mgr",
        "libfusesideload",
        "libhidl-gen-utils",
        "libhidlbase",
        "liblog",
        "libselinux",
        "libtinyxml2",
        "libutils",
        "libz",
        "libziparchive",
    ],

    static_libs: [
        "librecovery_utils",
        "libotautil",
        "libsnapshot_nobinder",
        "ota_metadata_proto_cc",

        // external dependencies
        "libvintf",
    ],
}

cc_test_host {
    name: "libinstall_host_unittests",
    defaults: [
        "libspl_check_defaults"
    ],
    srcs: [
        "spl_check_unittests.cpp",
    ],
    static_libs: [
        "libspl_check",
    ],
}

cc_defaults {
    name: "libspl_check_defaults",
    static_libs: [
        "libbase",
        "ota_metadata_proto_cc",
        "liblog",
        "libziparchive",
        "libz",
        "libprotobuf-cpp-lite",
    ],
}

cc_library_static {
    name: "libspl_check",
    recovery_available: true,
    host_supported: true,
    defaults: [
        "libspl_check_defaults",
    ],
    srcs: ["spl_check.cpp"],
    export_include_dirs: [
        "include",
    ],
}

cc_library_static {
    name: "libinstall",
    recovery_available: true,

    defaults: [
        "libinstall_defaults",
    ],

    srcs: [
        "adb_install.cpp",
        "asn1_decoder.cpp",
        "fuse_install.cpp",
        "install.cpp",
        "package.cpp",
        "snapshot_utils.cpp",
        "verifier.cpp",
        "wipe_data.cpp",
        "wipe_device.cpp",
        "spl_check.cpp",
    ],

    header_libs: [
        "libminadbd_headers",
    ],

    shared_libs: [
        "librecovery_ui",
    ],

    export_include_dirs: [
        "include",
    ],

    export_shared_lib_headers: [
        "librecovery_ui",
    ],
}
