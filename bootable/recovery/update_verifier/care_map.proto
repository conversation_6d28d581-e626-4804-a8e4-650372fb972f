/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

package recovery_update_verifier;
option optimize_for = LITE_RUNTIME;

message CareMap {
  message PartitionInfo {
    string name = 1;
    string ranges = 2;
    string id = 3;
    string fingerprint = 4;
  }

  repeated PartitionInfo partitions = 1;
}
