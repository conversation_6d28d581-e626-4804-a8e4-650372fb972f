// Copyright (C) 2018 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "bootable_recovery_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["bootable_recovery_license"],
}

cc_library {
    name: "libminui",
    recovery_available: true,

    defaults: [
        "recovery_defaults",
    ],

    export_include_dirs: [
        "include",
    ],

    srcs: [
        "events.cpp",
        "graphics.cpp",
        "graphics_drm.cpp",
        "graphics_fbdev.cpp",
        "resources.cpp",
    ],

    whole_static_libs: [
        "libdrm",
        "libsync",
    ],

    shared_libs: [
        "libbase",
        "libpng",
        "libz",
    ],
}
