package com.android.server.yyb;

public interface YybAbilityErrorCode {

    int BASE_ERROR_CODE = -1000;
    int PARSE_INSTALL_LISTEN_EXCEPTION = -1001;
    int CLOSE_APP_PACKAGE_NAME_EMPTY = -1002;
    int CLOSE_APP_FAIL = -1003;
    int CLOSE_APP_EXCEPTION = -1004;
    int PARSE_INSTALL_APP_EXCEPTION = -1005;
    int OPEN_APP_PACKAGE_NAME_EMPTY = -1006;
    int LAUNCH_APP_FAIL = -1007;
    int PARSE_LAUNCH_APP_EXCEPTION = -1008;
    int REMOVE_LISTENER_UUID_EMPTY = -1009;
    int REMOVE_LISTENER_FAIL = -1010;
    int REMOVE_LISTENER_EXCEPTION = -1011;
    int UNINSTALL_APP_PACKAGE_NAME_EMPTY = -1012;
    int PARSE_UNINSTALL_APP_EXCEPTION = -1013;
    int INSTALL_APP_FAIL = -1014;
    int UNINSTALL_APP_FAIL = -1015;
    int INSTALL_APP_EXCEPTION = -1016;
    int UNINSTALL_APP_EXCEPTION = -1017;
    int CHANGE_APP_FONT_PACKAGE_NAME_EMPTY = -1018;
    int CHANGE_APP_FONT_SIZE_EMPTY = -1019;
    int CHANGE_APP_FONT_FAIL = -1020;
    int GET_DISK_INFO_FAIL = -1021;
    int LISTEN_DISPLAY_FAIL = -1022;
    int OPEN_APP_NOT_EXIT = -1023;
    int OPEN_APP_DISPLAY_NOT_EXIST = -1024;
    int FILE_PATH_INVALID = -1025;
    int GET_APP_ORIENTATION_PARSE_REQUEST_EXCEPTION = -1026;
    int HANDLER_NOT_FOUNT = -1027;
    int GET_PERMISSION_USAGE_APP_BUT_PERMISSION_TYPE_EMPTY = -1028;
    int WIFI_INFO_ADD_FAILED = -1029;
}
