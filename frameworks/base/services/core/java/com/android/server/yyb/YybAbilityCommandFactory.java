package com.android.server.yyb;


import android.annotation.Nullable;

import com.android.server.yyb.performer.ActiveDisplayPerformer;
import com.android.server.yyb.performer.CancelMuteAbilityPerformer;
import com.android.server.yyb.performer.CheckFileExistsAbilityPerformer;
import com.android.server.yyb.performer.CloseAppAbilityPerformer;
import com.android.server.yyb.performer.GetAppLaunchActivityScreenOrientationAbilityPerformer;
import com.android.server.yyb.performer.GetDiskInfoAbilityPerformer;
import com.android.server.yyb.performer.GetInstallAppListAbilityPerformer;
import com.android.server.yyb.performer.GetPermissionUsageAppPerformer;
import com.android.server.yyb.performer.GetRunningAppAbilityPerformer;
import com.android.server.yyb.performer.GetYybAospVersionPerformer;
import com.android.server.yyb.performer.InstallAppAbilityPerformer;
import com.android.server.yyb.performer.MuteAbilityPerformer;
import com.android.server.yyb.performer.OpenAppAbilityPerformer;
import com.android.server.yyb.performer.RemoveListenerAbilityPerformer;
import com.android.server.yyb.performer.UninstallAppAbilityPerformer;
import com.android.server.yyb.performer.SetAppFontSizePerformer;
import com.android.server.yyb.performer.SetAppVolumeIndexPerformer;
import com.android.server.yyb.performer.SetDisplayFpsPerformer;
import com.android.server.yyb.performer.HostFinishDocumentsOpPerformer;
import com.android.server.yyb.performer.YybMacWifiInfoPerformer;

import java.util.HashMap;
import java.util.Map;

public class YybAbilityCommandFactory {

    private static final Map<AbilityCommandType, IYybAbilityPerformer> PERFORMERS
            = new HashMap<>();

    public YybAbilityCommandFactory() {
        initPerforms();
    }

    private void initPerforms() {
        PERFORMERS.put(AbilityCommandType.MUTE, new MuteAbilityPerformer());
        PERFORMERS.put(AbilityCommandType.CANCEL_MUTE, new CancelMuteAbilityPerformer());
        PERFORMERS.put(AbilityCommandType.GET_DISK_INFO, new GetDiskInfoAbilityPerformer());
        PERFORMERS.put(AbilityCommandType.OPEN_APP, new OpenAppAbilityPerformer());
        PERFORMERS.put(AbilityCommandType.CLOSE_APP, new CloseAppAbilityPerformer());
        PERFORMERS.put(AbilityCommandType.GET_RUNNING_APP, new GetRunningAppAbilityPerformer());
        PERFORMERS.put(AbilityCommandType.INSTALL_APK, new InstallAppAbilityPerformer());
        PERFORMERS.put(AbilityCommandType.UNINSTALL_APK, new UninstallAppAbilityPerformer());
        PERFORMERS.put(AbilityCommandType.REMOVE_LISTENER, new RemoveListenerAbilityPerformer());
        PERFORMERS.put(AbilityCommandType.CHANGE_APP_FONT_SIZE, new SetAppFontSizePerformer());
        PERFORMERS.put(AbilityCommandType.GET_INSTALL_APP, new GetInstallAppListAbilityPerformer());
        PERFORMERS.put(AbilityCommandType.SET_APP_VOLUME_INDEX, new SetAppVolumeIndexPerformer());
        PERFORMERS.put(AbilityCommandType.SET_DISPLAY_FPS, new SetDisplayFpsPerformer());
        PERFORMERS.put(AbilityCommandType.HOST_FINISH_DOCUMENTS_OP,
                new HostFinishDocumentsOpPerformer());
        PERFORMERS.put(AbilityCommandType.GET_YYB_AOSP_VERSION, new GetYybAospVersionPerformer());
        PERFORMERS.put(AbilityCommandType.ACTIVE_DISPLAY, new ActiveDisplayPerformer());
        PERFORMERS.put(AbilityCommandType.CHECK_FILE_EXISTS, new CheckFileExistsAbilityPerformer());
        PERFORMERS.put(AbilityCommandType.GET_LAUNCH_ACTIVITY_SCREEN_ORIENTATION,
                new GetAppLaunchActivityScreenOrientationAbilityPerformer());
        PERFORMERS.put(AbilityCommandType.GET_PERMISSION_USAGE_APP,
                new GetPermissionUsageAppPerformer());
        PERFORMERS.put(AbilityCommandType.SEND_WIFI,
                new YybMacWifiInfoPerformer());
    }

    @Nullable
    public IYybAbilityPerformer createAbilityPerformer(AbilityCommandType type) {
        if (PERFORMERS.containsKey(type)) {
            return PERFORMERS.get(type);
        }
        return null;
    }

}
