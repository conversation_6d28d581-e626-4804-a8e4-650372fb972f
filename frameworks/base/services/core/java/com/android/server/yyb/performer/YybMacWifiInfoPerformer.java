package com.android.server.yyb.performer;

import android.content.Context;
import android.os.IBinder;
import android.os.ServiceManager;
import android.net.wifi.WifiManager;
import android.text.TextUtils;
import android.util.Log;

import com.android.server.yyb.IYybAbilityPerformer;
import com.android.server.yyb.MacWifiInfoReq;
import com.android.server.yyb.SimpleRsp;
import com.android.server.yyb.YybAbilityErrorCode;
import com.android.server.yyb.YybAbilityListenerCache;
import com.android.server.LocalServices;

import com.google.protobuf.ByteString;

public class YybMacWifiInfoPerformer implements IYybAbilityPerformer {

    @Override
    public void perform(Context context, ByteString data, OnPerformerListener performerListener) {
        int errorCode = YybAbilityErrorCode.BASE_ERROR_CODE; // 默认错误码
        try {
            MacWifiInfoReq macWifiInfoReq = MacWifiInfoReq.parseFrom(data);
            Log.i("WifiService","binghuaguo===>addOtherWifiInfo收到了客户端的信息");
            WifiManager mWifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (mWifiManager != null && macWifiInfoReq != null) {
                mWifiManager.addOtherWifiInfo(macWifiInfoReq.getBssidList());
                errorCode = 0;
            }else{
                errorCode = YybAbilityErrorCode.WIFI_INFO_ADD_FAILED;
            }

            ByteString rsp = SimpleRsp.newBuilder().setErrorCode(errorCode).build().toByteString();
            performerListener.onPerform(rsp);
        } catch (Exception e) {
            ByteString rspFail = SimpleRsp.newBuilder().setErrorCode(
                    YybAbilityErrorCode.BASE_ERROR_CODE).build().toByteString();
            performerListener.onPerform(rspFail);
        }
    }
}
