syntax = "proto3";

package com.android.server.yyb;

option java_multiple_files = true;
option java_outer_classname = "Proto";

enum YybAbilityServicePacketType {
  COMMAND = 0;
  LISTENER = 1;
}

enum AbilityCommandType {
  UNKNOWN = 0;
  MUTE = 1;
  CANCEL_MUTE = 2;
  INSTALL_APK = 3;
  UNINSTALL_APK = 4;
  OPEN_APP = 5;
  CLOSE_APP = 6;
  LISTEN_APP_INSTALL = 7;
  GET_RUNNING_APP = 8;
  GET_DISK_INFO = 9;
  TURN_UP_VOLUME = 10;
  TURN_DOWN_VOLUME = 11;
  LISTEN_INSTALL_PROGRESS = 12;
  REMOVE_LISTENER = 13;
  CHANGE_APP_FONT_SIZE = 14;
  GET_INSTALL_APP = 15;
  LISTEN_DISPLAY = 16;
  LISTEN_PACKAGE_ADDED_AND_REMOVED = 17;
  NOTIFY_CHECK_PERMISSION = 18;
  LISTEN_GUEST_APP = 19;
  SET_APP_VOLUME_INDEX = 20;
  SET_DISPLAY_FPS = 21;
  LISTEN_DOCUMENTS = 22;
  GET_YYB_AOSP_VERSION = 23;
  ACTIVE_DISPLAY = 24;
  HOST_FINISH_DOCUMENTS_OP = 25;
  LISTEN_ANDROID_PERMISSION_REQUEST_STATUS_CHANGED = 26;
  CHECK_FILE_EXISTS = 27;
  GET_LAUNCH_ACTIVITY_SCREEN_ORIENTATION = 28;
  GET_PERMISSION_USAGE_APP = 29;
  SEND_WIFI = 30;
}

message SimpleReq {

}

message SimpleRsp {
  int32 error_code = 1;
}

message SimpleErrorRsp {
  int32 error_code = 1;
  string error_msg = 2;
}

message InstallApkReq {
  string apk_path = 1;
}

message UninstallApkReq {
  string package_name = 1;
}

message InstallApkRsp {
  int32 error_code = 1;
  ApkPackageInfo package_info = 2;
  string error_msg = 3;
}

message YybAbilityServicePacket {

  string packet_id = 1;
  YybAbilityServicePacketType packet_type = 2;
  AbilityCommandType command_type = 3;
  bytes data = 4;
  int32 error_code = 5;

}

message ImageMessage {
  int32 width = 1;
  int32 height = 2;
  bytes image_data = 3;
}

message GetRunningAppReq {
  bool include_system_apps = 1;
}

message GetInstallAppReq {
  bool include_system_apps = 1;
}

message InstallAppInfoList {
  int32 error_code = 1;
  repeated ApkPackageInfo app_info_list = 2;
}

message ApkPackageInfo {
  int32 session_id = 1;
  string app_package_name = 2;
  float progress = 3;
  int64 size_bytes = 4;
  string app_label = 5;
  bool is_systema_app = 6;
  string version_code = 7;
  string version_name = 8;
}

message ApkProgress {
  int32 error_code = 1;
  string app_package_name = 2;
  float progress = 3;
  int64 size_bytes = 4;
}

message DiskInfo {
  int32 error_code = 1;
  int64 total_size = 2;
  int64 used_size = 3;
  int64 available_size = 4;
}

message RunningAppInfoList {
  int32 error_code = 1;
  repeated ApkPackageInfo app_info_list = 2;
}

message ListenInstallProgressReq {
  string package_name = 1;
}

message ListenInstallProgressRsp {
  int32 error_code = 1;
  ApkProgress progress = 2;
}

message RemoveListenerReq {
  string uuid = 1;
  AbilityCommandType command_type = 2;
}

message ChangeAppFontSizeReq {
  int32 font_size = 1;
  string app_package_name = 2;
}


message ListenDisplayReq {
}

enum DisplayRspType {
  ROTATION = 0;
}

message ListenDisplayRsp {
  int32 error_code = 1;
  DisplayRspType type = 2;
  RotationRsp rotation = 3;
}

message RotationRsp {
  int32 displayId = 1;
  int32 rotation = 2;
  int32 rotationType = 3;
  bool appPortrait = 4;
}

enum GuestAppRspType {
  GUEST_OPEN_APP = 0;
  GUEST_CLOSE_APP = 1;
  HOST_OPEN_APP = 2;
  HOST_CLOSE_APP = 3;
  APP_STOPPED = 4;
}

message ListenGuestAppReq {

}

message ListenGuestAppRsp {
  int32 error_code = 1;
  GuestAppRspType type = 2;
  GuestOpenAppRequest guestOpenAppReq = 3;
  GuestCloseAppRequest guestCloseAppReq = 4;
  OpenAppResult  openAppResult = 5;
  CloseAppResult closeAppResult = 6;
  AppProcessStopped appProcessStopped = 7;
}

message GuestOpenAppRequest {
  string app_package_name = 1;
  bool is_displayed = 2;
}

enum GuestCloseScene {
  BACK_PREESSED = 0;
  START_LAUNCHER = 1;
  MOVE_TASK_TO_BACK = 3;
  FROM_FINISH = 4;
}

message GuestCloseAppRequest {
  string app_package_name = 1;
  GuestCloseScene scene = 2;
  string reason = 3;
}

message OpenAppResult {
  int32 status = 1;
  string message = 2;
  string app_package_name = 3;
}

message HostOpenAppReq {
  string app_package_name = 1;
  int32  gustDisplayId = 2;
}

message HostCloseAppReq {
  string app_package_name = 1;
}

message CloseAppResult {
  int32 status = 1;
  string message = 2;
  string app_package_name = 3;
}

enum AppStoppedType {
  STOPED_BY_APP_SELF = 0;
  STOPED_BY_HOST_CLOSE = 1;
  STOPED_BY_TASK_IS_EMPTY = 2;
}

message AppProcessStopped {
  string app_package_name = 1;
  AppStoppedType stopedType = 2;
  string reason = 3;
}

message ActiveDisplayReq {
  int32 guestDisplayId = 1;
}

message PackageAddedOrRemovedRsp {
  int32 error_code = 1;
  bool isAdded = 2;
  ApkPackageInfo  package_info = 3;
}

enum AppPermissionType {
  LOCATION = 0;
  CAMERA = 1;
  RECORD_AUDIO = 2;
}

message AppVolumeIndexRsp {
  int32 app_volume_index = 1;
  string app_package_name = 2;
}

message DisplayFpsRsq {
  int32 display_fps_index = 1;
}

message ListenDocumentsReq {

}

enum DocumentsIntentAction {
  ACTION_GET_AND_OPEN = 0;
  ACTION_CREATE_DOCUMENT = 1;
  ACTION_OPEN_DOCUMENT_TREE = 2;
  ACTION_VIEW = 3;
  ACTION_SAVE_FINISH = 4;
  INPUT = 5;
}

message GetAndOpenDocumentExtra{
  bool allow_multiple = 1;
  repeated string mine_type = 2;
  string initial_tree_url = 3;
}

message CreateDocumentExtra{
  string suggested_name = 1;
  string initial_tree_url = 2;
  string mime_type = 3;
}

message OpenDocumentTreeExtra{
  string initial_tree_url = 1;
}

message ViewDocumentExtra{
  string initial_tree_url = 1;
}

message SaveDocumentfinishExtra{
  string document_url = 1;
}
message InputExtra{
  bool is_open = 1;
}

message ListenDocumentsRsq {
  string app_package_name = 1;
  DocumentsIntentAction type = 2;
  GetAndOpenDocumentExtra getAndOpenDocumentExtra = 3;
  CreateDocumentExtra createDocumentExtra = 4;
  OpenDocumentTreeExtra openDocumentTreeExtra = 5;
  ViewDocumentExtra viewDocumentExtra = 6;
  SaveDocumentfinishExtra saveDocumentfinishExtra = 7;
  InputExtra inputExtra = 8;
}

enum DocumentActionType {
  USER_CANCEL = 0;
  GET_CONTENT = 1;
  OPEN_DOCUMENT = 2;
  CREATE_DOCUMENT = 3;
  OPEN_DOCUMENT_TREE = 4;
  VIEW_DOCUMENT = 5;
}

message HostFinishDocumentsOpReq {
  DocumentActionType type = 1;
  string app_package_name = 2;
  repeated string file_path = 3;
  string file = 4;
}

message YybAospVersionRsp {
  string yyb_aosp_version = 1;
}

message ListenAndroidPermissionRequestStatusChangedRsp {
  AppPermissionType permission = 1;
  bool is_started = 2;
}

message CheckFileExistsReq {
  string file_path = 1;
}

message CheckFileExistsRsp {
  int32 error_code = 1;
  string error_msg = 2;
  bool is_exists = 3;
  bool is_file = 4;
}

message GetAppLaunchActivityScreenOrientationReq {
  string package_name = 1;
}

message GetAppLaunchActivityScreenOrientationRsp {
  int32 error_code = 1;
  int32 screen_orientation = 2;
}

message GetPermissionUsageAppReq {
  AppPermissionType permission_type = 1;
}

message GetPermissionUsageAppRsp {
  repeated string package_name = 1;
}

message MacWifiInfoReq {
  repeated string bssid = 1;
}
