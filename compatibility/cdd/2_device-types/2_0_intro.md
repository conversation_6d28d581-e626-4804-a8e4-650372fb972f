# 2\. Device Types

While the Android Open Source Project provides a software stack that can be used
for a variety of device types and form factors, there are a few device types
that have a relatively better established application distribution ecosystem.

This section describes those device types, and additional requirements and
recommendations applicable for each device type.

All Android device implementations that do not fit into any of the described
device types MUST still meet all requirements in the other sections of this
Compatibility Definition.
