# 10\. Software Compatibility Testing

Device implementations MUST pass all tests described in this
section.
However, note that no software test package is fully comprehensive.
For this reason, device implementers are **STRONGLY RECOMMENDED** to make the
minimum number of changes as possible to the reference and preferred
implementation of Android available from the Android Open Source Project.
This will minimize the risk of introducing bugs that create incompatibilities
requiring rework and potential device updates.
