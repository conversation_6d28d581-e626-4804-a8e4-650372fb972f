/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.tencent.yyb.mds;

import android.content.Context;
import android.app.Service;
import android.content.Intent;
import android.os.Handler;
import android.os.IBinder;
import android.util.Log;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.view.Surface;
import android.os.Messenger;
import android.content.ComponentName;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.app.ActivityOptions;
import android.app.ActivityManager;

import java.util.List;
import java.util.Objects;


public class MultiDisplayService extends Service {
    private static final String TAG = "MultiDisplayService";
    private static final String DISPLAY_NAME = "YYB_Display_";
    private static final String[] UNIQUE_DISPLAY_ID = new String[]{"notUsed", "1234562",
            "1234563", "1234564",
            "1234565", "1234566",
            "1234567", "1234568",
            "1234569", "1234570",
            "1234571"};
    private static final int MAX_DISPLAYS = 10;
    private static final int mFlags = DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC |
            DisplayManager.VIRTUAL_DISPLAY_FLAG_OWN_CONTENT_ONLY |
            DisplayManager.VIRTUAL_DISPLAY_FLAG_ROTATES_WITH_CONTENT |
            DisplayManager.VIRTUAL_DISPLAY_FLAG_TRUSTED |
            1 << 6 |//DisplayManager.VIRTUAL_DISPLAY_FLAG_SUPPORTS_TOUCH
            1 << 9; //DisplayManager.VIRTUAL_DISPLAY_FLAG_SHOULD_SHOW_SYSTEM_DECORATIONS;

    private static final int SUCCESS = 0;
    private static final int DISPLAY_ALREADY_EXIST = -1000;
    private static final int DISPLAY_MAX_COUNT = -1001;
    private static final int DISPLAY_ID_INVALID = -1002;
    private static final int DISPLAY_PARAMS_INVALID = -1003;
    private static final int DISPLAY_NOT_EXIST = 1004;

    private DisplayManager mDisplayManager;
    private VirtualDisplay mVirtualDisplay[];
    private Surface mSurface[];
    private Messenger mMessenger;
    private ListenerThread mListener;

    private final Handler mHandler = new Handler();

    class MultiDisplay {
        public int width;
        public int height;
        public int dpi;
        public int flag;
        public VirtualDisplay virtualDisplay;
        public Surface surface;
        public boolean enabled;

        MultiDisplay() {
            clear();
        }

        public void clear() {
            width = 0;
            height = 0;
            dpi = 0;
            flag = 0;
            virtualDisplay = null;
            surface = null;
            enabled = false;
        }

        public void set(int w, int h, int d, int f) {
            width = w;
            height = h;
            dpi = d;
            flag = f;
            enabled = true;
        }

        public boolean match(int w, int h, int d, int f) {
            return (w == width && h == height && d == dpi && f == flag);
        }
    }

    private MultiDisplay mMultiDisplay[];

    @Override
    public void onCreate() {
        super.onCreate();

        try {
            System.loadLibrary("yyb_mds_jni");
        } catch (Exception e) {
            Log.e(TAG, "Failed to loadLibrary: " + e);
        }

        mListener = new ListenerThread(this);
        mListener.start();

        mDisplayManager = (DisplayManager) getSystemService(Context.DISPLAY_SERVICE);
        mMultiDisplay = new MultiDisplay[MAX_DISPLAYS + 1];
        for (int i = 0; i < MAX_DISPLAYS + 1; i++) {
            mMultiDisplay[i] = new MultiDisplay();
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        if (mMessenger == null) {
            mMessenger = new Messenger(mHandler);
        }
        return mMessenger.getBinder();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // keep it alive.
        return START_STICKY;
    }

    class ListenerThread extends Thread {
        private Context mContext;

        ListenerThread(Context context) {
            super(TAG);
            mContext = context;
        }

        private void deleteVirtualDisplay(int displayId) {
            if (!mMultiDisplay[displayId].enabled) {
                notifyDeleteResult(displayId, DISPLAY_NOT_EXIST);
                return;
            }
            if (mMultiDisplay[displayId].virtualDisplay != null) {
                mMultiDisplay[displayId].virtualDisplay.release();
            }
            if (mMultiDisplay[displayId].surface != null) {
                mMultiDisplay[displayId].surface.release();
            }
            mMultiDisplay[displayId].clear();
            nativeReleaseListener(displayId);
            notifyDeleteResult(displayId, SUCCESS);
        }

        private void createVirtualDisplay(int displayId, int w, int h, int dpi, int flag) {
            mMultiDisplay[displayId].surface = nativeCreateSurface(displayId, w, h);
            mMultiDisplay[displayId].virtualDisplay = mDisplayManager.createVirtualDisplay(
                    null /* projection */,
                    DISPLAY_NAME + displayId , w, h, dpi,
                    mMultiDisplay[displayId].surface, flag,
                    null /* callback */,
                    null /* handler */,
                    UNIQUE_DISPLAY_ID[displayId]);
            mMultiDisplay[displayId].set(w, h, dpi, flag);
        }

        private void createDisplay(int displayId, int w, int h, int dpi, int flag) {
            if (mMultiDisplay[displayId].match(w, h, dpi, flag)) {
                notifyCreateResult(displayId, -1, DISPLAY_ALREADY_EXIST);
                return;
            }

            VirtualDisplay display = mMultiDisplay[displayId].virtualDisplay;
            if (display == null) {
                createVirtualDisplay(displayId, w, h, dpi, flag);
                display = mMultiDisplay[displayId].virtualDisplay;
            }

            if (display != null) {
                // 创建成功
                notifyCreateResult(displayId, display.getDisplay().getDisplayId(), SUCCESS);
            } else {
                notifyCreateResult(displayId, -1, DISPLAY_NOT_EXIST);
            }
        }

        private void updateDisplaySize(int displayId, int w, int h, int dpi) {
            if (!mMultiDisplay[displayId].enabled) {
                notifyUpdateSizeResult(displayId, w, h, dpi, DISPLAY_NOT_EXIST);
                return;
            }
            if (mMultiDisplay[displayId].width != w || mMultiDisplay[displayId].height != h) {
                nativeResizeListener(displayId, w, h);
            }
            mMultiDisplay[displayId].virtualDisplay.resize(w, h, dpi);
            int flag = mMultiDisplay[displayId].flag;
            mMultiDisplay[displayId].set(w, h, dpi, flag);
            notifyUpdateSizeResult(displayId, w, h, dpi, SUCCESS);
        }


        private void addVirtualDisplay(int displayId, int w, int h, int dpi, int flag) {
            if (mMultiDisplay[displayId].match(w, h, dpi, flag)) {
                return;
            }
            if (mMultiDisplay[displayId].virtualDisplay == null) {
                createVirtualDisplay(displayId, w, h, dpi, flag);
                return;
            }
            if (mMultiDisplay[displayId].flag != flag) {
                deleteVirtualDisplay(displayId);
                createVirtualDisplay(displayId, w, h, dpi, flag);
                return;
            }
            if (mMultiDisplay[displayId].width != w || mMultiDisplay[displayId].height != h) {
                nativeResizeListener(displayId, w, h);
            }
            // only dpi changes
            mMultiDisplay[displayId].virtualDisplay.resize(w, h, dpi);
            mMultiDisplay[displayId].set(w, h, dpi, flag);
        }

        private void setDisplayState(int displayId, int type) {
            if (displayId < 1 || displayId > MAX_DISPLAYS) {
                return;
            }
            if (!mMultiDisplay[displayId].enabled) {
                return;
            }
            if (mMultiDisplay[displayId].virtualDisplay == null) {
                return;
            }
            mMultiDisplay[displayId].virtualDisplay.setDisplayState(
                    type == MultiDisplayInfo.ON_DISPLAY);
        }

        private void rotateDisplay(int displayId,int rotation, int type){
            if (displayId < 1 || displayId > MAX_DISPLAYS) {
                return;
            }
            if (!mMultiDisplay[displayId].enabled) {
                return;
            }
            if (mMultiDisplay[displayId].virtualDisplay == null) {
                return;
            }
            if (mDisplayManager != null) {
                VirtualDisplay vd = mMultiDisplay[displayId].virtualDisplay;
                if (vd.getDisplay() != null) {
                    int rotationType = DisplayManager.USER_ROTATION_CONTENT;
                    if (type == MultiDisplayInfo.ROTATE_DISPLAY) {
                        rotationType = DisplayManager.USER_ROTATION_DISPLAY;
                    }
                    mDisplayManager.setDisplayRotation(vd.getDisplay().getDisplayId(),
                            rotation, rotationType);
                }
            }
        }

//        private void handleStartApp(int i, int width, int height, int dpi, int flag, final
//        String packageName) {
//            if (packageName == null || packageName.isEmpty()) {
//                Log.e(TAG, "packageName is null or empty, avoid create display");
//                nativeOpenResult(i, Integer.MAX_VALUE, PACKAGE_NAME_INVALID);
//                return;
//            }
//            if (i > MAX_DISPLAYS || width <= 0 || height <= 0 || dpi <= 0
//                    || flag < 0) {
//                Log.e(TAG, "handleStartApp invalid parameters for add/modify display ");
//                nativeOpenResult(i, Integer.MAX_VALUE, DISPLAY_PARAMS_INVALID);
//                return;
//            }
//            PackageManager pm = mContext.getPackageManager();
//            Intent mainIntent = new Intent(Intent.ACTION_MAIN);
//            mainIntent.addCategory(Intent.CATEGORY_LAUNCHER);
//            mainIntent.setPackage(packageName);
//            // 先判断启动的合法性
//            List<ResolveInfo> activities = pm.queryIntentActivities(mainIntent, PackageManager
//            .MATCH_ALL);
//            if (activities.isEmpty()) {
//                Log.e(TAG, "要启动的app(" + packageName + ") 不存在");
//                nativeOpenResult(i, Integer.MAX_VALUE, APP_NOT_EXIT);
//                return;
//            }
//            // todo by franticzhou 这里需要考虑是否拦截 displayId 一致但是包名不同的情况
//            int displayId = -1;
//            if (i > 0) {
//                addVirtualDisplay(i, width, height, dpi, flag, packageName);
//                VirtualDisplay vd = mMultiDisplay[i].virtualDisplay;
//                if (vd == null) {
//                    Log.e(TAG, "VirtualDisplay notfuond");
//                    return;
//                }
//                displayId = vd.getDisplay().getDisplayId();
//            } else {
//                displayId = 0;
//                // 主屏的handle 就是 1
//                int colorBufferHandle = 1;
//                fillData(displayId, colorBufferHandle);
//                mMultiDisplay[0].appPackageName = packageName;
//            }
//            final ActivityOptions options = ActivityOptions.makeBasic();
//            options.setLaunchDisplayId(displayId);
//            ResolveInfo resolveInfo = activities.get(0);
//            ComponentName component = new ComponentName(
//                    resolveInfo.activityInfo.packageName,
//                    resolveInfo.activityInfo.name);
//
//            final Intent launchIntent = new Intent();
//            launchIntent.setComponent(component);
//            // launchIntent.setFlags(intentFlags);
//            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
//                    Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
//            final int fi = i;
//            final int fdid = displayId;
//            Runnable task = new Runnable(){
//                public void run(){
//                    try {
//                        mContext.startActivity(launchIntent, options.toBundle());
//                        nativeOpenResult(fi, fdid, 0);
//                        Log.e(TAG, "Launching " + packageName + " on display " + fdid);
//                    } catch (SecurityException e) {
//                        Log.e(TAG, "Permission denied to launch: " + packageName, e);
//                        nativeOpenResult(fi, fdid, UNKOWN_ERROR);
//                    }
//                }
//            };
//
//            if(displayId != 0){
//                mMultiDisplay[i].pendingTask = task;
//            } else {
//                task.run();
//            }
//
//        }

//        private void handleCloseApp(int i, String packageName) {
//            // 关闭 app
//            if (i > MAX_DISPLAYS || i < 0) {
//                Log.e(TAG, "invalid parameters for delete display");
//                return;
//            }
//            if (packageName != null && !packageName.isEmpty()) {
//                try {
//                    ActivityManager am = (ActivityManager) mContext.getSystemService(Context
//                    .ACTIVITY_SERVICE);
//                    am.forceStopPackage(packageName);
//                } catch (Exception e) {
//                    Log.e(TAG, "failed stop " + packageName, e);
//                }
//            }
//            // display 0 不能删除
//            if (i > 0) {
//                Log.e(TAG, "handleCloseApp close display " + i + " app:" + packageName);
//                deleteVirtualDisplay(i);
//            }
//        }

        @Override
        public void run() {
            while (nativeOpen() <= 0) {
                Log.e(TAG, "failed to open multiDisplay pipe, retry");
            }
            while (true) {
                MultiDisplayInfo multiDisplayInfo;
                if ((multiDisplayInfo = nativeReadPipe()) == null) {
                    continue;
                }
                int type = multiDisplayInfo.getType();
                int i = multiDisplayInfo.getDisplayId();
                int width = multiDisplayInfo.getWidth();
                int height = multiDisplayInfo.getHeight();
                int dpi = multiDisplayInfo.getDpi();
                int flag = (multiDisplayInfo.getFlag() != 0) ? multiDisplayInfo.getFlag() : mFlags;
                int rotation = multiDisplayInfo.getRotation() % 4;
                switch (type) {
                    case MultiDisplayInfo.CREATE_DISPLAY: {
                        if (i < 1 || i > MAX_DISPLAYS) {
                            notifyCreateResult(i, -1, DISPLAY_ID_INVALID);
                            break;
                        }
                        if (width <= 0 || height <= 0 || dpi <= 0 || flag < 0) {
                            Log.e(TAG, "invalid parameters for add/modify display");
                            notifyCreateResult(i, -1, DISPLAY_PARAMS_INVALID);
                            break;
                        }
                        createDisplay(i, width, height, dpi, flag);
                        break;
                    }
                    case MultiDisplayInfo.CLOSE_DISPLAY: {
                        Log.d(TAG, "DEL " + i);
                        if (i < 1 || i > MAX_DISPLAYS) {
                            Log.e(TAG, "invalid parameters for delete display");
                            notifyDeleteResult(i, DISPLAY_ID_INVALID);
                            break;
                        }
                        deleteVirtualDisplay(i);
                        break;
                    }
                    case MultiDisplayInfo.UPDATE_SIZE: {
                        updateDisplaySize(i, width, height, dpi);
                        break;
                    }
                    case MultiDisplayInfo.OFF_DISPLAY:
                    case MultiDisplayInfo.ON_DISPLAY: {
                        setDisplayState(i,type);
                        break;
                    }
                    case MultiDisplayInfo.ROTATE_CONTENT:
                    case MultiDisplayInfo.ROTATE_DISPLAY: {
                        rotateDisplay(i, rotation, type);
                        break;
                    }
                    default:
                        break;
                }
            }
        }
    }

    private void notifyCreateResult(int displayId, int guestDisplayId, int status) {
        int data[] = new int[]{MultiDisplayInfo.CREATE_DISPLAY, status, displayId, guestDisplayId};
        nativeResult(data);
    }

    private void notifyUpdateSizeResult(int displayId, int w, int h, int dpi, int status) {
        int data[] = new int[]{MultiDisplayInfo.UPDATE_SIZE, status, displayId, w, h, dpi};
        nativeResult(data);
    }

    private void notifyDeleteResult(int displayId, int status) {
        int data[] = new int[]{MultiDisplayInfo.CLOSE_DISPLAY, status, displayId};
        nativeResult(data);
    }

    private native int nativeOpen();

    private native Surface nativeCreateSurface(int displayId, int width, int height);

    private native boolean nativeReleaseListener(int displayId);

    private native boolean nativeResizeListener(int displayId, int with, int height);

    private native MultiDisplayInfo nativeReadPipe();

    private native void nativeResult(int data[]);

}
