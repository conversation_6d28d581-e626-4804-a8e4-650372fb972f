// Generated Code - DO NOT EDIT !!
// generated by 'emugen'
#include <stdio.h>
#include <stdlib.h>
#include "gl2_client_context.h"

extern "C" {
	void glActiveTexture(GLenum texture);
	void glAttachShader(GLuint program, GLuint shader);
	void glBindAttribLocation(GLuint program, GLuint index, const GLchar* name);
	void glBindBuffer(GLenum target, GLuint buffer);
	void glBindFramebuffer(GLenum target, GLuint framebuffer);
	void glBindRenderbuffer(GLenum target, GLuint renderbuffer);
	void glBindTexture(GLenum target, GLuint texture);
	void glBlendColor(GLclampf red, GLclampf green, GLclampf blue, GLclampf alpha);
	void glBlendEquation(GLenum mode);
	void glBlendEquationSeparate(GLenum modeRGB, GLenum modeAlpha);
	void glBlendFunc(GLenum sfactor, GLenum dfactor);
	void glBlendFuncSeparate(GLenum srcRGB, GLenum dstRGB, GLenum srcAlpha, GLenum dstAlpha);
	void glBufferData(GLenum target, GLsizeiptr size, const GLvoid* data, GLenum usage);
	void glBufferSubData(GLenum target, GLintptr offset, GLsizeiptr size, const GLvoid* data);
	GLenum glCheckFramebufferStatus(GLenum target);
	void glClear(GLbitfield mask);
	void glClearColor(GLclampf red, GLclampf green, GLclampf blue, GLclampf alpha);
	void glClearDepthf(GLclampf depth);
	void glClearStencil(GLint s);
	void glColorMask(GLboolean red, GLboolean green, GLboolean blue, GLboolean alpha);
	void glCompileShader(GLuint shader);
	void glCompressedTexImage2D(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLint border, GLsizei imageSize, const GLvoid* data);
	void glCompressedTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLsizei imageSize, const GLvoid* data);
	void glCopyTexImage2D(GLenum target, GLint level, GLenum internalformat, GLint x, GLint y, GLsizei width, GLsizei height, GLint border);
	void glCopyTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint x, GLint y, GLsizei width, GLsizei height);
	GLuint glCreateProgram();
	GLuint glCreateShader(GLenum type);
	void glCullFace(GLenum mode);
	void glDeleteBuffers(GLsizei n, const GLuint* buffers);
	void glDeleteFramebuffers(GLsizei n, const GLuint* framebuffers);
	void glDeleteProgram(GLuint program);
	void glDeleteRenderbuffers(GLsizei n, const GLuint* renderbuffers);
	void glDeleteShader(GLuint shader);
	void glDeleteTextures(GLsizei n, const GLuint* textures);
	void glDepthFunc(GLenum func);
	void glDepthMask(GLboolean flag);
	void glDepthRangef(GLclampf zNear, GLclampf zFar);
	void glDetachShader(GLuint program, GLuint shader);
	void glDisable(GLenum cap);
	void glDisableVertexAttribArray(GLuint index);
	void glDrawArrays(GLenum mode, GLint first, GLsizei count);
	void glDrawElements(GLenum mode, GLsizei count, GLenum type, const GLvoid* indices);
	void glEnable(GLenum cap);
	void glEnableVertexAttribArray(GLuint index);
	void glFinish();
	void glFlush();
	void glFramebufferRenderbuffer(GLenum target, GLenum attachment, GLenum renderbuffertarget, GLuint renderbuffer);
	void glFramebufferTexture2D(GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level);
	void glFrontFace(GLenum mode);
	void glGenBuffers(GLsizei n, GLuint* buffers);
	void glGenerateMipmap(GLenum target);
	void glGenFramebuffers(GLsizei n, GLuint* framebuffers);
	void glGenRenderbuffers(GLsizei n, GLuint* renderbuffers);
	void glGenTextures(GLsizei n, GLuint* textures);
	void glGetActiveAttrib(GLuint program, GLuint index, GLsizei bufsize, GLsizei* length, GLint* size, GLenum* type, GLchar* name);
	void glGetActiveUniform(GLuint program, GLuint index, GLsizei bufsize, GLsizei* length, GLint* size, GLenum* type, GLchar* name);
	void glGetAttachedShaders(GLuint program, GLsizei maxcount, GLsizei* count, GLuint* shaders);
	int glGetAttribLocation(GLuint program, const GLchar* name);
	void glGetBooleanv(GLenum pname, GLboolean* params);
	void glGetBufferParameteriv(GLenum target, GLenum pname, GLint* params);
	GLenum glGetError();
	void glGetFloatv(GLenum pname, GLfloat* params);
	void glGetFramebufferAttachmentParameteriv(GLenum target, GLenum attachment, GLenum pname, GLint* params);
	void glGetIntegerv(GLenum pname, GLint* params);
	void glGetProgramiv(GLuint program, GLenum pname, GLint* params);
	void glGetProgramInfoLog(GLuint program, GLsizei bufsize, GLsizei* length, GLchar* infolog);
	void glGetRenderbufferParameteriv(GLenum target, GLenum pname, GLint* params);
	void glGetShaderiv(GLuint shader, GLenum pname, GLint* params);
	void glGetShaderInfoLog(GLuint shader, GLsizei bufsize, GLsizei* length, GLchar* infolog);
	void glGetShaderPrecisionFormat(GLenum shadertype, GLenum precisiontype, GLint* range, GLint* precision);
	void glGetShaderSource(GLuint shader, GLsizei bufsize, GLsizei* length, GLchar* source);
	const GLubyte* glGetString(GLenum name);
	void glGetTexParameterfv(GLenum target, GLenum pname, GLfloat* params);
	void glGetTexParameteriv(GLenum target, GLenum pname, GLint* params);
	void glGetUniformfv(GLuint program, GLint location, GLfloat* params);
	void glGetUniformiv(GLuint program, GLint location, GLint* params);
	int glGetUniformLocation(GLuint program, const GLchar* name);
	void glGetVertexAttribfv(GLuint index, GLenum pname, GLfloat* params);
	void glGetVertexAttribiv(GLuint index, GLenum pname, GLint* params);
	void glGetVertexAttribPointerv(GLuint index, GLenum pname, GLvoid** pointer);
	void glHint(GLenum target, GLenum mode);
	GLboolean glIsBuffer(GLuint buffer);
	GLboolean glIsEnabled(GLenum cap);
	GLboolean glIsFramebuffer(GLuint framebuffer);
	GLboolean glIsProgram(GLuint program);
	GLboolean glIsRenderbuffer(GLuint renderbuffer);
	GLboolean glIsShader(GLuint shader);
	GLboolean glIsTexture(GLuint texture);
	void glLineWidth(GLfloat width);
	void glLinkProgram(GLuint program);
	void glPixelStorei(GLenum pname, GLint param);
	void glPolygonOffset(GLfloat factor, GLfloat units);
	void glReadPixels(GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLvoid* pixels);
	void glReleaseShaderCompiler();
	void glRenderbufferStorage(GLenum target, GLenum internalformat, GLsizei width, GLsizei height);
	void glSampleCoverage(GLclampf value, GLboolean invert);
	void glScissor(GLint x, GLint y, GLsizei width, GLsizei height);
	void glShaderBinary(GLsizei n, const GLuint* shaders, GLenum binaryformat, const GLvoid* binary, GLsizei length);
	void glShaderSource(GLuint shader, GLsizei count, const GLchar* const* string, const GLint* length);
	void glStencilFunc(GLenum func, GLint ref, GLuint mask);
	void glStencilFuncSeparate(GLenum face, GLenum func, GLint ref, GLuint mask);
	void glStencilMask(GLuint mask);
	void glStencilMaskSeparate(GLenum face, GLuint mask);
	void glStencilOp(GLenum fail, GLenum zfail, GLenum zpass);
	void glStencilOpSeparate(GLenum face, GLenum fail, GLenum zfail, GLenum zpass);
	void glTexImage2D(GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLint border, GLenum format, GLenum type, const GLvoid* pixels);
	void glTexParameterf(GLenum target, GLenum pname, GLfloat param);
	void glTexParameterfv(GLenum target, GLenum pname, const GLfloat* params);
	void glTexParameteri(GLenum target, GLenum pname, GLint param);
	void glTexParameteriv(GLenum target, GLenum pname, const GLint* params);
	void glTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLenum type, const GLvoid* pixels);
	void glUniform1f(GLint location, GLfloat x);
	void glUniform1fv(GLint location, GLsizei count, const GLfloat* v);
	void glUniform1i(GLint location, GLint x);
	void glUniform1iv(GLint location, GLsizei count, const GLint* v);
	void glUniform2f(GLint location, GLfloat x, GLfloat y);
	void glUniform2fv(GLint location, GLsizei count, const GLfloat* v);
	void glUniform2i(GLint location, GLint x, GLint y);
	void glUniform2iv(GLint location, GLsizei count, const GLint* v);
	void glUniform3f(GLint location, GLfloat x, GLfloat y, GLfloat z);
	void glUniform3fv(GLint location, GLsizei count, const GLfloat* v);
	void glUniform3i(GLint location, GLint x, GLint y, GLint z);
	void glUniform3iv(GLint location, GLsizei count, const GLint* v);
	void glUniform4f(GLint location, GLfloat x, GLfloat y, GLfloat z, GLfloat w);
	void glUniform4fv(GLint location, GLsizei count, const GLfloat* v);
	void glUniform4i(GLint location, GLint x, GLint y, GLint z, GLint w);
	void glUniform4iv(GLint location, GLsizei count, const GLint* v);
	void glUniformMatrix2fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glUniformMatrix3fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glUniformMatrix4fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glUseProgram(GLuint program);
	void glValidateProgram(GLuint program);
	void glVertexAttrib1f(GLuint indx, GLfloat x);
	void glVertexAttrib1fv(GLuint indx, const GLfloat* values);
	void glVertexAttrib2f(GLuint indx, GLfloat x, GLfloat y);
	void glVertexAttrib2fv(GLuint indx, const GLfloat* values);
	void glVertexAttrib3f(GLuint indx, GLfloat x, GLfloat y, GLfloat z);
	void glVertexAttrib3fv(GLuint indx, const GLfloat* values);
	void glVertexAttrib4f(GLuint indx, GLfloat x, GLfloat y, GLfloat z, GLfloat w);
	void glVertexAttrib4fv(GLuint indx, const GLfloat* values);
	void glVertexAttribPointer(GLuint indx, GLint size, GLenum type, GLboolean normalized, GLsizei stride, const GLvoid* ptr);
	void glViewport(GLint x, GLint y, GLsizei width, GLsizei height);
	void glEGLImageTargetTexture2DOES(GLenum target, GLeglImageOES image);
	void glEGLImageTargetRenderbufferStorageOES(GLenum target, GLeglImageOES image);
	void glGetProgramBinaryOES(GLuint program, GLsizei bufSize, GLsizei* length, GLenum* binaryFormat, GLvoid* binary);
	void glProgramBinaryOES(GLuint program, GLenum binaryFormat, const GLvoid* binary, GLint length);
	void* glMapBufferOES(GLenum target, GLenum access);
	GLboolean glUnmapBufferOES(GLenum target);
	void glTexImage3DOES(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLenum format, GLenum type, const GLvoid* pixels);
	void glTexSubImage3DOES(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, const GLvoid* pixels);
	void glCopyTexSubImage3DOES(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLint x, GLint y, GLsizei width, GLsizei height);
	void glCompressedTexImage3DOES(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLsizei imageSize, const GLvoid* data);
	void glCompressedTexSubImage3DOES(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLsizei imageSize, const GLvoid* data);
	void glFramebufferTexture3DOES(GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level, GLint zoffset);
	void glBindVertexArrayOES(GLuint array);
	void glDeleteVertexArraysOES(GLsizei n, const GLuint* arrays);
	void glGenVertexArraysOES(GLsizei n, GLuint* arrays);
	GLboolean glIsVertexArrayOES(GLuint array);
	void glDiscardFramebufferEXT(GLenum target, GLsizei numAttachments, const GLenum* attachments);
	void glMultiDrawArraysEXT(GLenum mode, const GLint* first, const GLsizei* count, GLsizei primcount);
	void glMultiDrawElementsEXT(GLenum mode, const GLsizei* count, GLenum type, const GLvoid* const* indices, GLsizei primcount);
	void glGetPerfMonitorGroupsAMD(GLint* numGroups, GLsizei groupsSize, GLuint* groups);
	void glGetPerfMonitorCountersAMD(GLuint group, GLint* numCounters, GLint* maxActiveCounters, GLsizei counterSize, GLuint* counters);
	void glGetPerfMonitorGroupStringAMD(GLuint group, GLsizei bufSize, GLsizei* length, GLchar* groupString);
	void glGetPerfMonitorCounterStringAMD(GLuint group, GLuint counter, GLsizei bufSize, GLsizei* length, GLchar* counterString);
	void glGetPerfMonitorCounterInfoAMD(GLuint group, GLuint counter, GLenum pname, GLvoid* data);
	void glGenPerfMonitorsAMD(GLsizei n, GLuint* monitors);
	void glDeletePerfMonitorsAMD(GLsizei n, GLuint* monitors);
	void glSelectPerfMonitorCountersAMD(GLuint monitor, GLboolean enable, GLuint group, GLint numCounters, GLuint* countersList);
	void glBeginPerfMonitorAMD(GLuint monitor);
	void glEndPerfMonitorAMD(GLuint monitor);
	void glGetPerfMonitorCounterDataAMD(GLuint monitor, GLenum pname, GLsizei dataSize, GLuint* data, GLint* bytesWritten);
	void glRenderbufferStorageMultisampleIMG(GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height);
	void glFramebufferTexture2DMultisampleIMG(GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level, GLsizei samples);
	void glDeleteFencesNV(GLsizei n, const GLuint* fences);
	void glGenFencesNV(GLsizei n, GLuint* fences);
	GLboolean glIsFenceNV(GLuint fence);
	GLboolean glTestFenceNV(GLuint fence);
	void glGetFenceivNV(GLuint fence, GLenum pname, GLint* params);
	void glFinishFenceNV(GLuint fence);
	void glSetFenceNV(GLuint fence, GLenum condition);
	void glCoverageMaskNV(GLboolean mask);
	void glCoverageOperationNV(GLenum operation);
	void glGetDriverControlsQCOM(GLint* num, GLsizei size, GLuint* driverControls);
	void glGetDriverControlStringQCOM(GLuint driverControl, GLsizei bufSize, GLsizei* length, GLchar* driverControlString);
	void glEnableDriverControlQCOM(GLuint driverControl);
	void glDisableDriverControlQCOM(GLuint driverControl);
	void glExtGetTexturesQCOM(GLuint* textures, GLint maxTextures, GLint* numTextures);
	void glExtGetBuffersQCOM(GLuint* buffers, GLint maxBuffers, GLint* numBuffers);
	void glExtGetRenderbuffersQCOM(GLuint* renderbuffers, GLint maxRenderbuffers, GLint* numRenderbuffers);
	void glExtGetFramebuffersQCOM(GLuint* framebuffers, GLint maxFramebuffers, GLint* numFramebuffers);
	void glExtGetTexLevelParameterivQCOM(GLuint texture, GLenum face, GLint level, GLenum pname, GLint* params);
	void glExtTexObjectStateOverrideiQCOM(GLenum target, GLenum pname, GLint param);
	void glExtGetTexSubImageQCOM(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, GLvoid* texels);
	void glExtGetBufferPointervQCOM(GLenum target, GLvoidptr* params);
	void glExtGetShadersQCOM(GLuint* shaders, GLint maxShaders, GLint* numShaders);
	void glExtGetProgramsQCOM(GLuint* programs, GLint maxPrograms, GLint* numPrograms);
	GLboolean glExtIsProgramBinaryQCOM(GLuint program);
	void glExtGetProgramBinarySourceQCOM(GLuint program, GLenum shadertype, GLchar* source, GLint* length);
	void glStartTilingQCOM(GLuint x, GLuint y, GLuint width, GLuint height, GLbitfield preserveMask);
	void glEndTilingQCOM(GLbitfield preserveMask);
	void glVertexAttribPointerData(GLuint indx, GLint size, GLenum type, GLboolean normalized, GLsizei stride, void* data, GLuint datalen);
	void glVertexAttribPointerOffset(GLuint indx, GLint size, GLenum type, GLboolean normalized, GLsizei stride, GLuint offset);
	void glDrawElementsOffset(GLenum mode, GLsizei count, GLenum type, GLuint offset);
	void glDrawElementsData(GLenum mode, GLsizei count, GLenum type, void* data, GLuint datalen);
	void glGetCompressedTextureFormats(int count, GLint* formats);
	void glShaderString(GLuint shader, const GLchar* string, GLsizei len);
	int glFinishRoundTrip();
	void glGenVertexArrays(GLsizei n, GLuint* arrays);
	void glBindVertexArray(GLuint array);
	void glDeleteVertexArrays(GLsizei n, const GLuint* arrays);
	GLboolean glIsVertexArray(GLuint array);
	void* glMapBufferRange(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access);
	GLboolean glUnmapBuffer(GLenum target);
	void glFlushMappedBufferRange(GLenum target, GLintptr offset, GLsizeiptr length);
	void glMapBufferRangeAEMU(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* mapped);
	void glUnmapBufferAEMU(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* guest_buffer, GLboolean* out_res);
	void glFlushMappedBufferRangeAEMU(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* guest_buffer);
	void glReadPixelsOffsetAEMU(GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLuint offset);
	void glCompressedTexImage2DOffsetAEMU(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLint border, GLsizei imageSize, GLuint offset);
	void glCompressedTexSubImage2DOffsetAEMU(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLsizei imageSize, GLuint offset);
	void glTexImage2DOffsetAEMU(GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLint border, GLenum format, GLenum type, GLuint offset);
	void glTexSubImage2DOffsetAEMU(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLenum type, GLuint offset);
	void glBindBufferRange(GLenum target, GLuint index, GLuint buffer, GLintptr offset, GLsizeiptr size);
	void glBindBufferBase(GLenum target, GLuint index, GLuint buffer);
	void glCopyBufferSubData(GLenum readtarget, GLenum writetarget, GLintptr readoffset, GLintptr writeoffset, GLsizeiptr size);
	void glClearBufferiv(GLenum buffer, GLint drawBuffer, const GLint* value);
	void glClearBufferuiv(GLenum buffer, GLint drawBuffer, const GLuint* value);
	void glClearBufferfv(GLenum buffer, GLint drawBuffer, const GLfloat* value);
	void glClearBufferfi(GLenum buffer, GLint drawBuffer, GLfloat depth, GLint stencil);
	void glGetBufferParameteri64v(GLenum target, GLenum value, GLint64* data);
	void glGetBufferPointerv(GLenum target, GLenum pname, GLvoid** params);
	void glUniformBlockBinding(GLuint program, GLuint uniformBlockIndex, GLuint uniformBlockBinding);
	GLuint glGetUniformBlockIndex(GLuint program, const GLchar* uniformBlockName);
	void glGetUniformIndices(GLuint program, GLsizei uniformCount, const GLchar** uniformNames, GLuint* uniformIndices);
	void glGetUniformIndicesAEMU(GLuint program, GLsizei uniformCount, const GLchar* packedUniformNames, GLsizei packedLen, GLuint* uniformIndices);
	void glGetActiveUniformBlockiv(GLuint program, GLuint uniformBlockIndex, GLenum pname, GLint* params);
	void glGetActiveUniformBlockName(GLuint program, GLuint uniformBlockIndex, GLsizei bufSize, GLsizei* length, GLchar* uniformBlockName);
	void glUniform1ui(GLint location, GLuint v0);
	void glUniform2ui(GLint location, GLuint v0, GLuint v1);
	void glUniform3ui(GLint location, GLuint v0, GLuint v1, GLuint v2);
	void glUniform4ui(GLint location, GLint v0, GLuint v1, GLuint v2, GLuint v3);
	void glUniform1uiv(GLint location, GLsizei count, const GLuint* value);
	void glUniform2uiv(GLint location, GLsizei count, const GLuint* value);
	void glUniform3uiv(GLint location, GLsizei count, const GLuint* value);
	void glUniform4uiv(GLint location, GLsizei count, const GLuint* value);
	void glUniformMatrix2x3fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glUniformMatrix3x2fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glUniformMatrix2x4fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glUniformMatrix4x2fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glUniformMatrix3x4fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glUniformMatrix4x3fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glGetUniformuiv(GLuint program, GLint location, GLuint* params);
	void glGetActiveUniformsiv(GLuint program, GLsizei uniformCount, const GLuint* uniformIndices, GLenum pname, GLint* params);
	void glVertexAttribI4i(GLuint index, GLint v0, GLint v1, GLint v2, GLint v3);
	void glVertexAttribI4ui(GLuint index, GLuint v0, GLuint v1, GLuint v2, GLuint v3);
	void glVertexAttribI4iv(GLuint index, const GLint* v);
	void glVertexAttribI4uiv(GLuint index, const GLuint* v);
	void glVertexAttribIPointer(GLuint index, GLint size, GLenum type, GLsizei stride, const GLvoid* pointer);
	void glVertexAttribIPointerOffsetAEMU(GLuint index, GLint size, GLenum type, GLsizei stride, GLuint offset);
	void glVertexAttribIPointerDataAEMU(GLuint index, GLint size, GLenum type, GLsizei stride, void* data, GLuint datalen);
	void glGetVertexAttribIiv(GLuint index, GLenum pname, GLint* params);
	void glGetVertexAttribIuiv(GLuint index, GLenum pname, GLuint* params);
	void glVertexAttribDivisor(GLuint index, GLuint divisor);
	void glDrawArraysInstanced(GLenum mode, GLint first, GLsizei count, GLsizei primcount);
	void glDrawElementsInstanced(GLenum mode, GLsizei count, GLenum type, const void* indices, GLsizei primcount);
	void glDrawElementsInstancedDataAEMU(GLenum mode, GLsizei count, GLenum type, const void* indices, GLsizei primcount, GLsizei datalen);
	void glDrawElementsInstancedOffsetAEMU(GLenum mode, GLsizei count, GLenum type, GLuint offset, GLsizei primcount);
	void glDrawRangeElements(GLenum mode, GLuint start, GLuint end, GLsizei count, GLenum type, const GLvoid* indices);
	void glDrawRangeElementsDataAEMU(GLenum mode, GLuint start, GLuint end, GLsizei count, GLenum type, const GLvoid* indices, GLsizei datalen);
	void glDrawRangeElementsOffsetAEMU(GLenum mode, GLuint start, GLuint end, GLsizei count, GLenum type, GLuint offset);
	GLsync glFenceSync(GLenum condition, GLbitfield flags);
	GLenum glClientWaitSync(GLsync wait_on, GLbitfield flags, GLuint64 timeout);
	void glWaitSync(GLsync wait_on, GLbitfield flags, GLuint64 timeout);
	void glDeleteSync(GLsync to_delete);
	GLboolean glIsSync(GLsync sync);
	void glGetSynciv(GLsync sync, GLenum pname, GLsizei bufSize, GLsizei* length, GLint* values);
	uint64_t glFenceSyncAEMU(GLenum condition, GLbitfield flags);
	GLenum glClientWaitSyncAEMU(uint64_t wait_on, GLbitfield flags, GLuint64 timeout);
	void glWaitSyncAEMU(uint64_t wait_on, GLbitfield flags, GLuint64 timeout);
	void glDeleteSyncAEMU(uint64_t to_delete);
	GLboolean glIsSyncAEMU(uint64_t sync);
	void glGetSyncivAEMU(uint64_t sync, GLenum pname, GLsizei bufSize, GLsizei* length, GLint* values);
	void glDrawBuffers(GLsizei n, const GLenum* bufs);
	void glReadBuffer(GLenum src);
	void glBlitFramebuffer(GLint srcX0, GLint srcY0, GLint srcX1, GLint srcY1, GLint dstX0, GLint dstY0, GLint dstX1, GLint dstY1, GLbitfield mask, GLenum filter);
	void glInvalidateFramebuffer(GLenum target, GLsizei numAttachments, const GLenum* attachments);
	void glInvalidateSubFramebuffer(GLenum target, GLsizei numAttachments, const GLenum* attachments, GLint x, GLint y, GLsizei width, GLsizei height);
	void glFramebufferTextureLayer(GLenum target, GLenum attachment, GLuint texture, GLint level, GLint layer);
	void glRenderbufferStorageMultisample(GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height);
	void glTexStorage2D(GLenum target, GLsizei levels, GLenum internalformat, GLsizei width, GLsizei height);
	void glGetInternalformativ(GLenum target, GLenum internalformat, GLenum pname, GLsizei bufSize, GLint* params);
	void glBeginTransformFeedback(GLenum primitiveMode);
	void glEndTransformFeedback();
	void glGenTransformFeedbacks(GLsizei n, GLuint* ids);
	void glDeleteTransformFeedbacks(GLsizei n, const GLuint* ids);
	void glBindTransformFeedback(GLenum target, GLuint id);
	void glPauseTransformFeedback();
	void glResumeTransformFeedback();
	GLboolean glIsTransformFeedback(GLuint id);
	void glTransformFeedbackVaryings(GLuint program, GLsizei count, const char** varyings, GLenum bufferMode);
	void glTransformFeedbackVaryingsAEMU(GLuint program, GLsizei count, const char* packedVaryings, GLuint packedVaryingsLen, GLenum bufferMode);
	void glGetTransformFeedbackVarying(GLuint program, GLuint index, GLsizei bufSize, GLsizei* length, GLsizei* size, GLenum* type, char* name);
	void glGenSamplers(GLsizei n, GLuint* samplers);
	void glDeleteSamplers(GLsizei n, const GLuint* samplers);
	void glBindSampler(GLuint unit, GLuint sampler);
	void glSamplerParameterf(GLuint sampler, GLenum pname, GLfloat param);
	void glSamplerParameteri(GLuint sampler, GLenum pname, GLint param);
	void glSamplerParameterfv(GLuint sampler, GLenum pname, const GLfloat* params);
	void glSamplerParameteriv(GLuint sampler, GLenum pname, const GLint* params);
	void glGetSamplerParameterfv(GLuint sampler, GLenum pname, GLfloat* params);
	void glGetSamplerParameteriv(GLuint sampler, GLenum pname, GLint* params);
	GLboolean glIsSampler(GLuint sampler);
	void glGenQueries(GLsizei n, GLuint* queries);
	void glDeleteQueries(GLsizei n, const GLuint* queries);
	void glBeginQuery(GLenum target, GLuint query);
	void glEndQuery(GLenum target);
	void glGetQueryiv(GLenum target, GLenum pname, GLint* params);
	void glGetQueryObjectuiv(GLuint query, GLenum pname, GLuint* params);
	GLboolean glIsQuery(GLuint query);
	void glProgramParameteri(GLuint program, GLenum pname, GLint value);
	void glProgramBinary(GLuint program, GLenum binaryFormat, const void* binary, GLsizei length);
	void glGetProgramBinary(GLuint program, GLsizei bufSize, GLsizei* length, GLenum* binaryFormat, void* binary);
	GLint glGetFragDataLocation(GLuint program, const char* name);
	void glGetInteger64v(GLenum pname, GLint64* data);
	void glGetIntegeri_v(GLenum target, GLuint index, GLint* data);
	void glGetInteger64i_v(GLenum target, GLuint index, GLint64* data);
	void glTexImage3D(GLenum target, GLint level, GLint internalFormat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLenum format, GLenum type, const GLvoid* data);
	void glTexImage3DOffsetAEMU(GLenum target, GLint level, GLint internalFormat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLenum format, GLenum type, GLuint offset);
	void glTexStorage3D(GLenum target, GLsizei levels, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth);
	void glTexSubImage3D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, const GLvoid* data);
	void glTexSubImage3DOffsetAEMU(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, GLuint offset);
	void glCompressedTexImage3D(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLsizei imageSize, const GLvoid* data);
	void glCompressedTexImage3DOffsetAEMU(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLsizei imageSize, GLuint offset);
	void glCompressedTexSubImage3D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLsizei imageSize, const GLvoid* data);
	void glCompressedTexSubImage3DOffsetAEMU(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLsizei imageSize, GLuint data);
	void glCopyTexSubImage3D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLint x, GLint y, GLsizei width, GLsizei height);
	const GLubyte* glGetStringi(GLenum name, GLuint index);
	void glGetBooleani_v(GLenum target, GLuint index, GLboolean* data);
	void glMemoryBarrier(GLbitfield barriers);
	void glMemoryBarrierByRegion(GLbitfield barriers);
	void glGenProgramPipelines(GLsizei n, GLuint* pipelines);
	void glDeleteProgramPipelines(GLsizei n, const GLuint* pipelines);
	void glBindProgramPipeline(GLuint pipeline);
	void glGetProgramPipelineiv(GLuint pipeline, GLenum pname, GLint* params);
	void glGetProgramPipelineInfoLog(GLuint pipeline, GLsizei bufSize, GLsizei* length, GLchar* infoLog);
	void glValidateProgramPipeline(GLuint pipeline);
	GLboolean glIsProgramPipeline(GLuint pipeline);
	void glUseProgramStages(GLuint pipeline, GLbitfield stages, GLuint program);
	void glActiveShaderProgram(GLuint pipeline, GLuint program);
	GLuint glCreateShaderProgramv(GLenum type, GLsizei count, const char** strings);
	GLuint glCreateShaderProgramvAEMU(GLenum type, GLsizei count, const char* packedStrings, GLuint packedLen);
	void glProgramUniform1f(GLuint program, GLint location, GLfloat v0);
	void glProgramUniform2f(GLuint program, GLint location, GLfloat v0, GLfloat v1);
	void glProgramUniform3f(GLuint program, GLint location, GLfloat v0, GLfloat v1, GLfloat v2);
	void glProgramUniform4f(GLuint program, GLint location, GLfloat v0, GLfloat v1, GLfloat v2, GLfloat v3);
	void glProgramUniform1i(GLuint program, GLint location, GLint v0);
	void glProgramUniform2i(GLuint program, GLint location, GLint v0, GLint v1);
	void glProgramUniform3i(GLuint program, GLint location, GLint v0, GLint v1, GLint v2);
	void glProgramUniform4i(GLuint program, GLint location, GLint v0, GLint v1, GLint v2, GLint v3);
	void glProgramUniform1ui(GLuint program, GLint location, GLuint v0);
	void glProgramUniform2ui(GLuint program, GLint location, GLint v0, GLuint v1);
	void glProgramUniform3ui(GLuint program, GLint location, GLint v0, GLint v1, GLuint v2);
	void glProgramUniform4ui(GLuint program, GLint location, GLint v0, GLint v1, GLint v2, GLuint v3);
	void glProgramUniform1fv(GLuint program, GLint location, GLsizei count, const GLfloat* value);
	void glProgramUniform2fv(GLuint program, GLint location, GLsizei count, const GLfloat* value);
	void glProgramUniform3fv(GLuint program, GLint location, GLsizei count, const GLfloat* value);
	void glProgramUniform4fv(GLuint program, GLint location, GLsizei count, const GLfloat* value);
	void glProgramUniform1iv(GLuint program, GLint location, GLsizei count, const GLint* value);
	void glProgramUniform2iv(GLuint program, GLint location, GLsizei count, const GLint* value);
	void glProgramUniform3iv(GLuint program, GLint location, GLsizei count, const GLint* value);
	void glProgramUniform4iv(GLuint program, GLint location, GLsizei count, const GLint* value);
	void glProgramUniform1uiv(GLuint program, GLint location, GLsizei count, const GLuint* value);
	void glProgramUniform2uiv(GLuint program, GLint location, GLsizei count, const GLuint* value);
	void glProgramUniform3uiv(GLuint program, GLint location, GLsizei count, const GLuint* value);
	void glProgramUniform4uiv(GLuint program, GLint location, GLsizei count, const GLuint* value);
	void glProgramUniformMatrix2fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glProgramUniformMatrix3fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glProgramUniformMatrix4fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glProgramUniformMatrix2x3fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glProgramUniformMatrix3x2fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glProgramUniformMatrix2x4fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glProgramUniformMatrix4x2fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glProgramUniformMatrix3x4fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glProgramUniformMatrix4x3fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value);
	void glGetProgramInterfaceiv(GLuint program, GLenum programInterface, GLenum pname, GLint* params);
	void glGetProgramResourceiv(GLuint program, GLenum programInterface, GLuint index, GLsizei propCount, const GLenum* props, GLsizei bufSize, GLsizei* length, GLint* params);
	GLuint glGetProgramResourceIndex(GLuint program, GLenum programInterface, const char* name);
	GLint glGetProgramResourceLocation(GLuint program, GLenum programInterface, const char* name);
	void glGetProgramResourceName(GLuint program, GLenum programInterface, GLuint index, GLsizei bufSize, GLsizei* length, char* name);
	void glBindImageTexture(GLuint unit, GLuint texture, GLint level, GLboolean layered, GLint layer, GLenum access, GLenum format);
	void glDispatchCompute(GLuint num_groups_x, GLuint num_groups_y, GLuint num_groups_z);
	void glDispatchComputeIndirect(GLintptr indirect);
	void glBindVertexBuffer(GLuint bindingindex, GLuint buffer, GLintptr offset, GLintptr stride);
	void glVertexAttribBinding(GLuint attribindex, GLuint bindingindex);
	void glVertexAttribFormat(GLuint attribindex, GLint size, GLenum type, GLboolean normalized, GLuint relativeoffset);
	void glVertexAttribIFormat(GLuint attribindex, GLint size, GLenum type, GLuint relativeoffset);
	void glVertexBindingDivisor(GLuint bindingindex, GLuint divisor);
	void glDrawArraysIndirect(GLenum mode, const void* indirect);
	void glDrawArraysIndirectDataAEMU(GLenum mode, const void* indirect, GLuint datalen);
	void glDrawArraysIndirectOffsetAEMU(GLenum mode, GLuint offset);
	void glDrawElementsIndirect(GLenum mode, GLenum type, const void* indirect);
	void glDrawElementsIndirectDataAEMU(GLenum mode, GLenum type, const void* indirect, GLuint datalen);
	void glDrawElementsIndirectOffsetAEMU(GLenum mode, GLenum type, GLuint offset);
	void glTexStorage2DMultisample(GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height, GLboolean fixedsamplelocations);
	void glSampleMaski(GLuint maskNumber, GLbitfield mask);
	void glGetMultisamplefv(GLenum pname, GLuint index, GLfloat* val);
	void glFramebufferParameteri(GLenum target, GLenum pname, GLint param);
	void glGetFramebufferParameteriv(GLenum target, GLenum pname, GLint* params);
	void glGetTexLevelParameterfv(GLenum target, GLint level, GLenum pname, GLfloat* params);
	void glGetTexLevelParameteriv(GLenum target, GLint level, GLenum pname, GLint* params);
	void glMapBufferRangeDMA(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, uint64_t paddr);
	void glUnmapBufferDMA(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, uint64_t paddr, GLboolean* out_res);
	uint64_t glMapBufferRangeDirect(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, uint64_t paddr);
	void glUnmapBufferDirect(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, uint64_t paddr, uint64_t guest_ptr, GLboolean* out_res);
	void glFlushMappedBufferRangeDirect(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access);
	GLenum glGetGraphicsResetStatusEXT();
	void glReadnPixelsEXT(GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLsizei bufSize, GLvoid* data);
	void glGetnUniformfvEXT(GLuint program, GLint location, GLsizei bufSize, GLfloat* params);
	void glGetnUniformivEXT(GLuint program, GLint location, GLsizei bufSize, GLint* params);
	void glDrawArraysNullAEMU(GLenum mode, GLint first, GLsizei count);
	void glDrawElementsNullAEMU(GLenum mode, GLsizei count, GLenum type, const GLvoid* indices);
	void glDrawElementsOffsetNullAEMU(GLenum mode, GLsizei count, GLenum type, GLuint offset);
	void glDrawElementsDataNullAEMU(GLenum mode, GLsizei count, GLenum type, void* data, GLuint datalen);
	void glUnmapBufferAsyncAEMU(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* guest_buffer, GLboolean* out_res);
	void glFlushMappedBufferRangeAEMU2(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* guest_buffer);
	GLboolean glBufferDataSyncAEMU(GLenum target, GLsizeiptr size, const GLvoid* data, GLenum usage);
	void glTexBufferOES(GLenum target, GLenum internalFormat, GLuint buffer);
	void glTexBufferRangeOES(GLenum target, GLenum internalFormat, GLuint buffer, GLintptr offset, GLsizeiptr size);
	void glTexBufferEXT(GLenum target, GLenum internalFormat, GLuint buffer);
	void glTexBufferRangeEXT(GLenum target, GLenum internalFormat, GLuint buffer, GLintptr offset, GLsizeiptr size);
	void glEnableiEXT(GLenum cap, GLuint index);
	void glDisableiEXT(GLenum cap, GLuint index);
	void glBlendEquationiEXT(GLuint index, GLenum mode);
	void glBlendEquationSeparateiEXT(GLuint index, GLenum modeRGB, GLenum modeAlpha);
	void glBlendFunciEXT(GLuint index, GLenum sfactor, GLenum dfactor);
	void glBlendFuncSeparateiEXT(GLuint index, GLenum srcRGB, GLenum dstRGB, GLenum srcAlpha, GLenum dstAlpha);
	void glColorMaskiEXT(GLuint index, GLboolean red, GLboolean green, GLboolean blue, GLboolean alpha);
	GLboolean glIsEnablediEXT(GLenum cap, GLuint index);
	void glBlendBarrier();
	void glCopyImageSubData(GLuint srcName, GLenum srcTarget, GLint srcLevel, GLint srcX, GLint srcY, GLint srcZ, GLuint dstName, GLenum dstTarget, GLint dstLevel, GLint dstX, GLint dstY, GLint dstZ, GLsizei srcWidth, GLsizei srcHeight, GLsizei srcDepth);
	void glObjectLabel(GLenum identifier, GLuint name, GLsizei length, const GLchar* label);
	void glGetObjectLabel(GLenum identifier, GLuint name, GLsizei bufSize, GLsizei* length, GLchar* label);
	void glObjectPtrLabel(const void* ptr, GLsizei length, const GLchar* label);
	void glGetObjectPtrLabel(const void* ptr, GLsizei bufSize, GLsizei* length, GLchar* label);
	void glEnablei(GLenum target, GLuint index);
	void glDisablei(GLenum target, GLuint index);
	void glBlendEquationi(GLuint buf_, GLenum mode);
	void glBlendEquationSeparatei(GLuint buf_, GLenum modeRGB, GLenum modeAlpha);
	void glBlendFunci(GLuint buf_, GLenum src, GLenum dst);
	void glBlendFuncSeparatei(GLuint buf_, GLenum srcRGB, GLenum dstRGB, GLenum srcAlpha, GLenum dstAlpha);
	void glColorMaski(GLuint index, GLboolean r, GLboolean g, GLboolean b, GLboolean a);
	GLboolean glIsEnabledi(GLenum target, GLuint index);
	void glDrawElementsBaseVertex(GLenum mode, GLsizei count, GLenum type, const void* indices, GLint basevertex);
	void glDrawRangeElementsBaseVertex(GLenum mode, GLuint start, GLuint end, GLsizei count, GLenum type, const void* indices, GLint basevertex);
	void glDrawElementsInstancedBaseVertex(GLenum mode, GLsizei count, GLenum type, const void* indices, GLsizei instancecount, GLint basevertex);
	void glFramebufferTexture(GLenum target, GLenum attachment, GLuint texture, GLint level);
	void glPrimitiveBoundingBox(GLfloat minGL_ENTRY, GLfloat minY, GLfloat minZ, GLfloat minW, GLfloat maxX, GLfloat maxY, GLfloat maxZ, GLfloat maxW);
	GLenum glGetGraphicsResetStatus();
	void glReadnPixels(GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLsizei bufSize, void* data);
	void glGetnUniformfv(GLuint program, GLint location, GLsizei bufSize, GLfloat* params);
	void glGetnUniformiv(GLuint program, GLint location, GLsizei bufSize, GLint* params);
	void glGetnUniformuiv(GLuint program, GLint location, GLsizei bufSize, GLuint* params);
	void glMinSampleShading(GLfloat value);
	void glPatchParameteri(GLenum pname, GLint value);
	void glTexParameterIiv(GLenum target, GLenum pname, const GLint* params);
	void glTexParameterIuiv(GLenum target, GLenum pname, const GLuint* params);
	void glGetTexParameterIiv(GLenum target, GLenum pname, GLint* params);
	void glGetTexParameterIuiv(GLenum target, GLenum pname, GLuint* params);
	void glSamplerParameterIiv(GLuint sampler, GLenum pname, const GLint* param);
	void glSamplerParameterIuiv(GLuint sampler, GLenum pname, const GLuint* param);
	void glGetSamplerParameterIiv(GLuint sampler, GLenum pname, GLint* params);
	void glGetSamplerParameterIuiv(GLuint sampler, GLenum pname, GLuint* params);
	void glTexBuffer(GLenum target, GLenum internalformat, GLuint buffer);
	void glTexBufferRange(GLenum target, GLenum internalformat, GLuint buffer, GLintptr offset, GLsizeiptr size);
	void glTexStorage3DMultisample(GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLboolean fixedsamplelocations);
	void glCopyImageSubDataEXT(GLuint srcName, GLenum srcTarget, GLint srcLevel, GLint srcX, GLint srcY, GLint srcZ, GLuint dstName, GLenum dstTarget, GLint dstLevel, GLint dstX, GLint dstY, GLint dstZ, GLsizei srcWidth, GLsizei srcHeight, GLsizei srcDepth);
	void glDrawElementsBaseVertexOffset(GLenum mode, GLsizei count, GLenum type, GLuint offset, GLint basevertex);
	void glBindFragDataLocationEXT(GLuint program, GLuint color, const GLchar* name);
	void glBindFragDataLocationIndexedEXT(GLuint program, GLuint colorNumber, GLuint index, const GLchar* name);
	GLint glGetFragDataIndexEXT(GLuint program, const GLchar* name);
	GLint glGetProgramResourceLocationIndexEXT(GLuint program, GLenum programInterface, const GLchar* name);
	void glFramebufferTexture2DMultisampleEXT(GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level, GLsizei samples);
	void glRenderbufferStorageMultisampleEXT(GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height);
	void glTexStorage2DEXT(GLenum target, GLsizei levels, GLenum internalformat, GLsizei width, GLsizei height);
	void glTexStorage3DEXT(GLenum target, GLsizei levels, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth);
	void* glMapBufferRangeEXT(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access);
	void glFlushMappedBufferRangeEXT(GLenum target, GLintptr offset, GLsizeiptr length);
	void glDebugMessageInsert(GLenum source, GLenum type, GLuint id, GLenum severity, GLsizei length, const GLchar* buffer);
	void glBlendBarrierKHR();
};

#ifndef GET_CONTEXT
static gl2_client_context_t::CONTEXT_ACCESSOR_TYPE *getCurrentContext = NULL;
void gl2_client_context_t::setContextAccessor(CONTEXT_ACCESSOR_TYPE *f) { getCurrentContext = f; }
#define GET_CONTEXT gl2_client_context_t * ctx = getCurrentContext()
#endif

void glActiveTexture(GLenum texture)
{
	GET_CONTEXT;
	ctx->glActiveTexture(ctx, texture);
}

void glAttachShader(GLuint program, GLuint shader)
{
	GET_CONTEXT;
	ctx->glAttachShader(ctx, program, shader);
}

void glBindAttribLocation(GLuint program, GLuint index, const GLchar* name)
{
	GET_CONTEXT;
	ctx->glBindAttribLocation(ctx, program, index, name);
}

void glBindBuffer(GLenum target, GLuint buffer)
{
	GET_CONTEXT;
	ctx->glBindBuffer(ctx, target, buffer);
}

void glBindFramebuffer(GLenum target, GLuint framebuffer)
{
	GET_CONTEXT;
	ctx->glBindFramebuffer(ctx, target, framebuffer);
}

void glBindRenderbuffer(GLenum target, GLuint renderbuffer)
{
	GET_CONTEXT;
	ctx->glBindRenderbuffer(ctx, target, renderbuffer);
}

void glBindTexture(GLenum target, GLuint texture)
{
	GET_CONTEXT;
	ctx->glBindTexture(ctx, target, texture);
}

void glBlendColor(GLclampf red, GLclampf green, GLclampf blue, GLclampf alpha)
{
	GET_CONTEXT;
	ctx->glBlendColor(ctx, red, green, blue, alpha);
}

void glBlendEquation(GLenum mode)
{
	GET_CONTEXT;
	ctx->glBlendEquation(ctx, mode);
}

void glBlendEquationSeparate(GLenum modeRGB, GLenum modeAlpha)
{
	GET_CONTEXT;
	ctx->glBlendEquationSeparate(ctx, modeRGB, modeAlpha);
}

void glBlendFunc(GLenum sfactor, GLenum dfactor)
{
	GET_CONTEXT;
	ctx->glBlendFunc(ctx, sfactor, dfactor);
}

void glBlendFuncSeparate(GLenum srcRGB, GLenum dstRGB, GLenum srcAlpha, GLenum dstAlpha)
{
	GET_CONTEXT;
	ctx->glBlendFuncSeparate(ctx, srcRGB, dstRGB, srcAlpha, dstAlpha);
}

void glBufferData(GLenum target, GLsizeiptr size, const GLvoid* data, GLenum usage)
{
	GET_CONTEXT;
	ctx->glBufferData(ctx, target, size, data, usage);
}

void glBufferSubData(GLenum target, GLintptr offset, GLsizeiptr size, const GLvoid* data)
{
	GET_CONTEXT;
	ctx->glBufferSubData(ctx, target, offset, size, data);
}

GLenum glCheckFramebufferStatus(GLenum target)
{
	GET_CONTEXT;
	return ctx->glCheckFramebufferStatus(ctx, target);
}

void glClear(GLbitfield mask)
{
	GET_CONTEXT;
	ctx->glClear(ctx, mask);
}

void glClearColor(GLclampf red, GLclampf green, GLclampf blue, GLclampf alpha)
{
	GET_CONTEXT;
	ctx->glClearColor(ctx, red, green, blue, alpha);
}

void glClearDepthf(GLclampf depth)
{
	GET_CONTEXT;
	ctx->glClearDepthf(ctx, depth);
}

void glClearStencil(GLint s)
{
	GET_CONTEXT;
	ctx->glClearStencil(ctx, s);
}

void glColorMask(GLboolean red, GLboolean green, GLboolean blue, GLboolean alpha)
{
	GET_CONTEXT;
	ctx->glColorMask(ctx, red, green, blue, alpha);
}

void glCompileShader(GLuint shader)
{
	GET_CONTEXT;
	ctx->glCompileShader(ctx, shader);
}

void glCompressedTexImage2D(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLint border, GLsizei imageSize, const GLvoid* data)
{
	GET_CONTEXT;
	 if(imageSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glCompressedTexImage2D(ctx, target, level, internalformat, width, height, border, imageSize, data);
}

void glCompressedTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLsizei imageSize, const GLvoid* data)
{
	GET_CONTEXT;
	 if(imageSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glCompressedTexSubImage2D(ctx, target, level, xoffset, yoffset, width, height, format, imageSize, data);
}

void glCopyTexImage2D(GLenum target, GLint level, GLenum internalformat, GLint x, GLint y, GLsizei width, GLsizei height, GLint border)
{
	GET_CONTEXT;
	ctx->glCopyTexImage2D(ctx, target, level, internalformat, x, y, width, height, border);
}

void glCopyTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint x, GLint y, GLsizei width, GLsizei height)
{
	GET_CONTEXT;
	ctx->glCopyTexSubImage2D(ctx, target, level, xoffset, yoffset, x, y, width, height);
}

GLuint glCreateProgram()
{
	GET_CONTEXT;
	return ctx->glCreateProgram(ctx);
}

GLuint glCreateShader(GLenum type)
{
	GET_CONTEXT;
	return ctx->glCreateShader(ctx, type);
}

void glCullFace(GLenum mode)
{
	GET_CONTEXT;
	ctx->glCullFace(ctx, mode);
}

void glDeleteBuffers(GLsizei n, const GLuint* buffers)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glDeleteBuffers(ctx, n, buffers);
}

void glDeleteFramebuffers(GLsizei n, const GLuint* framebuffers)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glDeleteFramebuffers(ctx, n, framebuffers);
}

void glDeleteProgram(GLuint program)
{
	GET_CONTEXT;
	ctx->glDeleteProgram(ctx, program);
}

void glDeleteRenderbuffers(GLsizei n, const GLuint* renderbuffers)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glDeleteRenderbuffers(ctx, n, renderbuffers);
}

void glDeleteShader(GLuint shader)
{
	GET_CONTEXT;
	ctx->glDeleteShader(ctx, shader);
}

void glDeleteTextures(GLsizei n, const GLuint* textures)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glDeleteTextures(ctx, n, textures);
}

void glDepthFunc(GLenum func)
{
	GET_CONTEXT;
	ctx->glDepthFunc(ctx, func);
}

void glDepthMask(GLboolean flag)
{
	GET_CONTEXT;
	ctx->glDepthMask(ctx, flag);
}

void glDepthRangef(GLclampf zNear, GLclampf zFar)
{
	GET_CONTEXT;
	ctx->glDepthRangef(ctx, zNear, zFar);
}

void glDetachShader(GLuint program, GLuint shader)
{
	GET_CONTEXT;
	ctx->glDetachShader(ctx, program, shader);
}

void glDisable(GLenum cap)
{
	GET_CONTEXT;
	ctx->glDisable(ctx, cap);
}

void glDisableVertexAttribArray(GLuint index)
{
	GET_CONTEXT;
	ctx->glDisableVertexAttribArray(ctx, index);
}

void glDrawArrays(GLenum mode, GLint first, GLsizei count)
{
	GET_CONTEXT;
	ctx->glDrawArrays(ctx, mode, first, count);
}

void glDrawElements(GLenum mode, GLsizei count, GLenum type, const GLvoid* indices)
{
	GET_CONTEXT;
	ctx->glDrawElements(ctx, mode, count, type, indices);
}

void glEnable(GLenum cap)
{
	GET_CONTEXT;
	ctx->glEnable(ctx, cap);
}

void glEnableVertexAttribArray(GLuint index)
{
	GET_CONTEXT;
	ctx->glEnableVertexAttribArray(ctx, index);
}

void glFinish()
{
	GET_CONTEXT;
	ctx->glFinish(ctx);
}

void glFlush()
{
	GET_CONTEXT;
	ctx->glFlush(ctx);
}

void glFramebufferRenderbuffer(GLenum target, GLenum attachment, GLenum renderbuffertarget, GLuint renderbuffer)
{
	GET_CONTEXT;
	ctx->glFramebufferRenderbuffer(ctx, target, attachment, renderbuffertarget, renderbuffer);
}

void glFramebufferTexture2D(GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level)
{
	GET_CONTEXT;
	ctx->glFramebufferTexture2D(ctx, target, attachment, textarget, texture, level);
}

void glFrontFace(GLenum mode)
{
	GET_CONTEXT;
	ctx->glFrontFace(ctx, mode);
}

void glGenBuffers(GLsizei n, GLuint* buffers)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGenBuffers(ctx, n, buffers);
}

void glGenerateMipmap(GLenum target)
{
	GET_CONTEXT;
	ctx->glGenerateMipmap(ctx, target);
}

void glGenFramebuffers(GLsizei n, GLuint* framebuffers)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGenFramebuffers(ctx, n, framebuffers);
}

void glGenRenderbuffers(GLsizei n, GLuint* renderbuffers)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGenRenderbuffers(ctx, n, renderbuffers);
}

void glGenTextures(GLsizei n, GLuint* textures)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGenTextures(ctx, n, textures);
}

void glGetActiveAttrib(GLuint program, GLuint index, GLsizei bufsize, GLsizei* length, GLint* size, GLenum* type, GLchar* name)
{
	GET_CONTEXT;
	 if(bufsize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetActiveAttrib(ctx, program, index, bufsize, length, size, type, name);
}

void glGetActiveUniform(GLuint program, GLuint index, GLsizei bufsize, GLsizei* length, GLint* size, GLenum* type, GLchar* name)
{
	GET_CONTEXT;
	 if(bufsize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetActiveUniform(ctx, program, index, bufsize, length, size, type, name);
}

void glGetAttachedShaders(GLuint program, GLsizei maxcount, GLsizei* count, GLuint* shaders)
{
	GET_CONTEXT;
	ctx->glGetAttachedShaders(ctx, program, maxcount, count, shaders);
}

int glGetAttribLocation(GLuint program, const GLchar* name)
{
	GET_CONTEXT;
	return ctx->glGetAttribLocation(ctx, program, name);
}

void glGetBooleanv(GLenum pname, GLboolean* params)
{
	GET_CONTEXT;
	ctx->glGetBooleanv(ctx, pname, params);
}

void glGetBufferParameteriv(GLenum target, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetBufferParameteriv(ctx, target, pname, params);
}

GLenum glGetError()
{
	GET_CONTEXT;
	return ctx->glGetError(ctx);
}

void glGetFloatv(GLenum pname, GLfloat* params)
{
	GET_CONTEXT;
	ctx->glGetFloatv(ctx, pname, params);
}

void glGetFramebufferAttachmentParameteriv(GLenum target, GLenum attachment, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetFramebufferAttachmentParameteriv(ctx, target, attachment, pname, params);
}

void glGetIntegerv(GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetIntegerv(ctx, pname, params);
}

void glGetProgramiv(GLuint program, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetProgramiv(ctx, program, pname, params);
}

void glGetProgramInfoLog(GLuint program, GLsizei bufsize, GLsizei* length, GLchar* infolog)
{
	GET_CONTEXT;
	 if(bufsize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetProgramInfoLog(ctx, program, bufsize, length, infolog);
}

void glGetRenderbufferParameteriv(GLenum target, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetRenderbufferParameteriv(ctx, target, pname, params);
}

void glGetShaderiv(GLuint shader, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetShaderiv(ctx, shader, pname, params);
}

void glGetShaderInfoLog(GLuint shader, GLsizei bufsize, GLsizei* length, GLchar* infolog)
{
	GET_CONTEXT;
	 if(bufsize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetShaderInfoLog(ctx, shader, bufsize, length, infolog);
}

void glGetShaderPrecisionFormat(GLenum shadertype, GLenum precisiontype, GLint* range, GLint* precision)
{
	GET_CONTEXT;
	ctx->glGetShaderPrecisionFormat(ctx, shadertype, precisiontype, range, precision);
}

void glGetShaderSource(GLuint shader, GLsizei bufsize, GLsizei* length, GLchar* source)
{
	GET_CONTEXT;
	 if(bufsize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetShaderSource(ctx, shader, bufsize, length, source);
}

const GLubyte* glGetString(GLenum name)
{
	GET_CONTEXT;
	return ctx->glGetString(ctx, name);
}

void glGetTexParameterfv(GLenum target, GLenum pname, GLfloat* params)
{
	GET_CONTEXT;
	ctx->glGetTexParameterfv(ctx, target, pname, params);
}

void glGetTexParameteriv(GLenum target, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetTexParameteriv(ctx, target, pname, params);
}

void glGetUniformfv(GLuint program, GLint location, GLfloat* params)
{
	GET_CONTEXT;
	ctx->glGetUniformfv(ctx, program, location, params);
}

void glGetUniformiv(GLuint program, GLint location, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetUniformiv(ctx, program, location, params);
}

int glGetUniformLocation(GLuint program, const GLchar* name)
{
	GET_CONTEXT;
	return ctx->glGetUniformLocation(ctx, program, name);
}

void glGetVertexAttribfv(GLuint index, GLenum pname, GLfloat* params)
{
	GET_CONTEXT;
	ctx->glGetVertexAttribfv(ctx, index, pname, params);
}

void glGetVertexAttribiv(GLuint index, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetVertexAttribiv(ctx, index, pname, params);
}

void glGetVertexAttribPointerv(GLuint index, GLenum pname, GLvoid** pointer)
{
	GET_CONTEXT;
	ctx->glGetVertexAttribPointerv(ctx, index, pname, pointer);
}

void glHint(GLenum target, GLenum mode)
{
	GET_CONTEXT;
	ctx->glHint(ctx, target, mode);
}

GLboolean glIsBuffer(GLuint buffer)
{
	GET_CONTEXT;
	return ctx->glIsBuffer(ctx, buffer);
}

GLboolean glIsEnabled(GLenum cap)
{
	GET_CONTEXT;
	return ctx->glIsEnabled(ctx, cap);
}

GLboolean glIsFramebuffer(GLuint framebuffer)
{
	GET_CONTEXT;
	return ctx->glIsFramebuffer(ctx, framebuffer);
}

GLboolean glIsProgram(GLuint program)
{
	GET_CONTEXT;
	return ctx->glIsProgram(ctx, program);
}

GLboolean glIsRenderbuffer(GLuint renderbuffer)
{
	GET_CONTEXT;
	return ctx->glIsRenderbuffer(ctx, renderbuffer);
}

GLboolean glIsShader(GLuint shader)
{
	GET_CONTEXT;
	return ctx->glIsShader(ctx, shader);
}

GLboolean glIsTexture(GLuint texture)
{
	GET_CONTEXT;
	return ctx->glIsTexture(ctx, texture);
}

void glLineWidth(GLfloat width)
{
	GET_CONTEXT;
	ctx->glLineWidth(ctx, width);
}

void glLinkProgram(GLuint program)
{
	GET_CONTEXT;
	ctx->glLinkProgram(ctx, program);
}

void glPixelStorei(GLenum pname, GLint param)
{
	GET_CONTEXT;
	ctx->glPixelStorei(ctx, pname, param);
}

void glPolygonOffset(GLfloat factor, GLfloat units)
{
	GET_CONTEXT;
	ctx->glPolygonOffset(ctx, factor, units);
}

void glReadPixels(GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLvoid* pixels)
{
	GET_CONTEXT;
	ctx->glReadPixels(ctx, x, y, width, height, format, type, pixels);
}

void glReleaseShaderCompiler()
{
	GET_CONTEXT;
	ctx->glReleaseShaderCompiler(ctx);
}

void glRenderbufferStorage(GLenum target, GLenum internalformat, GLsizei width, GLsizei height)
{
	GET_CONTEXT;
	ctx->glRenderbufferStorage(ctx, target, internalformat, width, height);
}

void glSampleCoverage(GLclampf value, GLboolean invert)
{
	GET_CONTEXT;
	ctx->glSampleCoverage(ctx, value, invert);
}

void glScissor(GLint x, GLint y, GLsizei width, GLsizei height)
{
	GET_CONTEXT;
	ctx->glScissor(ctx, x, y, width, height);
}

void glShaderBinary(GLsizei n, const GLuint* shaders, GLenum binaryformat, const GLvoid* binary, GLsizei length)
{
	GET_CONTEXT;
	ctx->glShaderBinary(ctx, n, shaders, binaryformat, binary, length);
}

void glShaderSource(GLuint shader, GLsizei count, const GLchar* const* string, const GLint* length)
{
	GET_CONTEXT;
	ctx->glShaderSource(ctx, shader, count, string, length);
}

void glStencilFunc(GLenum func, GLint ref, GLuint mask)
{
	GET_CONTEXT;
	ctx->glStencilFunc(ctx, func, ref, mask);
}

void glStencilFuncSeparate(GLenum face, GLenum func, GLint ref, GLuint mask)
{
	GET_CONTEXT;
	ctx->glStencilFuncSeparate(ctx, face, func, ref, mask);
}

void glStencilMask(GLuint mask)
{
	GET_CONTEXT;
	ctx->glStencilMask(ctx, mask);
}

void glStencilMaskSeparate(GLenum face, GLuint mask)
{
	GET_CONTEXT;
	ctx->glStencilMaskSeparate(ctx, face, mask);
}

void glStencilOp(GLenum fail, GLenum zfail, GLenum zpass)
{
	GET_CONTEXT;
	ctx->glStencilOp(ctx, fail, zfail, zpass);
}

void glStencilOpSeparate(GLenum face, GLenum fail, GLenum zfail, GLenum zpass)
{
	GET_CONTEXT;
	ctx->glStencilOpSeparate(ctx, face, fail, zfail, zpass);
}

void glTexImage2D(GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLint border, GLenum format, GLenum type, const GLvoid* pixels)
{
	GET_CONTEXT;
	ctx->glTexImage2D(ctx, target, level, internalformat, width, height, border, format, type, pixels);
}

void glTexParameterf(GLenum target, GLenum pname, GLfloat param)
{
	GET_CONTEXT;
	ctx->glTexParameterf(ctx, target, pname, param);
}

void glTexParameterfv(GLenum target, GLenum pname, const GLfloat* params)
{
	GET_CONTEXT;
	ctx->glTexParameterfv(ctx, target, pname, params);
}

void glTexParameteri(GLenum target, GLenum pname, GLint param)
{
	GET_CONTEXT;
	ctx->glTexParameteri(ctx, target, pname, param);
}

void glTexParameteriv(GLenum target, GLenum pname, const GLint* params)
{
	GET_CONTEXT;
	ctx->glTexParameteriv(ctx, target, pname, params);
}

void glTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLenum type, const GLvoid* pixels)
{
	GET_CONTEXT;
	ctx->glTexSubImage2D(ctx, target, level, xoffset, yoffset, width, height, format, type, pixels);
}

void glUniform1f(GLint location, GLfloat x)
{
	GET_CONTEXT;
	ctx->glUniform1f(ctx, location, x);
}

void glUniform1fv(GLint location, GLsizei count, const GLfloat* v)
{
	GET_CONTEXT;
	ctx->glUniform1fv(ctx, location, count, v);
}

void glUniform1i(GLint location, GLint x)
{
	GET_CONTEXT;
	ctx->glUniform1i(ctx, location, x);
}

void glUniform1iv(GLint location, GLsizei count, const GLint* v)
{
	GET_CONTEXT;
	ctx->glUniform1iv(ctx, location, count, v);
}

void glUniform2f(GLint location, GLfloat x, GLfloat y)
{
	GET_CONTEXT;
	ctx->glUniform2f(ctx, location, x, y);
}

void glUniform2fv(GLint location, GLsizei count, const GLfloat* v)
{
	GET_CONTEXT;
	ctx->glUniform2fv(ctx, location, count, v);
}

void glUniform2i(GLint location, GLint x, GLint y)
{
	GET_CONTEXT;
	ctx->glUniform2i(ctx, location, x, y);
}

void glUniform2iv(GLint location, GLsizei count, const GLint* v)
{
	GET_CONTEXT;
	ctx->glUniform2iv(ctx, location, count, v);
}

void glUniform3f(GLint location, GLfloat x, GLfloat y, GLfloat z)
{
	GET_CONTEXT;
	ctx->glUniform3f(ctx, location, x, y, z);
}

void glUniform3fv(GLint location, GLsizei count, const GLfloat* v)
{
	GET_CONTEXT;
	ctx->glUniform3fv(ctx, location, count, v);
}

void glUniform3i(GLint location, GLint x, GLint y, GLint z)
{
	GET_CONTEXT;
	ctx->glUniform3i(ctx, location, x, y, z);
}

void glUniform3iv(GLint location, GLsizei count, const GLint* v)
{
	GET_CONTEXT;
	ctx->glUniform3iv(ctx, location, count, v);
}

void glUniform4f(GLint location, GLfloat x, GLfloat y, GLfloat z, GLfloat w)
{
	GET_CONTEXT;
	ctx->glUniform4f(ctx, location, x, y, z, w);
}

void glUniform4fv(GLint location, GLsizei count, const GLfloat* v)
{
	GET_CONTEXT;
	ctx->glUniform4fv(ctx, location, count, v);
}

void glUniform4i(GLint location, GLint x, GLint y, GLint z, GLint w)
{
	GET_CONTEXT;
	ctx->glUniform4i(ctx, location, x, y, z, w);
}

void glUniform4iv(GLint location, GLsizei count, const GLint* v)
{
	GET_CONTEXT;
	ctx->glUniform4iv(ctx, location, count, v);
}

void glUniformMatrix2fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glUniformMatrix2fv(ctx, location, count, transpose, value);
}

void glUniformMatrix3fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glUniformMatrix3fv(ctx, location, count, transpose, value);
}

void glUniformMatrix4fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glUniformMatrix4fv(ctx, location, count, transpose, value);
}

void glUseProgram(GLuint program)
{
	GET_CONTEXT;
	ctx->glUseProgram(ctx, program);
}

void glValidateProgram(GLuint program)
{
	GET_CONTEXT;
	ctx->glValidateProgram(ctx, program);
}

void glVertexAttrib1f(GLuint indx, GLfloat x)
{
	GET_CONTEXT;
	ctx->glVertexAttrib1f(ctx, indx, x);
}

void glVertexAttrib1fv(GLuint indx, const GLfloat* values)
{
	GET_CONTEXT;
	ctx->glVertexAttrib1fv(ctx, indx, values);
}

void glVertexAttrib2f(GLuint indx, GLfloat x, GLfloat y)
{
	GET_CONTEXT;
	ctx->glVertexAttrib2f(ctx, indx, x, y);
}

void glVertexAttrib2fv(GLuint indx, const GLfloat* values)
{
	GET_CONTEXT;
	ctx->glVertexAttrib2fv(ctx, indx, values);
}

void glVertexAttrib3f(GLuint indx, GLfloat x, GLfloat y, GLfloat z)
{
	GET_CONTEXT;
	ctx->glVertexAttrib3f(ctx, indx, x, y, z);
}

void glVertexAttrib3fv(GLuint indx, const GLfloat* values)
{
	GET_CONTEXT;
	ctx->glVertexAttrib3fv(ctx, indx, values);
}

void glVertexAttrib4f(GLuint indx, GLfloat x, GLfloat y, GLfloat z, GLfloat w)
{
	GET_CONTEXT;
	ctx->glVertexAttrib4f(ctx, indx, x, y, z, w);
}

void glVertexAttrib4fv(GLuint indx, const GLfloat* values)
{
	GET_CONTEXT;
	ctx->glVertexAttrib4fv(ctx, indx, values);
}

void glVertexAttribPointer(GLuint indx, GLint size, GLenum type, GLboolean normalized, GLsizei stride, const GLvoid* ptr)
{
	GET_CONTEXT;
	ctx->glVertexAttribPointer(ctx, indx, size, type, normalized, stride, ptr);
}

void glViewport(GLint x, GLint y, GLsizei width, GLsizei height)
{
	GET_CONTEXT;
	ctx->glViewport(ctx, x, y, width, height);
}

void glEGLImageTargetTexture2DOES(GLenum target, GLeglImageOES image)
{
	GET_CONTEXT;
	ctx->glEGLImageTargetTexture2DOES(ctx, target, image);
}

void glEGLImageTargetRenderbufferStorageOES(GLenum target, GLeglImageOES image)
{
	GET_CONTEXT;
	ctx->glEGLImageTargetRenderbufferStorageOES(ctx, target, image);
}

void glGetProgramBinaryOES(GLuint program, GLsizei bufSize, GLsizei* length, GLenum* binaryFormat, GLvoid* binary)
{
	GET_CONTEXT;
	 if(bufSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetProgramBinaryOES(ctx, program, bufSize, length, binaryFormat, binary);
}

void glProgramBinaryOES(GLuint program, GLenum binaryFormat, const GLvoid* binary, GLint length)
{
	GET_CONTEXT;
	ctx->glProgramBinaryOES(ctx, program, binaryFormat, binary, length);
}

void* glMapBufferOES(GLenum target, GLenum access)
{
	GET_CONTEXT;
	return ctx->glMapBufferOES(ctx, target, access);
}

GLboolean glUnmapBufferOES(GLenum target)
{
	GET_CONTEXT;
	return ctx->glUnmapBufferOES(ctx, target);
}

void glTexImage3DOES(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLenum format, GLenum type, const GLvoid* pixels)
{
	GET_CONTEXT;
	ctx->glTexImage3DOES(ctx, target, level, internalformat, width, height, depth, border, format, type, pixels);
}

void glTexSubImage3DOES(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, const GLvoid* pixels)
{
	GET_CONTEXT;
	ctx->glTexSubImage3DOES(ctx, target, level, xoffset, yoffset, zoffset, width, height, depth, format, type, pixels);
}

void glCopyTexSubImage3DOES(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLint x, GLint y, GLsizei width, GLsizei height)
{
	GET_CONTEXT;
	ctx->glCopyTexSubImage3DOES(ctx, target, level, xoffset, yoffset, zoffset, x, y, width, height);
}

void glCompressedTexImage3DOES(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLsizei imageSize, const GLvoid* data)
{
	GET_CONTEXT;
	 if(imageSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glCompressedTexImage3DOES(ctx, target, level, internalformat, width, height, depth, border, imageSize, data);
}

void glCompressedTexSubImage3DOES(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLsizei imageSize, const GLvoid* data)
{
	GET_CONTEXT;
	 if(imageSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glCompressedTexSubImage3DOES(ctx, target, level, xoffset, yoffset, zoffset, width, height, depth, format, imageSize, data);
}

void glFramebufferTexture3DOES(GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level, GLint zoffset)
{
	GET_CONTEXT;
	ctx->glFramebufferTexture3DOES(ctx, target, attachment, textarget, texture, level, zoffset);
}

void glBindVertexArrayOES(GLuint array)
{
	GET_CONTEXT;
	ctx->glBindVertexArrayOES(ctx, array);
}

void glDeleteVertexArraysOES(GLsizei n, const GLuint* arrays)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glDeleteVertexArraysOES(ctx, n, arrays);
}

void glGenVertexArraysOES(GLsizei n, GLuint* arrays)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGenVertexArraysOES(ctx, n, arrays);
}

GLboolean glIsVertexArrayOES(GLuint array)
{
	GET_CONTEXT;
	return ctx->glIsVertexArrayOES(ctx, array);
}

void glDiscardFramebufferEXT(GLenum target, GLsizei numAttachments, const GLenum* attachments)
{
	GET_CONTEXT;
	ctx->glDiscardFramebufferEXT(ctx, target, numAttachments, attachments);
}

void glMultiDrawArraysEXT(GLenum mode, const GLint* first, const GLsizei* count, GLsizei primcount)
{
	GET_CONTEXT;
	ctx->glMultiDrawArraysEXT(ctx, mode, first, count, primcount);
}

void glMultiDrawElementsEXT(GLenum mode, const GLsizei* count, GLenum type, const GLvoid* const* indices, GLsizei primcount)
{
	GET_CONTEXT;
	ctx->glMultiDrawElementsEXT(ctx, mode, count, type, indices, primcount);
}

void glGetPerfMonitorGroupsAMD(GLint* numGroups, GLsizei groupsSize, GLuint* groups)
{
	GET_CONTEXT;
	ctx->glGetPerfMonitorGroupsAMD(ctx, numGroups, groupsSize, groups);
}

void glGetPerfMonitorCountersAMD(GLuint group, GLint* numCounters, GLint* maxActiveCounters, GLsizei counterSize, GLuint* counters)
{
	GET_CONTEXT;
	ctx->glGetPerfMonitorCountersAMD(ctx, group, numCounters, maxActiveCounters, counterSize, counters);
}

void glGetPerfMonitorGroupStringAMD(GLuint group, GLsizei bufSize, GLsizei* length, GLchar* groupString)
{
	GET_CONTEXT;
	ctx->glGetPerfMonitorGroupStringAMD(ctx, group, bufSize, length, groupString);
}

void glGetPerfMonitorCounterStringAMD(GLuint group, GLuint counter, GLsizei bufSize, GLsizei* length, GLchar* counterString)
{
	GET_CONTEXT;
	ctx->glGetPerfMonitorCounterStringAMD(ctx, group, counter, bufSize, length, counterString);
}

void glGetPerfMonitorCounterInfoAMD(GLuint group, GLuint counter, GLenum pname, GLvoid* data)
{
	GET_CONTEXT;
	ctx->glGetPerfMonitorCounterInfoAMD(ctx, group, counter, pname, data);
}

void glGenPerfMonitorsAMD(GLsizei n, GLuint* monitors)
{
	GET_CONTEXT;
	ctx->glGenPerfMonitorsAMD(ctx, n, monitors);
}

void glDeletePerfMonitorsAMD(GLsizei n, GLuint* monitors)
{
	GET_CONTEXT;
	ctx->glDeletePerfMonitorsAMD(ctx, n, monitors);
}

void glSelectPerfMonitorCountersAMD(GLuint monitor, GLboolean enable, GLuint group, GLint numCounters, GLuint* countersList)
{
	GET_CONTEXT;
	ctx->glSelectPerfMonitorCountersAMD(ctx, monitor, enable, group, numCounters, countersList);
}

void glBeginPerfMonitorAMD(GLuint monitor)
{
	GET_CONTEXT;
	ctx->glBeginPerfMonitorAMD(ctx, monitor);
}

void glEndPerfMonitorAMD(GLuint monitor)
{
	GET_CONTEXT;
	ctx->glEndPerfMonitorAMD(ctx, monitor);
}

void glGetPerfMonitorCounterDataAMD(GLuint monitor, GLenum pname, GLsizei dataSize, GLuint* data, GLint* bytesWritten)
{
	GET_CONTEXT;
	ctx->glGetPerfMonitorCounterDataAMD(ctx, monitor, pname, dataSize, data, bytesWritten);
}

void glRenderbufferStorageMultisampleIMG(GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height)
{
	GET_CONTEXT;
	ctx->glRenderbufferStorageMultisampleIMG(ctx, target, samples, internalformat, width, height);
}

void glFramebufferTexture2DMultisampleIMG(GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level, GLsizei samples)
{
	GET_CONTEXT;
	ctx->glFramebufferTexture2DMultisampleIMG(ctx, target, attachment, textarget, texture, level, samples);
}

void glDeleteFencesNV(GLsizei n, const GLuint* fences)
{
	GET_CONTEXT;
	ctx->glDeleteFencesNV(ctx, n, fences);
}

void glGenFencesNV(GLsizei n, GLuint* fences)
{
	GET_CONTEXT;
	ctx->glGenFencesNV(ctx, n, fences);
}

GLboolean glIsFenceNV(GLuint fence)
{
	GET_CONTEXT;
	return ctx->glIsFenceNV(ctx, fence);
}

GLboolean glTestFenceNV(GLuint fence)
{
	GET_CONTEXT;
	return ctx->glTestFenceNV(ctx, fence);
}

void glGetFenceivNV(GLuint fence, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetFenceivNV(ctx, fence, pname, params);
}

void glFinishFenceNV(GLuint fence)
{
	GET_CONTEXT;
	ctx->glFinishFenceNV(ctx, fence);
}

void glSetFenceNV(GLuint fence, GLenum condition)
{
	GET_CONTEXT;
	ctx->glSetFenceNV(ctx, fence, condition);
}

void glCoverageMaskNV(GLboolean mask)
{
	GET_CONTEXT;
	ctx->glCoverageMaskNV(ctx, mask);
}

void glCoverageOperationNV(GLenum operation)
{
	GET_CONTEXT;
	ctx->glCoverageOperationNV(ctx, operation);
}

void glGetDriverControlsQCOM(GLint* num, GLsizei size, GLuint* driverControls)
{
	GET_CONTEXT;
	ctx->glGetDriverControlsQCOM(ctx, num, size, driverControls);
}

void glGetDriverControlStringQCOM(GLuint driverControl, GLsizei bufSize, GLsizei* length, GLchar* driverControlString)
{
	GET_CONTEXT;
	ctx->glGetDriverControlStringQCOM(ctx, driverControl, bufSize, length, driverControlString);
}

void glEnableDriverControlQCOM(GLuint driverControl)
{
	GET_CONTEXT;
	ctx->glEnableDriverControlQCOM(ctx, driverControl);
}

void glDisableDriverControlQCOM(GLuint driverControl)
{
	GET_CONTEXT;
	ctx->glDisableDriverControlQCOM(ctx, driverControl);
}

void glExtGetTexturesQCOM(GLuint* textures, GLint maxTextures, GLint* numTextures)
{
	GET_CONTEXT;
	ctx->glExtGetTexturesQCOM(ctx, textures, maxTextures, numTextures);
}

void glExtGetBuffersQCOM(GLuint* buffers, GLint maxBuffers, GLint* numBuffers)
{
	GET_CONTEXT;
	ctx->glExtGetBuffersQCOM(ctx, buffers, maxBuffers, numBuffers);
}

void glExtGetRenderbuffersQCOM(GLuint* renderbuffers, GLint maxRenderbuffers, GLint* numRenderbuffers)
{
	GET_CONTEXT;
	ctx->glExtGetRenderbuffersQCOM(ctx, renderbuffers, maxRenderbuffers, numRenderbuffers);
}

void glExtGetFramebuffersQCOM(GLuint* framebuffers, GLint maxFramebuffers, GLint* numFramebuffers)
{
	GET_CONTEXT;
	ctx->glExtGetFramebuffersQCOM(ctx, framebuffers, maxFramebuffers, numFramebuffers);
}

void glExtGetTexLevelParameterivQCOM(GLuint texture, GLenum face, GLint level, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glExtGetTexLevelParameterivQCOM(ctx, texture, face, level, pname, params);
}

void glExtTexObjectStateOverrideiQCOM(GLenum target, GLenum pname, GLint param)
{
	GET_CONTEXT;
	ctx->glExtTexObjectStateOverrideiQCOM(ctx, target, pname, param);
}

void glExtGetTexSubImageQCOM(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, GLvoid* texels)
{
	GET_CONTEXT;
	ctx->glExtGetTexSubImageQCOM(ctx, target, level, xoffset, yoffset, zoffset, width, height, depth, format, type, texels);
}

void glExtGetBufferPointervQCOM(GLenum target, GLvoidptr* params)
{
	GET_CONTEXT;
	ctx->glExtGetBufferPointervQCOM(ctx, target, params);
}

void glExtGetShadersQCOM(GLuint* shaders, GLint maxShaders, GLint* numShaders)
{
	GET_CONTEXT;
	ctx->glExtGetShadersQCOM(ctx, shaders, maxShaders, numShaders);
}

void glExtGetProgramsQCOM(GLuint* programs, GLint maxPrograms, GLint* numPrograms)
{
	GET_CONTEXT;
	ctx->glExtGetProgramsQCOM(ctx, programs, maxPrograms, numPrograms);
}

GLboolean glExtIsProgramBinaryQCOM(GLuint program)
{
	GET_CONTEXT;
	return ctx->glExtIsProgramBinaryQCOM(ctx, program);
}

void glExtGetProgramBinarySourceQCOM(GLuint program, GLenum shadertype, GLchar* source, GLint* length)
{
	GET_CONTEXT;
	ctx->glExtGetProgramBinarySourceQCOM(ctx, program, shadertype, source, length);
}

void glStartTilingQCOM(GLuint x, GLuint y, GLuint width, GLuint height, GLbitfield preserveMask)
{
	GET_CONTEXT;
	ctx->glStartTilingQCOM(ctx, x, y, width, height, preserveMask);
}

void glEndTilingQCOM(GLbitfield preserveMask)
{
	GET_CONTEXT;
	ctx->glEndTilingQCOM(ctx, preserveMask);
}

void glVertexAttribPointerData(GLuint indx, GLint size, GLenum type, GLboolean normalized, GLsizei stride, void* data, GLuint datalen)
{
	GET_CONTEXT;
	ctx->glVertexAttribPointerData(ctx, indx, size, type, normalized, stride, data, datalen);
}

void glVertexAttribPointerOffset(GLuint indx, GLint size, GLenum type, GLboolean normalized, GLsizei stride, GLuint offset)
{
	GET_CONTEXT;
	ctx->glVertexAttribPointerOffset(ctx, indx, size, type, normalized, stride, offset);
}

void glDrawElementsOffset(GLenum mode, GLsizei count, GLenum type, GLuint offset)
{
	GET_CONTEXT;
	ctx->glDrawElementsOffset(ctx, mode, count, type, offset);
}

void glDrawElementsData(GLenum mode, GLsizei count, GLenum type, void* data, GLuint datalen)
{
	GET_CONTEXT;
	ctx->glDrawElementsData(ctx, mode, count, type, data, datalen);
}

void glGetCompressedTextureFormats(int count, GLint* formats)
{
	GET_CONTEXT;
	ctx->glGetCompressedTextureFormats(ctx, count, formats);
}

void glShaderString(GLuint shader, const GLchar* string, GLsizei len)
{
	GET_CONTEXT;
	ctx->glShaderString(ctx, shader, string, len);
}

int glFinishRoundTrip()
{
	GET_CONTEXT;
	return ctx->glFinishRoundTrip(ctx);
}

void glGenVertexArrays(GLsizei n, GLuint* arrays)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGenVertexArrays(ctx, n, arrays);
}

void glBindVertexArray(GLuint array)
{
	GET_CONTEXT;
	ctx->glBindVertexArray(ctx, array);
}

void glDeleteVertexArrays(GLsizei n, const GLuint* arrays)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glDeleteVertexArrays(ctx, n, arrays);
}

GLboolean glIsVertexArray(GLuint array)
{
	GET_CONTEXT;
	return ctx->glIsVertexArray(ctx, array);
}

void* glMapBufferRange(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access)
{
	GET_CONTEXT;
	return ctx->glMapBufferRange(ctx, target, offset, length, access);
}

GLboolean glUnmapBuffer(GLenum target)
{
	GET_CONTEXT;
	return ctx->glUnmapBuffer(ctx, target);
}

void glFlushMappedBufferRange(GLenum target, GLintptr offset, GLsizeiptr length)
{
	GET_CONTEXT;
	ctx->glFlushMappedBufferRange(ctx, target, offset, length);
}

void glMapBufferRangeAEMU(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* mapped)
{
	GET_CONTEXT;
	ctx->glMapBufferRangeAEMU(ctx, target, offset, length, access, mapped);
}

void glUnmapBufferAEMU(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* guest_buffer, GLboolean* out_res)
{
	GET_CONTEXT;
	ctx->glUnmapBufferAEMU(ctx, target, offset, length, access, guest_buffer, out_res);
}

void glFlushMappedBufferRangeAEMU(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* guest_buffer)
{
	GET_CONTEXT;
	ctx->glFlushMappedBufferRangeAEMU(ctx, target, offset, length, access, guest_buffer);
}

void glReadPixelsOffsetAEMU(GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLuint offset)
{
	GET_CONTEXT;
	ctx->glReadPixelsOffsetAEMU(ctx, x, y, width, height, format, type, offset);
}

void glCompressedTexImage2DOffsetAEMU(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLint border, GLsizei imageSize, GLuint offset)
{
	GET_CONTEXT;
	ctx->glCompressedTexImage2DOffsetAEMU(ctx, target, level, internalformat, width, height, border, imageSize, offset);
}

void glCompressedTexSubImage2DOffsetAEMU(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLsizei imageSize, GLuint offset)
{
	GET_CONTEXT;
	ctx->glCompressedTexSubImage2DOffsetAEMU(ctx, target, level, xoffset, yoffset, width, height, format, imageSize, offset);
}

void glTexImage2DOffsetAEMU(GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLint border, GLenum format, GLenum type, GLuint offset)
{
	GET_CONTEXT;
	ctx->glTexImage2DOffsetAEMU(ctx, target, level, internalformat, width, height, border, format, type, offset);
}

void glTexSubImage2DOffsetAEMU(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLenum type, GLuint offset)
{
	GET_CONTEXT;
	ctx->glTexSubImage2DOffsetAEMU(ctx, target, level, xoffset, yoffset, width, height, format, type, offset);
}

void glBindBufferRange(GLenum target, GLuint index, GLuint buffer, GLintptr offset, GLsizeiptr size)
{
	GET_CONTEXT;
	ctx->glBindBufferRange(ctx, target, index, buffer, offset, size);
}

void glBindBufferBase(GLenum target, GLuint index, GLuint buffer)
{
	GET_CONTEXT;
	ctx->glBindBufferBase(ctx, target, index, buffer);
}

void glCopyBufferSubData(GLenum readtarget, GLenum writetarget, GLintptr readoffset, GLintptr writeoffset, GLsizeiptr size)
{
	GET_CONTEXT;
	ctx->glCopyBufferSubData(ctx, readtarget, writetarget, readoffset, writeoffset, size);
}

void glClearBufferiv(GLenum buffer, GLint drawBuffer, const GLint* value)
{
	GET_CONTEXT;
	ctx->glClearBufferiv(ctx, buffer, drawBuffer, value);
}

void glClearBufferuiv(GLenum buffer, GLint drawBuffer, const GLuint* value)
{
	GET_CONTEXT;
	ctx->glClearBufferuiv(ctx, buffer, drawBuffer, value);
}

void glClearBufferfv(GLenum buffer, GLint drawBuffer, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glClearBufferfv(ctx, buffer, drawBuffer, value);
}

void glClearBufferfi(GLenum buffer, GLint drawBuffer, GLfloat depth, GLint stencil)
{
	GET_CONTEXT;
	ctx->glClearBufferfi(ctx, buffer, drawBuffer, depth, stencil);
}

void glGetBufferParameteri64v(GLenum target, GLenum value, GLint64* data)
{
	GET_CONTEXT;
	ctx->glGetBufferParameteri64v(ctx, target, value, data);
}

void glGetBufferPointerv(GLenum target, GLenum pname, GLvoid** params)
{
	GET_CONTEXT;
	ctx->glGetBufferPointerv(ctx, target, pname, params);
}

void glUniformBlockBinding(GLuint program, GLuint uniformBlockIndex, GLuint uniformBlockBinding)
{
	GET_CONTEXT;
	ctx->glUniformBlockBinding(ctx, program, uniformBlockIndex, uniformBlockBinding);
}

GLuint glGetUniformBlockIndex(GLuint program, const GLchar* uniformBlockName)
{
	GET_CONTEXT;
	return ctx->glGetUniformBlockIndex(ctx, program, uniformBlockName);
}

void glGetUniformIndices(GLuint program, GLsizei uniformCount, const GLchar** uniformNames, GLuint* uniformIndices)
{
	GET_CONTEXT;
	ctx->glGetUniformIndices(ctx, program, uniformCount, uniformNames, uniformIndices);
}

void glGetUniformIndicesAEMU(GLuint program, GLsizei uniformCount, const GLchar* packedUniformNames, GLsizei packedLen, GLuint* uniformIndices)
{
	GET_CONTEXT;
	ctx->glGetUniformIndicesAEMU(ctx, program, uniformCount, packedUniformNames, packedLen, uniformIndices);
}

void glGetActiveUniformBlockiv(GLuint program, GLuint uniformBlockIndex, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetActiveUniformBlockiv(ctx, program, uniformBlockIndex, pname, params);
}

void glGetActiveUniformBlockName(GLuint program, GLuint uniformBlockIndex, GLsizei bufSize, GLsizei* length, GLchar* uniformBlockName)
{
	GET_CONTEXT;
	 if(bufSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetActiveUniformBlockName(ctx, program, uniformBlockIndex, bufSize, length, uniformBlockName);
}

void glUniform1ui(GLint location, GLuint v0)
{
	GET_CONTEXT;
	ctx->glUniform1ui(ctx, location, v0);
}

void glUniform2ui(GLint location, GLuint v0, GLuint v1)
{
	GET_CONTEXT;
	ctx->glUniform2ui(ctx, location, v0, v1);
}

void glUniform3ui(GLint location, GLuint v0, GLuint v1, GLuint v2)
{
	GET_CONTEXT;
	ctx->glUniform3ui(ctx, location, v0, v1, v2);
}

void glUniform4ui(GLint location, GLint v0, GLuint v1, GLuint v2, GLuint v3)
{
	GET_CONTEXT;
	ctx->glUniform4ui(ctx, location, v0, v1, v2, v3);
}

void glUniform1uiv(GLint location, GLsizei count, const GLuint* value)
{
	GET_CONTEXT;
	ctx->glUniform1uiv(ctx, location, count, value);
}

void glUniform2uiv(GLint location, GLsizei count, const GLuint* value)
{
	GET_CONTEXT;
	ctx->glUniform2uiv(ctx, location, count, value);
}

void glUniform3uiv(GLint location, GLsizei count, const GLuint* value)
{
	GET_CONTEXT;
	ctx->glUniform3uiv(ctx, location, count, value);
}

void glUniform4uiv(GLint location, GLsizei count, const GLuint* value)
{
	GET_CONTEXT;
	ctx->glUniform4uiv(ctx, location, count, value);
}

void glUniformMatrix2x3fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glUniformMatrix2x3fv(ctx, location, count, transpose, value);
}

void glUniformMatrix3x2fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glUniformMatrix3x2fv(ctx, location, count, transpose, value);
}

void glUniformMatrix2x4fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glUniformMatrix2x4fv(ctx, location, count, transpose, value);
}

void glUniformMatrix4x2fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glUniformMatrix4x2fv(ctx, location, count, transpose, value);
}

void glUniformMatrix3x4fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glUniformMatrix3x4fv(ctx, location, count, transpose, value);
}

void glUniformMatrix4x3fv(GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glUniformMatrix4x3fv(ctx, location, count, transpose, value);
}

void glGetUniformuiv(GLuint program, GLint location, GLuint* params)
{
	GET_CONTEXT;
	ctx->glGetUniformuiv(ctx, program, location, params);
}

void glGetActiveUniformsiv(GLuint program, GLsizei uniformCount, const GLuint* uniformIndices, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetActiveUniformsiv(ctx, program, uniformCount, uniformIndices, pname, params);
}

void glVertexAttribI4i(GLuint index, GLint v0, GLint v1, GLint v2, GLint v3)
{
	GET_CONTEXT;
	ctx->glVertexAttribI4i(ctx, index, v0, v1, v2, v3);
}

void glVertexAttribI4ui(GLuint index, GLuint v0, GLuint v1, GLuint v2, GLuint v3)
{
	GET_CONTEXT;
	ctx->glVertexAttribI4ui(ctx, index, v0, v1, v2, v3);
}

void glVertexAttribI4iv(GLuint index, const GLint* v)
{
	GET_CONTEXT;
	ctx->glVertexAttribI4iv(ctx, index, v);
}

void glVertexAttribI4uiv(GLuint index, const GLuint* v)
{
	GET_CONTEXT;
	ctx->glVertexAttribI4uiv(ctx, index, v);
}

void glVertexAttribIPointer(GLuint index, GLint size, GLenum type, GLsizei stride, const GLvoid* pointer)
{
	GET_CONTEXT;
	ctx->glVertexAttribIPointer(ctx, index, size, type, stride, pointer);
}

void glVertexAttribIPointerOffsetAEMU(GLuint index, GLint size, GLenum type, GLsizei stride, GLuint offset)
{
	GET_CONTEXT;
	ctx->glVertexAttribIPointerOffsetAEMU(ctx, index, size, type, stride, offset);
}

void glVertexAttribIPointerDataAEMU(GLuint index, GLint size, GLenum type, GLsizei stride, void* data, GLuint datalen)
{
	GET_CONTEXT;
	ctx->glVertexAttribIPointerDataAEMU(ctx, index, size, type, stride, data, datalen);
}

void glGetVertexAttribIiv(GLuint index, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetVertexAttribIiv(ctx, index, pname, params);
}

void glGetVertexAttribIuiv(GLuint index, GLenum pname, GLuint* params)
{
	GET_CONTEXT;
	ctx->glGetVertexAttribIuiv(ctx, index, pname, params);
}

void glVertexAttribDivisor(GLuint index, GLuint divisor)
{
	GET_CONTEXT;
	ctx->glVertexAttribDivisor(ctx, index, divisor);
}

void glDrawArraysInstanced(GLenum mode, GLint first, GLsizei count, GLsizei primcount)
{
	GET_CONTEXT;
	ctx->glDrawArraysInstanced(ctx, mode, first, count, primcount);
}

void glDrawElementsInstanced(GLenum mode, GLsizei count, GLenum type, const void* indices, GLsizei primcount)
{
	GET_CONTEXT;
	ctx->glDrawElementsInstanced(ctx, mode, count, type, indices, primcount);
}

void glDrawElementsInstancedDataAEMU(GLenum mode, GLsizei count, GLenum type, const void* indices, GLsizei primcount, GLsizei datalen)
{
	GET_CONTEXT;
	ctx->glDrawElementsInstancedDataAEMU(ctx, mode, count, type, indices, primcount, datalen);
}

void glDrawElementsInstancedOffsetAEMU(GLenum mode, GLsizei count, GLenum type, GLuint offset, GLsizei primcount)
{
	GET_CONTEXT;
	ctx->glDrawElementsInstancedOffsetAEMU(ctx, mode, count, type, offset, primcount);
}

void glDrawRangeElements(GLenum mode, GLuint start, GLuint end, GLsizei count, GLenum type, const GLvoid* indices)
{
	GET_CONTEXT;
	ctx->glDrawRangeElements(ctx, mode, start, end, count, type, indices);
}

void glDrawRangeElementsDataAEMU(GLenum mode, GLuint start, GLuint end, GLsizei count, GLenum type, const GLvoid* indices, GLsizei datalen)
{
	GET_CONTEXT;
	ctx->glDrawRangeElementsDataAEMU(ctx, mode, start, end, count, type, indices, datalen);
}

void glDrawRangeElementsOffsetAEMU(GLenum mode, GLuint start, GLuint end, GLsizei count, GLenum type, GLuint offset)
{
	GET_CONTEXT;
	ctx->glDrawRangeElementsOffsetAEMU(ctx, mode, start, end, count, type, offset);
}

GLsync glFenceSync(GLenum condition, GLbitfield flags)
{
	GET_CONTEXT;
	return ctx->glFenceSync(ctx, condition, flags);
}

GLenum glClientWaitSync(GLsync wait_on, GLbitfield flags, GLuint64 timeout)
{
	GET_CONTEXT;
	return ctx->glClientWaitSync(ctx, wait_on, flags, timeout);
}

void glWaitSync(GLsync wait_on, GLbitfield flags, GLuint64 timeout)
{
	GET_CONTEXT;
	ctx->glWaitSync(ctx, wait_on, flags, timeout);
}

void glDeleteSync(GLsync to_delete)
{
	GET_CONTEXT;
	ctx->glDeleteSync(ctx, to_delete);
}

GLboolean glIsSync(GLsync sync)
{
	GET_CONTEXT;
	return ctx->glIsSync(ctx, sync);
}

void glGetSynciv(GLsync sync, GLenum pname, GLsizei bufSize, GLsizei* length, GLint* values)
{
	GET_CONTEXT;
	ctx->glGetSynciv(ctx, sync, pname, bufSize, length, values);
}

uint64_t glFenceSyncAEMU(GLenum condition, GLbitfield flags)
{
	GET_CONTEXT;
	return ctx->glFenceSyncAEMU(ctx, condition, flags);
}

GLenum glClientWaitSyncAEMU(uint64_t wait_on, GLbitfield flags, GLuint64 timeout)
{
	GET_CONTEXT;
	return ctx->glClientWaitSyncAEMU(ctx, wait_on, flags, timeout);
}

void glWaitSyncAEMU(uint64_t wait_on, GLbitfield flags, GLuint64 timeout)
{
	GET_CONTEXT;
	ctx->glWaitSyncAEMU(ctx, wait_on, flags, timeout);
}

void glDeleteSyncAEMU(uint64_t to_delete)
{
	GET_CONTEXT;
	ctx->glDeleteSyncAEMU(ctx, to_delete);
}

GLboolean glIsSyncAEMU(uint64_t sync)
{
	GET_CONTEXT;
	return ctx->glIsSyncAEMU(ctx, sync);
}

void glGetSyncivAEMU(uint64_t sync, GLenum pname, GLsizei bufSize, GLsizei* length, GLint* values)
{
	GET_CONTEXT;
	 if(bufSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetSyncivAEMU(ctx, sync, pname, bufSize, length, values);
}

void glDrawBuffers(GLsizei n, const GLenum* bufs)
{
	GET_CONTEXT;
	ctx->glDrawBuffers(ctx, n, bufs);
}

void glReadBuffer(GLenum src)
{
	GET_CONTEXT;
	ctx->glReadBuffer(ctx, src);
}

void glBlitFramebuffer(GLint srcX0, GLint srcY0, GLint srcX1, GLint srcY1, GLint dstX0, GLint dstY0, GLint dstX1, GLint dstY1, GLbitfield mask, GLenum filter)
{
	GET_CONTEXT;
	ctx->glBlitFramebuffer(ctx, srcX0, srcY0, srcX1, srcY1, dstX0, dstY0, dstX1, dstY1, mask, filter);
}

void glInvalidateFramebuffer(GLenum target, GLsizei numAttachments, const GLenum* attachments)
{
	GET_CONTEXT;
	ctx->glInvalidateFramebuffer(ctx, target, numAttachments, attachments);
}

void glInvalidateSubFramebuffer(GLenum target, GLsizei numAttachments, const GLenum* attachments, GLint x, GLint y, GLsizei width, GLsizei height)
{
	GET_CONTEXT;
	ctx->glInvalidateSubFramebuffer(ctx, target, numAttachments, attachments, x, y, width, height);
}

void glFramebufferTextureLayer(GLenum target, GLenum attachment, GLuint texture, GLint level, GLint layer)
{
	GET_CONTEXT;
	ctx->glFramebufferTextureLayer(ctx, target, attachment, texture, level, layer);
}

void glRenderbufferStorageMultisample(GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height)
{
	GET_CONTEXT;
	ctx->glRenderbufferStorageMultisample(ctx, target, samples, internalformat, width, height);
}

void glTexStorage2D(GLenum target, GLsizei levels, GLenum internalformat, GLsizei width, GLsizei height)
{
	GET_CONTEXT;
	ctx->glTexStorage2D(ctx, target, levels, internalformat, width, height);
}

void glGetInternalformativ(GLenum target, GLenum internalformat, GLenum pname, GLsizei bufSize, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetInternalformativ(ctx, target, internalformat, pname, bufSize, params);
}

void glBeginTransformFeedback(GLenum primitiveMode)
{
	GET_CONTEXT;
	ctx->glBeginTransformFeedback(ctx, primitiveMode);
}

void glEndTransformFeedback()
{
	GET_CONTEXT;
	ctx->glEndTransformFeedback(ctx);
}

void glGenTransformFeedbacks(GLsizei n, GLuint* ids)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGenTransformFeedbacks(ctx, n, ids);
}

void glDeleteTransformFeedbacks(GLsizei n, const GLuint* ids)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glDeleteTransformFeedbacks(ctx, n, ids);
}

void glBindTransformFeedback(GLenum target, GLuint id)
{
	GET_CONTEXT;
	ctx->glBindTransformFeedback(ctx, target, id);
}

void glPauseTransformFeedback()
{
	GET_CONTEXT;
	ctx->glPauseTransformFeedback(ctx);
}

void glResumeTransformFeedback()
{
	GET_CONTEXT;
	ctx->glResumeTransformFeedback(ctx);
}

GLboolean glIsTransformFeedback(GLuint id)
{
	GET_CONTEXT;
	return ctx->glIsTransformFeedback(ctx, id);
}

void glTransformFeedbackVaryings(GLuint program, GLsizei count, const char** varyings, GLenum bufferMode)
{
	GET_CONTEXT;
	ctx->glTransformFeedbackVaryings(ctx, program, count, varyings, bufferMode);
}

void glTransformFeedbackVaryingsAEMU(GLuint program, GLsizei count, const char* packedVaryings, GLuint packedVaryingsLen, GLenum bufferMode)
{
	GET_CONTEXT;
	ctx->glTransformFeedbackVaryingsAEMU(ctx, program, count, packedVaryings, packedVaryingsLen, bufferMode);
}

void glGetTransformFeedbackVarying(GLuint program, GLuint index, GLsizei bufSize, GLsizei* length, GLsizei* size, GLenum* type, char* name)
{
	GET_CONTEXT;
	 if(bufSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetTransformFeedbackVarying(ctx, program, index, bufSize, length, size, type, name);
}

void glGenSamplers(GLsizei n, GLuint* samplers)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGenSamplers(ctx, n, samplers);
}

void glDeleteSamplers(GLsizei n, const GLuint* samplers)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glDeleteSamplers(ctx, n, samplers);
}

void glBindSampler(GLuint unit, GLuint sampler)
{
	GET_CONTEXT;
	ctx->glBindSampler(ctx, unit, sampler);
}

void glSamplerParameterf(GLuint sampler, GLenum pname, GLfloat param)
{
	GET_CONTEXT;
	ctx->glSamplerParameterf(ctx, sampler, pname, param);
}

void glSamplerParameteri(GLuint sampler, GLenum pname, GLint param)
{
	GET_CONTEXT;
	ctx->glSamplerParameteri(ctx, sampler, pname, param);
}

void glSamplerParameterfv(GLuint sampler, GLenum pname, const GLfloat* params)
{
	GET_CONTEXT;
	ctx->glSamplerParameterfv(ctx, sampler, pname, params);
}

void glSamplerParameteriv(GLuint sampler, GLenum pname, const GLint* params)
{
	GET_CONTEXT;
	ctx->glSamplerParameteriv(ctx, sampler, pname, params);
}

void glGetSamplerParameterfv(GLuint sampler, GLenum pname, GLfloat* params)
{
	GET_CONTEXT;
	ctx->glGetSamplerParameterfv(ctx, sampler, pname, params);
}

void glGetSamplerParameteriv(GLuint sampler, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetSamplerParameteriv(ctx, sampler, pname, params);
}

GLboolean glIsSampler(GLuint sampler)
{
	GET_CONTEXT;
	return ctx->glIsSampler(ctx, sampler);
}

void glGenQueries(GLsizei n, GLuint* queries)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGenQueries(ctx, n, queries);
}

void glDeleteQueries(GLsizei n, const GLuint* queries)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glDeleteQueries(ctx, n, queries);
}

void glBeginQuery(GLenum target, GLuint query)
{
	GET_CONTEXT;
	ctx->glBeginQuery(ctx, target, query);
}

void glEndQuery(GLenum target)
{
	GET_CONTEXT;
	ctx->glEndQuery(ctx, target);
}

void glGetQueryiv(GLenum target, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetQueryiv(ctx, target, pname, params);
}

void glGetQueryObjectuiv(GLuint query, GLenum pname, GLuint* params)
{
	GET_CONTEXT;
	ctx->glGetQueryObjectuiv(ctx, query, pname, params);
}

GLboolean glIsQuery(GLuint query)
{
	GET_CONTEXT;
	return ctx->glIsQuery(ctx, query);
}

void glProgramParameteri(GLuint program, GLenum pname, GLint value)
{
	GET_CONTEXT;
	ctx->glProgramParameteri(ctx, program, pname, value);
}

void glProgramBinary(GLuint program, GLenum binaryFormat, const void* binary, GLsizei length)
{
	GET_CONTEXT;
	ctx->glProgramBinary(ctx, program, binaryFormat, binary, length);
}

void glGetProgramBinary(GLuint program, GLsizei bufSize, GLsizei* length, GLenum* binaryFormat, void* binary)
{
	GET_CONTEXT;
	 if(bufSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetProgramBinary(ctx, program, bufSize, length, binaryFormat, binary);
}

GLint glGetFragDataLocation(GLuint program, const char* name)
{
	GET_CONTEXT;
	return ctx->glGetFragDataLocation(ctx, program, name);
}

void glGetInteger64v(GLenum pname, GLint64* data)
{
	GET_CONTEXT;
	ctx->glGetInteger64v(ctx, pname, data);
}

void glGetIntegeri_v(GLenum target, GLuint index, GLint* data)
{
	GET_CONTEXT;
	ctx->glGetIntegeri_v(ctx, target, index, data);
}

void glGetInteger64i_v(GLenum target, GLuint index, GLint64* data)
{
	GET_CONTEXT;
	ctx->glGetInteger64i_v(ctx, target, index, data);
}

void glTexImage3D(GLenum target, GLint level, GLint internalFormat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLenum format, GLenum type, const GLvoid* data)
{
	GET_CONTEXT;
	ctx->glTexImage3D(ctx, target, level, internalFormat, width, height, depth, border, format, type, data);
}

void glTexImage3DOffsetAEMU(GLenum target, GLint level, GLint internalFormat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLenum format, GLenum type, GLuint offset)
{
	GET_CONTEXT;
	ctx->glTexImage3DOffsetAEMU(ctx, target, level, internalFormat, width, height, depth, border, format, type, offset);
}

void glTexStorage3D(GLenum target, GLsizei levels, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth)
{
	GET_CONTEXT;
	ctx->glTexStorage3D(ctx, target, levels, internalformat, width, height, depth);
}

void glTexSubImage3D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, const GLvoid* data)
{
	GET_CONTEXT;
	ctx->glTexSubImage3D(ctx, target, level, xoffset, yoffset, zoffset, width, height, depth, format, type, data);
}

void glTexSubImage3DOffsetAEMU(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, GLuint offset)
{
	GET_CONTEXT;
	ctx->glTexSubImage3DOffsetAEMU(ctx, target, level, xoffset, yoffset, zoffset, width, height, depth, format, type, offset);
}

void glCompressedTexImage3D(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLsizei imageSize, const GLvoid* data)
{
	GET_CONTEXT;
	 if(imageSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glCompressedTexImage3D(ctx, target, level, internalformat, width, height, depth, border, imageSize, data);
}

void glCompressedTexImage3DOffsetAEMU(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLsizei imageSize, GLuint offset)
{
	GET_CONTEXT;
	ctx->glCompressedTexImage3DOffsetAEMU(ctx, target, level, internalformat, width, height, depth, border, imageSize, offset);
}

void glCompressedTexSubImage3D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLsizei imageSize, const GLvoid* data)
{
	GET_CONTEXT;
	 if(imageSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glCompressedTexSubImage3D(ctx, target, level, xoffset, yoffset, zoffset, width, height, depth, format, imageSize, data);
}

void glCompressedTexSubImage3DOffsetAEMU(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLsizei imageSize, GLuint data)
{
	GET_CONTEXT;
	ctx->glCompressedTexSubImage3DOffsetAEMU(ctx, target, level, xoffset, yoffset, zoffset, width, height, depth, format, imageSize, data);
}

void glCopyTexSubImage3D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLint x, GLint y, GLsizei width, GLsizei height)
{
	GET_CONTEXT;
	ctx->glCopyTexSubImage3D(ctx, target, level, xoffset, yoffset, zoffset, x, y, width, height);
}

const GLubyte* glGetStringi(GLenum name, GLuint index)
{
	GET_CONTEXT;
	return ctx->glGetStringi(ctx, name, index);
}

void glGetBooleani_v(GLenum target, GLuint index, GLboolean* data)
{
	GET_CONTEXT;
	ctx->glGetBooleani_v(ctx, target, index, data);
}

void glMemoryBarrier(GLbitfield barriers)
{
	GET_CONTEXT;
	ctx->glMemoryBarrier(ctx, barriers);
}

void glMemoryBarrierByRegion(GLbitfield barriers)
{
	GET_CONTEXT;
	ctx->glMemoryBarrierByRegion(ctx, barriers);
}

void glGenProgramPipelines(GLsizei n, GLuint* pipelines)
{
	GET_CONTEXT;
	ctx->glGenProgramPipelines(ctx, n, pipelines);
}

void glDeleteProgramPipelines(GLsizei n, const GLuint* pipelines)
{
	GET_CONTEXT;
	 if(n<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glDeleteProgramPipelines(ctx, n, pipelines);
}

void glBindProgramPipeline(GLuint pipeline)
{
	GET_CONTEXT;
	ctx->glBindProgramPipeline(ctx, pipeline);
}

void glGetProgramPipelineiv(GLuint pipeline, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetProgramPipelineiv(ctx, pipeline, pname, params);
}

void glGetProgramPipelineInfoLog(GLuint pipeline, GLsizei bufSize, GLsizei* length, GLchar* infoLog)
{
	GET_CONTEXT;
	 if(bufSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetProgramPipelineInfoLog(ctx, pipeline, bufSize, length, infoLog);
}

void glValidateProgramPipeline(GLuint pipeline)
{
	GET_CONTEXT;
	ctx->glValidateProgramPipeline(ctx, pipeline);
}

GLboolean glIsProgramPipeline(GLuint pipeline)
{
	GET_CONTEXT;
	return ctx->glIsProgramPipeline(ctx, pipeline);
}

void glUseProgramStages(GLuint pipeline, GLbitfield stages, GLuint program)
{
	GET_CONTEXT;
	ctx->glUseProgramStages(ctx, pipeline, stages, program);
}

void glActiveShaderProgram(GLuint pipeline, GLuint program)
{
	GET_CONTEXT;
	ctx->glActiveShaderProgram(ctx, pipeline, program);
}

GLuint glCreateShaderProgramv(GLenum type, GLsizei count, const char** strings)
{
	GET_CONTEXT;
	return ctx->glCreateShaderProgramv(ctx, type, count, strings);
}

GLuint glCreateShaderProgramvAEMU(GLenum type, GLsizei count, const char* packedStrings, GLuint packedLen)
{
	GET_CONTEXT;
	return ctx->glCreateShaderProgramvAEMU(ctx, type, count, packedStrings, packedLen);
}

void glProgramUniform1f(GLuint program, GLint location, GLfloat v0)
{
	GET_CONTEXT;
	ctx->glProgramUniform1f(ctx, program, location, v0);
}

void glProgramUniform2f(GLuint program, GLint location, GLfloat v0, GLfloat v1)
{
	GET_CONTEXT;
	ctx->glProgramUniform2f(ctx, program, location, v0, v1);
}

void glProgramUniform3f(GLuint program, GLint location, GLfloat v0, GLfloat v1, GLfloat v2)
{
	GET_CONTEXT;
	ctx->glProgramUniform3f(ctx, program, location, v0, v1, v2);
}

void glProgramUniform4f(GLuint program, GLint location, GLfloat v0, GLfloat v1, GLfloat v2, GLfloat v3)
{
	GET_CONTEXT;
	ctx->glProgramUniform4f(ctx, program, location, v0, v1, v2, v3);
}

void glProgramUniform1i(GLuint program, GLint location, GLint v0)
{
	GET_CONTEXT;
	ctx->glProgramUniform1i(ctx, program, location, v0);
}

void glProgramUniform2i(GLuint program, GLint location, GLint v0, GLint v1)
{
	GET_CONTEXT;
	ctx->glProgramUniform2i(ctx, program, location, v0, v1);
}

void glProgramUniform3i(GLuint program, GLint location, GLint v0, GLint v1, GLint v2)
{
	GET_CONTEXT;
	ctx->glProgramUniform3i(ctx, program, location, v0, v1, v2);
}

void glProgramUniform4i(GLuint program, GLint location, GLint v0, GLint v1, GLint v2, GLint v3)
{
	GET_CONTEXT;
	ctx->glProgramUniform4i(ctx, program, location, v0, v1, v2, v3);
}

void glProgramUniform1ui(GLuint program, GLint location, GLuint v0)
{
	GET_CONTEXT;
	ctx->glProgramUniform1ui(ctx, program, location, v0);
}

void glProgramUniform2ui(GLuint program, GLint location, GLint v0, GLuint v1)
{
	GET_CONTEXT;
	ctx->glProgramUniform2ui(ctx, program, location, v0, v1);
}

void glProgramUniform3ui(GLuint program, GLint location, GLint v0, GLint v1, GLuint v2)
{
	GET_CONTEXT;
	ctx->glProgramUniform3ui(ctx, program, location, v0, v1, v2);
}

void glProgramUniform4ui(GLuint program, GLint location, GLint v0, GLint v1, GLint v2, GLuint v3)
{
	GET_CONTEXT;
	ctx->glProgramUniform4ui(ctx, program, location, v0, v1, v2, v3);
}

void glProgramUniform1fv(GLuint program, GLint location, GLsizei count, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniform1fv(ctx, program, location, count, value);
}

void glProgramUniform2fv(GLuint program, GLint location, GLsizei count, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniform2fv(ctx, program, location, count, value);
}

void glProgramUniform3fv(GLuint program, GLint location, GLsizei count, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniform3fv(ctx, program, location, count, value);
}

void glProgramUniform4fv(GLuint program, GLint location, GLsizei count, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniform4fv(ctx, program, location, count, value);
}

void glProgramUniform1iv(GLuint program, GLint location, GLsizei count, const GLint* value)
{
	GET_CONTEXT;
	ctx->glProgramUniform1iv(ctx, program, location, count, value);
}

void glProgramUniform2iv(GLuint program, GLint location, GLsizei count, const GLint* value)
{
	GET_CONTEXT;
	ctx->glProgramUniform2iv(ctx, program, location, count, value);
}

void glProgramUniform3iv(GLuint program, GLint location, GLsizei count, const GLint* value)
{
	GET_CONTEXT;
	ctx->glProgramUniform3iv(ctx, program, location, count, value);
}

void glProgramUniform4iv(GLuint program, GLint location, GLsizei count, const GLint* value)
{
	GET_CONTEXT;
	ctx->glProgramUniform4iv(ctx, program, location, count, value);
}

void glProgramUniform1uiv(GLuint program, GLint location, GLsizei count, const GLuint* value)
{
	GET_CONTEXT;
	ctx->glProgramUniform1uiv(ctx, program, location, count, value);
}

void glProgramUniform2uiv(GLuint program, GLint location, GLsizei count, const GLuint* value)
{
	GET_CONTEXT;
	ctx->glProgramUniform2uiv(ctx, program, location, count, value);
}

void glProgramUniform3uiv(GLuint program, GLint location, GLsizei count, const GLuint* value)
{
	GET_CONTEXT;
	ctx->glProgramUniform3uiv(ctx, program, location, count, value);
}

void glProgramUniform4uiv(GLuint program, GLint location, GLsizei count, const GLuint* value)
{
	GET_CONTEXT;
	ctx->glProgramUniform4uiv(ctx, program, location, count, value);
}

void glProgramUniformMatrix2fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniformMatrix2fv(ctx, program, location, count, transpose, value);
}

void glProgramUniformMatrix3fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniformMatrix3fv(ctx, program, location, count, transpose, value);
}

void glProgramUniformMatrix4fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniformMatrix4fv(ctx, program, location, count, transpose, value);
}

void glProgramUniformMatrix2x3fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniformMatrix2x3fv(ctx, program, location, count, transpose, value);
}

void glProgramUniformMatrix3x2fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniformMatrix3x2fv(ctx, program, location, count, transpose, value);
}

void glProgramUniformMatrix2x4fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniformMatrix2x4fv(ctx, program, location, count, transpose, value);
}

void glProgramUniformMatrix4x2fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniformMatrix4x2fv(ctx, program, location, count, transpose, value);
}

void glProgramUniformMatrix3x4fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniformMatrix3x4fv(ctx, program, location, count, transpose, value);
}

void glProgramUniformMatrix4x3fv(GLuint program, GLint location, GLsizei count, GLboolean transpose, const GLfloat* value)
{
	GET_CONTEXT;
	ctx->glProgramUniformMatrix4x3fv(ctx, program, location, count, transpose, value);
}

void glGetProgramInterfaceiv(GLuint program, GLenum programInterface, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetProgramInterfaceiv(ctx, program, programInterface, pname, params);
}

void glGetProgramResourceiv(GLuint program, GLenum programInterface, GLuint index, GLsizei propCount, const GLenum* props, GLsizei bufSize, GLsizei* length, GLint* params)
{
	GET_CONTEXT;
	 if(bufSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetProgramResourceiv(ctx, program, programInterface, index, propCount, props, bufSize, length, params);
}

GLuint glGetProgramResourceIndex(GLuint program, GLenum programInterface, const char* name)
{
	GET_CONTEXT;
	return ctx->glGetProgramResourceIndex(ctx, program, programInterface, name);
}

GLint glGetProgramResourceLocation(GLuint program, GLenum programInterface, const char* name)
{
	GET_CONTEXT;
	return ctx->glGetProgramResourceLocation(ctx, program, programInterface, name);
}

void glGetProgramResourceName(GLuint program, GLenum programInterface, GLuint index, GLsizei bufSize, GLsizei* length, char* name)
{
	GET_CONTEXT;
	 if(bufSize<0){ ctx->setError(GL_INVALID_VALUE); return; }
	ctx->glGetProgramResourceName(ctx, program, programInterface, index, bufSize, length, name);
}

void glBindImageTexture(GLuint unit, GLuint texture, GLint level, GLboolean layered, GLint layer, GLenum access, GLenum format)
{
	GET_CONTEXT;
	ctx->glBindImageTexture(ctx, unit, texture, level, layered, layer, access, format);
}

void glDispatchCompute(GLuint num_groups_x, GLuint num_groups_y, GLuint num_groups_z)
{
	GET_CONTEXT;
	ctx->glDispatchCompute(ctx, num_groups_x, num_groups_y, num_groups_z);
}

void glDispatchComputeIndirect(GLintptr indirect)
{
	GET_CONTEXT;
	ctx->glDispatchComputeIndirect(ctx, indirect);
}

void glBindVertexBuffer(GLuint bindingindex, GLuint buffer, GLintptr offset, GLintptr stride)
{
	GET_CONTEXT;
	ctx->glBindVertexBuffer(ctx, bindingindex, buffer, offset, stride);
}

void glVertexAttribBinding(GLuint attribindex, GLuint bindingindex)
{
	GET_CONTEXT;
	ctx->glVertexAttribBinding(ctx, attribindex, bindingindex);
}

void glVertexAttribFormat(GLuint attribindex, GLint size, GLenum type, GLboolean normalized, GLuint relativeoffset)
{
	GET_CONTEXT;
	ctx->glVertexAttribFormat(ctx, attribindex, size, type, normalized, relativeoffset);
}

void glVertexAttribIFormat(GLuint attribindex, GLint size, GLenum type, GLuint relativeoffset)
{
	GET_CONTEXT;
	ctx->glVertexAttribIFormat(ctx, attribindex, size, type, relativeoffset);
}

void glVertexBindingDivisor(GLuint bindingindex, GLuint divisor)
{
	GET_CONTEXT;
	ctx->glVertexBindingDivisor(ctx, bindingindex, divisor);
}

void glDrawArraysIndirect(GLenum mode, const void* indirect)
{
	GET_CONTEXT;
	ctx->glDrawArraysIndirect(ctx, mode, indirect);
}

void glDrawArraysIndirectDataAEMU(GLenum mode, const void* indirect, GLuint datalen)
{
	GET_CONTEXT;
	ctx->glDrawArraysIndirectDataAEMU(ctx, mode, indirect, datalen);
}

void glDrawArraysIndirectOffsetAEMU(GLenum mode, GLuint offset)
{
	GET_CONTEXT;
	ctx->glDrawArraysIndirectOffsetAEMU(ctx, mode, offset);
}

void glDrawElementsIndirect(GLenum mode, GLenum type, const void* indirect)
{
	GET_CONTEXT;
	ctx->glDrawElementsIndirect(ctx, mode, type, indirect);
}

void glDrawElementsIndirectDataAEMU(GLenum mode, GLenum type, const void* indirect, GLuint datalen)
{
	GET_CONTEXT;
	ctx->glDrawElementsIndirectDataAEMU(ctx, mode, type, indirect, datalen);
}

void glDrawElementsIndirectOffsetAEMU(GLenum mode, GLenum type, GLuint offset)
{
	GET_CONTEXT;
	ctx->glDrawElementsIndirectOffsetAEMU(ctx, mode, type, offset);
}

void glTexStorage2DMultisample(GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height, GLboolean fixedsamplelocations)
{
	GET_CONTEXT;
	ctx->glTexStorage2DMultisample(ctx, target, samples, internalformat, width, height, fixedsamplelocations);
}

void glSampleMaski(GLuint maskNumber, GLbitfield mask)
{
	GET_CONTEXT;
	ctx->glSampleMaski(ctx, maskNumber, mask);
}

void glGetMultisamplefv(GLenum pname, GLuint index, GLfloat* val)
{
	GET_CONTEXT;
	ctx->glGetMultisamplefv(ctx, pname, index, val);
}

void glFramebufferParameteri(GLenum target, GLenum pname, GLint param)
{
	GET_CONTEXT;
	ctx->glFramebufferParameteri(ctx, target, pname, param);
}

void glGetFramebufferParameteriv(GLenum target, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetFramebufferParameteriv(ctx, target, pname, params);
}

void glGetTexLevelParameterfv(GLenum target, GLint level, GLenum pname, GLfloat* params)
{
	GET_CONTEXT;
	ctx->glGetTexLevelParameterfv(ctx, target, level, pname, params);
}

void glGetTexLevelParameteriv(GLenum target, GLint level, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetTexLevelParameteriv(ctx, target, level, pname, params);
}

void glMapBufferRangeDMA(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, uint64_t paddr)
{
	GET_CONTEXT;
	ctx->glMapBufferRangeDMA(ctx, target, offset, length, access, paddr);
}

void glUnmapBufferDMA(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, uint64_t paddr, GLboolean* out_res)
{
	GET_CONTEXT;
	ctx->glUnmapBufferDMA(ctx, target, offset, length, access, paddr, out_res);
}

uint64_t glMapBufferRangeDirect(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, uint64_t paddr)
{
	GET_CONTEXT;
	return ctx->glMapBufferRangeDirect(ctx, target, offset, length, access, paddr);
}

void glUnmapBufferDirect(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, uint64_t paddr, uint64_t guest_ptr, GLboolean* out_res)
{
	GET_CONTEXT;
	ctx->glUnmapBufferDirect(ctx, target, offset, length, access, paddr, guest_ptr, out_res);
}

void glFlushMappedBufferRangeDirect(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access)
{
	GET_CONTEXT;
	ctx->glFlushMappedBufferRangeDirect(ctx, target, offset, length, access);
}

GLenum glGetGraphicsResetStatusEXT()
{
	GET_CONTEXT;
	return ctx->glGetGraphicsResetStatusEXT(ctx);
}

void glReadnPixelsEXT(GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLsizei bufSize, GLvoid* data)
{
	GET_CONTEXT;
	ctx->glReadnPixelsEXT(ctx, x, y, width, height, format, type, bufSize, data);
}

void glGetnUniformfvEXT(GLuint program, GLint location, GLsizei bufSize, GLfloat* params)
{
	GET_CONTEXT;
	ctx->glGetnUniformfvEXT(ctx, program, location, bufSize, params);
}

void glGetnUniformivEXT(GLuint program, GLint location, GLsizei bufSize, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetnUniformivEXT(ctx, program, location, bufSize, params);
}

void glDrawArraysNullAEMU(GLenum mode, GLint first, GLsizei count)
{
	GET_CONTEXT;
	ctx->glDrawArraysNullAEMU(ctx, mode, first, count);
}

void glDrawElementsNullAEMU(GLenum mode, GLsizei count, GLenum type, const GLvoid* indices)
{
	GET_CONTEXT;
	ctx->glDrawElementsNullAEMU(ctx, mode, count, type, indices);
}

void glDrawElementsOffsetNullAEMU(GLenum mode, GLsizei count, GLenum type, GLuint offset)
{
	GET_CONTEXT;
	ctx->glDrawElementsOffsetNullAEMU(ctx, mode, count, type, offset);
}

void glDrawElementsDataNullAEMU(GLenum mode, GLsizei count, GLenum type, void* data, GLuint datalen)
{
	GET_CONTEXT;
	ctx->glDrawElementsDataNullAEMU(ctx, mode, count, type, data, datalen);
}

void glUnmapBufferAsyncAEMU(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* guest_buffer, GLboolean* out_res)
{
	GET_CONTEXT;
	ctx->glUnmapBufferAsyncAEMU(ctx, target, offset, length, access, guest_buffer, out_res);
}

void glFlushMappedBufferRangeAEMU2(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access, void* guest_buffer)
{
	GET_CONTEXT;
	ctx->glFlushMappedBufferRangeAEMU2(ctx, target, offset, length, access, guest_buffer);
}

GLboolean glBufferDataSyncAEMU(GLenum target, GLsizeiptr size, const GLvoid* data, GLenum usage)
{
	GET_CONTEXT;
	return ctx->glBufferDataSyncAEMU(ctx, target, size, data, usage);
}

void glTexBufferOES(GLenum target, GLenum internalFormat, GLuint buffer)
{
	GET_CONTEXT;
	ctx->glTexBufferOES(ctx, target, internalFormat, buffer);
}

void glTexBufferRangeOES(GLenum target, GLenum internalFormat, GLuint buffer, GLintptr offset, GLsizeiptr size)
{
	GET_CONTEXT;
	ctx->glTexBufferRangeOES(ctx, target, internalFormat, buffer, offset, size);
}

void glTexBufferEXT(GLenum target, GLenum internalFormat, GLuint buffer)
{
	GET_CONTEXT;
	ctx->glTexBufferEXT(ctx, target, internalFormat, buffer);
}

void glTexBufferRangeEXT(GLenum target, GLenum internalFormat, GLuint buffer, GLintptr offset, GLsizeiptr size)
{
	GET_CONTEXT;
	ctx->glTexBufferRangeEXT(ctx, target, internalFormat, buffer, offset, size);
}

void glEnableiEXT(GLenum cap, GLuint index)
{
	GET_CONTEXT;
	ctx->glEnableiEXT(ctx, cap, index);
}

void glDisableiEXT(GLenum cap, GLuint index)
{
	GET_CONTEXT;
	ctx->glDisableiEXT(ctx, cap, index);
}

void glBlendEquationiEXT(GLuint index, GLenum mode)
{
	GET_CONTEXT;
	ctx->glBlendEquationiEXT(ctx, index, mode);
}

void glBlendEquationSeparateiEXT(GLuint index, GLenum modeRGB, GLenum modeAlpha)
{
	GET_CONTEXT;
	ctx->glBlendEquationSeparateiEXT(ctx, index, modeRGB, modeAlpha);
}

void glBlendFunciEXT(GLuint index, GLenum sfactor, GLenum dfactor)
{
	GET_CONTEXT;
	ctx->glBlendFunciEXT(ctx, index, sfactor, dfactor);
}

void glBlendFuncSeparateiEXT(GLuint index, GLenum srcRGB, GLenum dstRGB, GLenum srcAlpha, GLenum dstAlpha)
{
	GET_CONTEXT;
	ctx->glBlendFuncSeparateiEXT(ctx, index, srcRGB, dstRGB, srcAlpha, dstAlpha);
}

void glColorMaskiEXT(GLuint index, GLboolean red, GLboolean green, GLboolean blue, GLboolean alpha)
{
	GET_CONTEXT;
	ctx->glColorMaskiEXT(ctx, index, red, green, blue, alpha);
}

GLboolean glIsEnablediEXT(GLenum cap, GLuint index)
{
	GET_CONTEXT;
	return ctx->glIsEnablediEXT(ctx, cap, index);
}

void glBlendBarrier()
{
	GET_CONTEXT;
	ctx->glBlendBarrier(ctx);
}

void glCopyImageSubData(GLuint srcName, GLenum srcTarget, GLint srcLevel, GLint srcX, GLint srcY, GLint srcZ, GLuint dstName, GLenum dstTarget, GLint dstLevel, GLint dstX, GLint dstY, GLint dstZ, GLsizei srcWidth, GLsizei srcHeight, GLsizei srcDepth)
{
	GET_CONTEXT;
	ctx->glCopyImageSubData(ctx, srcName, srcTarget, srcLevel, srcX, srcY, srcZ, dstName, dstTarget, dstLevel, dstX, dstY, dstZ, srcWidth, srcHeight, srcDepth);
}

void glObjectLabel(GLenum identifier, GLuint name, GLsizei length, const GLchar* label)
{
	GET_CONTEXT;
	ctx->glObjectLabel(ctx, identifier, name, length, label);
}

void glGetObjectLabel(GLenum identifier, GLuint name, GLsizei bufSize, GLsizei* length, GLchar* label)
{
	GET_CONTEXT;
	ctx->glGetObjectLabel(ctx, identifier, name, bufSize, length, label);
}

void glObjectPtrLabel(const void* ptr, GLsizei length, const GLchar* label)
{
	GET_CONTEXT;
	ctx->glObjectPtrLabel(ctx, ptr, length, label);
}

void glGetObjectPtrLabel(const void* ptr, GLsizei bufSize, GLsizei* length, GLchar* label)
{
	GET_CONTEXT;
	ctx->glGetObjectPtrLabel(ctx, ptr, bufSize, length, label);
}

void glEnablei(GLenum target, GLuint index)
{
	GET_CONTEXT;
	ctx->glEnablei(ctx, target, index);
}

void glDisablei(GLenum target, GLuint index)
{
	GET_CONTEXT;
	ctx->glDisablei(ctx, target, index);
}

void glBlendEquationi(GLuint buf_, GLenum mode)
{
	GET_CONTEXT;
	ctx->glBlendEquationi(ctx, buf_, mode);
}

void glBlendEquationSeparatei(GLuint buf_, GLenum modeRGB, GLenum modeAlpha)
{
	GET_CONTEXT;
	ctx->glBlendEquationSeparatei(ctx, buf_, modeRGB, modeAlpha);
}

void glBlendFunci(GLuint buf_, GLenum src, GLenum dst)
{
	GET_CONTEXT;
	ctx->glBlendFunci(ctx, buf_, src, dst);
}

void glBlendFuncSeparatei(GLuint buf_, GLenum srcRGB, GLenum dstRGB, GLenum srcAlpha, GLenum dstAlpha)
{
	GET_CONTEXT;
	ctx->glBlendFuncSeparatei(ctx, buf_, srcRGB, dstRGB, srcAlpha, dstAlpha);
}

void glColorMaski(GLuint index, GLboolean r, GLboolean g, GLboolean b, GLboolean a)
{
	GET_CONTEXT;
	ctx->glColorMaski(ctx, index, r, g, b, a);
}

GLboolean glIsEnabledi(GLenum target, GLuint index)
{
	GET_CONTEXT;
	return ctx->glIsEnabledi(ctx, target, index);
}

void glDrawElementsBaseVertex(GLenum mode, GLsizei count, GLenum type, const void* indices, GLint basevertex)
{
	GET_CONTEXT;
	ctx->glDrawElementsBaseVertex(ctx, mode, count, type, indices, basevertex);
}

void glDrawRangeElementsBaseVertex(GLenum mode, GLuint start, GLuint end, GLsizei count, GLenum type, const void* indices, GLint basevertex)
{
	GET_CONTEXT;
	ctx->glDrawRangeElementsBaseVertex(ctx, mode, start, end, count, type, indices, basevertex);
}

void glDrawElementsInstancedBaseVertex(GLenum mode, GLsizei count, GLenum type, const void* indices, GLsizei instancecount, GLint basevertex)
{
	GET_CONTEXT;
	ctx->glDrawElementsInstancedBaseVertex(ctx, mode, count, type, indices, instancecount, basevertex);
}

void glFramebufferTexture(GLenum target, GLenum attachment, GLuint texture, GLint level)
{
	GET_CONTEXT;
	ctx->glFramebufferTexture(ctx, target, attachment, texture, level);
}

void glPrimitiveBoundingBox(GLfloat minGL_ENTRY, GLfloat minY, GLfloat minZ, GLfloat minW, GLfloat maxX, GLfloat maxY, GLfloat maxZ, GLfloat maxW)
{
	GET_CONTEXT;
	ctx->glPrimitiveBoundingBox(ctx, minGL_ENTRY, minY, minZ, minW, maxX, maxY, maxZ, maxW);
}

GLenum glGetGraphicsResetStatus()
{
	GET_CONTEXT;
	return ctx->glGetGraphicsResetStatus(ctx);
}

void glReadnPixels(GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type, GLsizei bufSize, void* data)
{
	GET_CONTEXT;
	ctx->glReadnPixels(ctx, x, y, width, height, format, type, bufSize, data);
}

void glGetnUniformfv(GLuint program, GLint location, GLsizei bufSize, GLfloat* params)
{
	GET_CONTEXT;
	ctx->glGetnUniformfv(ctx, program, location, bufSize, params);
}

void glGetnUniformiv(GLuint program, GLint location, GLsizei bufSize, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetnUniformiv(ctx, program, location, bufSize, params);
}

void glGetnUniformuiv(GLuint program, GLint location, GLsizei bufSize, GLuint* params)
{
	GET_CONTEXT;
	ctx->glGetnUniformuiv(ctx, program, location, bufSize, params);
}

void glMinSampleShading(GLfloat value)
{
	GET_CONTEXT;
	ctx->glMinSampleShading(ctx, value);
}

void glPatchParameteri(GLenum pname, GLint value)
{
	GET_CONTEXT;
	ctx->glPatchParameteri(ctx, pname, value);
}

void glTexParameterIiv(GLenum target, GLenum pname, const GLint* params)
{
	GET_CONTEXT;
	ctx->glTexParameterIiv(ctx, target, pname, params);
}

void glTexParameterIuiv(GLenum target, GLenum pname, const GLuint* params)
{
	GET_CONTEXT;
	ctx->glTexParameterIuiv(ctx, target, pname, params);
}

void glGetTexParameterIiv(GLenum target, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetTexParameterIiv(ctx, target, pname, params);
}

void glGetTexParameterIuiv(GLenum target, GLenum pname, GLuint* params)
{
	GET_CONTEXT;
	ctx->glGetTexParameterIuiv(ctx, target, pname, params);
}

void glSamplerParameterIiv(GLuint sampler, GLenum pname, const GLint* param)
{
	GET_CONTEXT;
	ctx->glSamplerParameterIiv(ctx, sampler, pname, param);
}

void glSamplerParameterIuiv(GLuint sampler, GLenum pname, const GLuint* param)
{
	GET_CONTEXT;
	ctx->glSamplerParameterIuiv(ctx, sampler, pname, param);
}

void glGetSamplerParameterIiv(GLuint sampler, GLenum pname, GLint* params)
{
	GET_CONTEXT;
	ctx->glGetSamplerParameterIiv(ctx, sampler, pname, params);
}

void glGetSamplerParameterIuiv(GLuint sampler, GLenum pname, GLuint* params)
{
	GET_CONTEXT;
	ctx->glGetSamplerParameterIuiv(ctx, sampler, pname, params);
}

void glTexBuffer(GLenum target, GLenum internalformat, GLuint buffer)
{
	GET_CONTEXT;
	ctx->glTexBuffer(ctx, target, internalformat, buffer);
}

void glTexBufferRange(GLenum target, GLenum internalformat, GLuint buffer, GLintptr offset, GLsizeiptr size)
{
	GET_CONTEXT;
	ctx->glTexBufferRange(ctx, target, internalformat, buffer, offset, size);
}

void glTexStorage3DMultisample(GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLboolean fixedsamplelocations)
{
	GET_CONTEXT;
	ctx->glTexStorage3DMultisample(ctx, target, samples, internalformat, width, height, depth, fixedsamplelocations);
}

void glCopyImageSubDataEXT(GLuint srcName, GLenum srcTarget, GLint srcLevel, GLint srcX, GLint srcY, GLint srcZ, GLuint dstName, GLenum dstTarget, GLint dstLevel, GLint dstX, GLint dstY, GLint dstZ, GLsizei srcWidth, GLsizei srcHeight, GLsizei srcDepth)
{
	GET_CONTEXT;
	ctx->glCopyImageSubDataEXT(ctx, srcName, srcTarget, srcLevel, srcX, srcY, srcZ, dstName, dstTarget, dstLevel, dstX, dstY, dstZ, srcWidth, srcHeight, srcDepth);
}

void glDrawElementsBaseVertexOffset(GLenum mode, GLsizei count, GLenum type, GLuint offset, GLint basevertex)
{
	GET_CONTEXT;
	ctx->glDrawElementsBaseVertexOffset(ctx, mode, count, type, offset, basevertex);
}

void glBindFragDataLocationEXT(GLuint program, GLuint color, const GLchar* name)
{
	GET_CONTEXT;
	ctx->glBindFragDataLocationEXT(ctx, program, color, name);
}

void glBindFragDataLocationIndexedEXT(GLuint program, GLuint colorNumber, GLuint index, const GLchar* name)
{
	GET_CONTEXT;
	ctx->glBindFragDataLocationIndexedEXT(ctx, program, colorNumber, index, name);
}

GLint glGetFragDataIndexEXT(GLuint program, const GLchar* name)
{
	GET_CONTEXT;
	return ctx->glGetFragDataIndexEXT(ctx, program, name);
}

GLint glGetProgramResourceLocationIndexEXT(GLuint program, GLenum programInterface, const GLchar* name)
{
	GET_CONTEXT;
	return ctx->glGetProgramResourceLocationIndexEXT(ctx, program, programInterface, name);
}

void glFramebufferTexture2DMultisampleEXT(GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level, GLsizei samples)
{
	GET_CONTEXT;
	ctx->glFramebufferTexture2DMultisampleEXT(ctx, target, attachment, textarget, texture, level, samples);
}

void glRenderbufferStorageMultisampleEXT(GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height)
{
	GET_CONTEXT;
	ctx->glRenderbufferStorageMultisampleEXT(ctx, target, samples, internalformat, width, height);
}

void glTexStorage2DEXT(GLenum target, GLsizei levels, GLenum internalformat, GLsizei width, GLsizei height)
{
	GET_CONTEXT;
	ctx->glTexStorage2DEXT(ctx, target, levels, internalformat, width, height);
}

void glTexStorage3DEXT(GLenum target, GLsizei levels, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth)
{
	GET_CONTEXT;
	ctx->glTexStorage3DEXT(ctx, target, levels, internalformat, width, height, depth);
}

void* glMapBufferRangeEXT(GLenum target, GLintptr offset, GLsizeiptr length, GLbitfield access)
{
	GET_CONTEXT;
	return ctx->glMapBufferRangeEXT(ctx, target, offset, length, access);
}

void glFlushMappedBufferRangeEXT(GLenum target, GLintptr offset, GLsizeiptr length)
{
	GET_CONTEXT;
	ctx->glFlushMappedBufferRangeEXT(ctx, target, offset, length);
}

void glDebugMessageInsert(GLenum source, GLenum type, GLuint id, GLenum severity, GLsizei length, const GLchar* buffer)
{
	GET_CONTEXT;
	ctx->glDebugMessageInsert(ctx, source, type, id, severity, length, buffer);
}

void glBlendBarrierKHR()
{
	GET_CONTEXT;
	ctx->glBlendBarrierKHR(ctx);
}
