// Generated Code - DO NOT EDIT !!
// generated by 'emugen'


#include <string.h>
#include "gl2_client_context.h"


#include <stdio.h>

int gl2_client_context_t::initDispatchByName(void *(*getProc)(const char *, void *userData), void *userData)
{
	glActiveTexture = (glActiveTexture_client_proc_t) getProc("glActiveTexture", userData);
	glAttachShader = (glAttachShader_client_proc_t) getProc("glAttachShader", userData);
	glBindAttribLocation = (glBindAttribLocation_client_proc_t) getProc("glBindAttribLocation", userData);
	glBindBuffer = (glBindBuffer_client_proc_t) getProc("glBindBuffer", userData);
	glBindFramebuffer = (glBindFramebuffer_client_proc_t) getProc("glBindFramebuffer", userData);
	glBindRenderbuffer = (glBindRenderbuffer_client_proc_t) getProc("glBindRenderbuffer", userData);
	glBindTexture = (glBindTexture_client_proc_t) getProc("glBindTexture", userData);
	glBlendColor = (glBlendColor_client_proc_t) getProc("glBlendColor", userData);
	glBlendEquation = (glBlendEquation_client_proc_t) getProc("glBlendEquation", userData);
	glBlendEquationSeparate = (glBlendEquationSeparate_client_proc_t) getProc("glBlendEquationSeparate", userData);
	glBlendFunc = (glBlendFunc_client_proc_t) getProc("glBlendFunc", userData);
	glBlendFuncSeparate = (glBlendFuncSeparate_client_proc_t) getProc("glBlendFuncSeparate", userData);
	glBufferData = (glBufferData_client_proc_t) getProc("glBufferData", userData);
	glBufferSubData = (glBufferSubData_client_proc_t) getProc("glBufferSubData", userData);
	glCheckFramebufferStatus = (glCheckFramebufferStatus_client_proc_t) getProc("glCheckFramebufferStatus", userData);
	glClear = (glClear_client_proc_t) getProc("glClear", userData);
	glClearColor = (glClearColor_client_proc_t) getProc("glClearColor", userData);
	glClearDepthf = (glClearDepthf_client_proc_t) getProc("glClearDepthf", userData);
	glClearStencil = (glClearStencil_client_proc_t) getProc("glClearStencil", userData);
	glColorMask = (glColorMask_client_proc_t) getProc("glColorMask", userData);
	glCompileShader = (glCompileShader_client_proc_t) getProc("glCompileShader", userData);
	glCompressedTexImage2D = (glCompressedTexImage2D_client_proc_t) getProc("glCompressedTexImage2D", userData);
	glCompressedTexSubImage2D = (glCompressedTexSubImage2D_client_proc_t) getProc("glCompressedTexSubImage2D", userData);
	glCopyTexImage2D = (glCopyTexImage2D_client_proc_t) getProc("glCopyTexImage2D", userData);
	glCopyTexSubImage2D = (glCopyTexSubImage2D_client_proc_t) getProc("glCopyTexSubImage2D", userData);
	glCreateProgram = (glCreateProgram_client_proc_t) getProc("glCreateProgram", userData);
	glCreateShader = (glCreateShader_client_proc_t) getProc("glCreateShader", userData);
	glCullFace = (glCullFace_client_proc_t) getProc("glCullFace", userData);
	glDeleteBuffers = (glDeleteBuffers_client_proc_t) getProc("glDeleteBuffers", userData);
	glDeleteFramebuffers = (glDeleteFramebuffers_client_proc_t) getProc("glDeleteFramebuffers", userData);
	glDeleteProgram = (glDeleteProgram_client_proc_t) getProc("glDeleteProgram", userData);
	glDeleteRenderbuffers = (glDeleteRenderbuffers_client_proc_t) getProc("glDeleteRenderbuffers", userData);
	glDeleteShader = (glDeleteShader_client_proc_t) getProc("glDeleteShader", userData);
	glDeleteTextures = (glDeleteTextures_client_proc_t) getProc("glDeleteTextures", userData);
	glDepthFunc = (glDepthFunc_client_proc_t) getProc("glDepthFunc", userData);
	glDepthMask = (glDepthMask_client_proc_t) getProc("glDepthMask", userData);
	glDepthRangef = (glDepthRangef_client_proc_t) getProc("glDepthRangef", userData);
	glDetachShader = (glDetachShader_client_proc_t) getProc("glDetachShader", userData);
	glDisable = (glDisable_client_proc_t) getProc("glDisable", userData);
	glDisableVertexAttribArray = (glDisableVertexAttribArray_client_proc_t) getProc("glDisableVertexAttribArray", userData);
	glDrawArrays = (glDrawArrays_client_proc_t) getProc("glDrawArrays", userData);
	glDrawElements = (glDrawElements_client_proc_t) getProc("glDrawElements", userData);
	glEnable = (glEnable_client_proc_t) getProc("glEnable", userData);
	glEnableVertexAttribArray = (glEnableVertexAttribArray_client_proc_t) getProc("glEnableVertexAttribArray", userData);
	glFinish = (glFinish_client_proc_t) getProc("glFinish", userData);
	glFlush = (glFlush_client_proc_t) getProc("glFlush", userData);
	glFramebufferRenderbuffer = (glFramebufferRenderbuffer_client_proc_t) getProc("glFramebufferRenderbuffer", userData);
	glFramebufferTexture2D = (glFramebufferTexture2D_client_proc_t) getProc("glFramebufferTexture2D", userData);
	glFrontFace = (glFrontFace_client_proc_t) getProc("glFrontFace", userData);
	glGenBuffers = (glGenBuffers_client_proc_t) getProc("glGenBuffers", userData);
	glGenerateMipmap = (glGenerateMipmap_client_proc_t) getProc("glGenerateMipmap", userData);
	glGenFramebuffers = (glGenFramebuffers_client_proc_t) getProc("glGenFramebuffers", userData);
	glGenRenderbuffers = (glGenRenderbuffers_client_proc_t) getProc("glGenRenderbuffers", userData);
	glGenTextures = (glGenTextures_client_proc_t) getProc("glGenTextures", userData);
	glGetActiveAttrib = (glGetActiveAttrib_client_proc_t) getProc("glGetActiveAttrib", userData);
	glGetActiveUniform = (glGetActiveUniform_client_proc_t) getProc("glGetActiveUniform", userData);
	glGetAttachedShaders = (glGetAttachedShaders_client_proc_t) getProc("glGetAttachedShaders", userData);
	glGetAttribLocation = (glGetAttribLocation_client_proc_t) getProc("glGetAttribLocation", userData);
	glGetBooleanv = (glGetBooleanv_client_proc_t) getProc("glGetBooleanv", userData);
	glGetBufferParameteriv = (glGetBufferParameteriv_client_proc_t) getProc("glGetBufferParameteriv", userData);
	glGetError = (glGetError_client_proc_t) getProc("glGetError", userData);
	glGetFloatv = (glGetFloatv_client_proc_t) getProc("glGetFloatv", userData);
	glGetFramebufferAttachmentParameteriv = (glGetFramebufferAttachmentParameteriv_client_proc_t) getProc("glGetFramebufferAttachmentParameteriv", userData);
	glGetIntegerv = (glGetIntegerv_client_proc_t) getProc("glGetIntegerv", userData);
	glGetProgramiv = (glGetProgramiv_client_proc_t) getProc("glGetProgramiv", userData);
	glGetProgramInfoLog = (glGetProgramInfoLog_client_proc_t) getProc("glGetProgramInfoLog", userData);
	glGetRenderbufferParameteriv = (glGetRenderbufferParameteriv_client_proc_t) getProc("glGetRenderbufferParameteriv", userData);
	glGetShaderiv = (glGetShaderiv_client_proc_t) getProc("glGetShaderiv", userData);
	glGetShaderInfoLog = (glGetShaderInfoLog_client_proc_t) getProc("glGetShaderInfoLog", userData);
	glGetShaderPrecisionFormat = (glGetShaderPrecisionFormat_client_proc_t) getProc("glGetShaderPrecisionFormat", userData);
	glGetShaderSource = (glGetShaderSource_client_proc_t) getProc("glGetShaderSource", userData);
	glGetString = (glGetString_client_proc_t) getProc("glGetString", userData);
	glGetTexParameterfv = (glGetTexParameterfv_client_proc_t) getProc("glGetTexParameterfv", userData);
	glGetTexParameteriv = (glGetTexParameteriv_client_proc_t) getProc("glGetTexParameteriv", userData);
	glGetUniformfv = (glGetUniformfv_client_proc_t) getProc("glGetUniformfv", userData);
	glGetUniformiv = (glGetUniformiv_client_proc_t) getProc("glGetUniformiv", userData);
	glGetUniformLocation = (glGetUniformLocation_client_proc_t) getProc("glGetUniformLocation", userData);
	glGetVertexAttribfv = (glGetVertexAttribfv_client_proc_t) getProc("glGetVertexAttribfv", userData);
	glGetVertexAttribiv = (glGetVertexAttribiv_client_proc_t) getProc("glGetVertexAttribiv", userData);
	glGetVertexAttribPointerv = (glGetVertexAttribPointerv_client_proc_t) getProc("glGetVertexAttribPointerv", userData);
	glHint = (glHint_client_proc_t) getProc("glHint", userData);
	glIsBuffer = (glIsBuffer_client_proc_t) getProc("glIsBuffer", userData);
	glIsEnabled = (glIsEnabled_client_proc_t) getProc("glIsEnabled", userData);
	glIsFramebuffer = (glIsFramebuffer_client_proc_t) getProc("glIsFramebuffer", userData);
	glIsProgram = (glIsProgram_client_proc_t) getProc("glIsProgram", userData);
	glIsRenderbuffer = (glIsRenderbuffer_client_proc_t) getProc("glIsRenderbuffer", userData);
	glIsShader = (glIsShader_client_proc_t) getProc("glIsShader", userData);
	glIsTexture = (glIsTexture_client_proc_t) getProc("glIsTexture", userData);
	glLineWidth = (glLineWidth_client_proc_t) getProc("glLineWidth", userData);
	glLinkProgram = (glLinkProgram_client_proc_t) getProc("glLinkProgram", userData);
	glPixelStorei = (glPixelStorei_client_proc_t) getProc("glPixelStorei", userData);
	glPolygonOffset = (glPolygonOffset_client_proc_t) getProc("glPolygonOffset", userData);
	glReadPixels = (glReadPixels_client_proc_t) getProc("glReadPixels", userData);
	glReleaseShaderCompiler = (glReleaseShaderCompiler_client_proc_t) getProc("glReleaseShaderCompiler", userData);
	glRenderbufferStorage = (glRenderbufferStorage_client_proc_t) getProc("glRenderbufferStorage", userData);
	glSampleCoverage = (glSampleCoverage_client_proc_t) getProc("glSampleCoverage", userData);
	glScissor = (glScissor_client_proc_t) getProc("glScissor", userData);
	glShaderBinary = (glShaderBinary_client_proc_t) getProc("glShaderBinary", userData);
	glShaderSource = (glShaderSource_client_proc_t) getProc("glShaderSource", userData);
	glStencilFunc = (glStencilFunc_client_proc_t) getProc("glStencilFunc", userData);
	glStencilFuncSeparate = (glStencilFuncSeparate_client_proc_t) getProc("glStencilFuncSeparate", userData);
	glStencilMask = (glStencilMask_client_proc_t) getProc("glStencilMask", userData);
	glStencilMaskSeparate = (glStencilMaskSeparate_client_proc_t) getProc("glStencilMaskSeparate", userData);
	glStencilOp = (glStencilOp_client_proc_t) getProc("glStencilOp", userData);
	glStencilOpSeparate = (glStencilOpSeparate_client_proc_t) getProc("glStencilOpSeparate", userData);
	glTexImage2D = (glTexImage2D_client_proc_t) getProc("glTexImage2D", userData);
	glTexParameterf = (glTexParameterf_client_proc_t) getProc("glTexParameterf", userData);
	glTexParameterfv = (glTexParameterfv_client_proc_t) getProc("glTexParameterfv", userData);
	glTexParameteri = (glTexParameteri_client_proc_t) getProc("glTexParameteri", userData);
	glTexParameteriv = (glTexParameteriv_client_proc_t) getProc("glTexParameteriv", userData);
	glTexSubImage2D = (glTexSubImage2D_client_proc_t) getProc("glTexSubImage2D", userData);
	glUniform1f = (glUniform1f_client_proc_t) getProc("glUniform1f", userData);
	glUniform1fv = (glUniform1fv_client_proc_t) getProc("glUniform1fv", userData);
	glUniform1i = (glUniform1i_client_proc_t) getProc("glUniform1i", userData);
	glUniform1iv = (glUniform1iv_client_proc_t) getProc("glUniform1iv", userData);
	glUniform2f = (glUniform2f_client_proc_t) getProc("glUniform2f", userData);
	glUniform2fv = (glUniform2fv_client_proc_t) getProc("glUniform2fv", userData);
	glUniform2i = (glUniform2i_client_proc_t) getProc("glUniform2i", userData);
	glUniform2iv = (glUniform2iv_client_proc_t) getProc("glUniform2iv", userData);
	glUniform3f = (glUniform3f_client_proc_t) getProc("glUniform3f", userData);
	glUniform3fv = (glUniform3fv_client_proc_t) getProc("glUniform3fv", userData);
	glUniform3i = (glUniform3i_client_proc_t) getProc("glUniform3i", userData);
	glUniform3iv = (glUniform3iv_client_proc_t) getProc("glUniform3iv", userData);
	glUniform4f = (glUniform4f_client_proc_t) getProc("glUniform4f", userData);
	glUniform4fv = (glUniform4fv_client_proc_t) getProc("glUniform4fv", userData);
	glUniform4i = (glUniform4i_client_proc_t) getProc("glUniform4i", userData);
	glUniform4iv = (glUniform4iv_client_proc_t) getProc("glUniform4iv", userData);
	glUniformMatrix2fv = (glUniformMatrix2fv_client_proc_t) getProc("glUniformMatrix2fv", userData);
	glUniformMatrix3fv = (glUniformMatrix3fv_client_proc_t) getProc("glUniformMatrix3fv", userData);
	glUniformMatrix4fv = (glUniformMatrix4fv_client_proc_t) getProc("glUniformMatrix4fv", userData);
	glUseProgram = (glUseProgram_client_proc_t) getProc("glUseProgram", userData);
	glValidateProgram = (glValidateProgram_client_proc_t) getProc("glValidateProgram", userData);
	glVertexAttrib1f = (glVertexAttrib1f_client_proc_t) getProc("glVertexAttrib1f", userData);
	glVertexAttrib1fv = (glVertexAttrib1fv_client_proc_t) getProc("glVertexAttrib1fv", userData);
	glVertexAttrib2f = (glVertexAttrib2f_client_proc_t) getProc("glVertexAttrib2f", userData);
	glVertexAttrib2fv = (glVertexAttrib2fv_client_proc_t) getProc("glVertexAttrib2fv", userData);
	glVertexAttrib3f = (glVertexAttrib3f_client_proc_t) getProc("glVertexAttrib3f", userData);
	glVertexAttrib3fv = (glVertexAttrib3fv_client_proc_t) getProc("glVertexAttrib3fv", userData);
	glVertexAttrib4f = (glVertexAttrib4f_client_proc_t) getProc("glVertexAttrib4f", userData);
	glVertexAttrib4fv = (glVertexAttrib4fv_client_proc_t) getProc("glVertexAttrib4fv", userData);
	glVertexAttribPointer = (glVertexAttribPointer_client_proc_t) getProc("glVertexAttribPointer", userData);
	glViewport = (glViewport_client_proc_t) getProc("glViewport", userData);
	glEGLImageTargetTexture2DOES = (glEGLImageTargetTexture2DOES_client_proc_t) getProc("glEGLImageTargetTexture2DOES", userData);
	glEGLImageTargetRenderbufferStorageOES = (glEGLImageTargetRenderbufferStorageOES_client_proc_t) getProc("glEGLImageTargetRenderbufferStorageOES", userData);
	glGetProgramBinaryOES = (glGetProgramBinaryOES_client_proc_t) getProc("glGetProgramBinaryOES", userData);
	glProgramBinaryOES = (glProgramBinaryOES_client_proc_t) getProc("glProgramBinaryOES", userData);
	glMapBufferOES = (glMapBufferOES_client_proc_t) getProc("glMapBufferOES", userData);
	glUnmapBufferOES = (glUnmapBufferOES_client_proc_t) getProc("glUnmapBufferOES", userData);
	glTexImage3DOES = (glTexImage3DOES_client_proc_t) getProc("glTexImage3DOES", userData);
	glTexSubImage3DOES = (glTexSubImage3DOES_client_proc_t) getProc("glTexSubImage3DOES", userData);
	glCopyTexSubImage3DOES = (glCopyTexSubImage3DOES_client_proc_t) getProc("glCopyTexSubImage3DOES", userData);
	glCompressedTexImage3DOES = (glCompressedTexImage3DOES_client_proc_t) getProc("glCompressedTexImage3DOES", userData);
	glCompressedTexSubImage3DOES = (glCompressedTexSubImage3DOES_client_proc_t) getProc("glCompressedTexSubImage3DOES", userData);
	glFramebufferTexture3DOES = (glFramebufferTexture3DOES_client_proc_t) getProc("glFramebufferTexture3DOES", userData);
	glBindVertexArrayOES = (glBindVertexArrayOES_client_proc_t) getProc("glBindVertexArrayOES", userData);
	glDeleteVertexArraysOES = (glDeleteVertexArraysOES_client_proc_t) getProc("glDeleteVertexArraysOES", userData);
	glGenVertexArraysOES = (glGenVertexArraysOES_client_proc_t) getProc("glGenVertexArraysOES", userData);
	glIsVertexArrayOES = (glIsVertexArrayOES_client_proc_t) getProc("glIsVertexArrayOES", userData);
	glDiscardFramebufferEXT = (glDiscardFramebufferEXT_client_proc_t) getProc("glDiscardFramebufferEXT", userData);
	glMultiDrawArraysEXT = (glMultiDrawArraysEXT_client_proc_t) getProc("glMultiDrawArraysEXT", userData);
	glMultiDrawElementsEXT = (glMultiDrawElementsEXT_client_proc_t) getProc("glMultiDrawElementsEXT", userData);
	glGetPerfMonitorGroupsAMD = (glGetPerfMonitorGroupsAMD_client_proc_t) getProc("glGetPerfMonitorGroupsAMD", userData);
	glGetPerfMonitorCountersAMD = (glGetPerfMonitorCountersAMD_client_proc_t) getProc("glGetPerfMonitorCountersAMD", userData);
	glGetPerfMonitorGroupStringAMD = (glGetPerfMonitorGroupStringAMD_client_proc_t) getProc("glGetPerfMonitorGroupStringAMD", userData);
	glGetPerfMonitorCounterStringAMD = (glGetPerfMonitorCounterStringAMD_client_proc_t) getProc("glGetPerfMonitorCounterStringAMD", userData);
	glGetPerfMonitorCounterInfoAMD = (glGetPerfMonitorCounterInfoAMD_client_proc_t) getProc("glGetPerfMonitorCounterInfoAMD", userData);
	glGenPerfMonitorsAMD = (glGenPerfMonitorsAMD_client_proc_t) getProc("glGenPerfMonitorsAMD", userData);
	glDeletePerfMonitorsAMD = (glDeletePerfMonitorsAMD_client_proc_t) getProc("glDeletePerfMonitorsAMD", userData);
	glSelectPerfMonitorCountersAMD = (glSelectPerfMonitorCountersAMD_client_proc_t) getProc("glSelectPerfMonitorCountersAMD", userData);
	glBeginPerfMonitorAMD = (glBeginPerfMonitorAMD_client_proc_t) getProc("glBeginPerfMonitorAMD", userData);
	glEndPerfMonitorAMD = (glEndPerfMonitorAMD_client_proc_t) getProc("glEndPerfMonitorAMD", userData);
	glGetPerfMonitorCounterDataAMD = (glGetPerfMonitorCounterDataAMD_client_proc_t) getProc("glGetPerfMonitorCounterDataAMD", userData);
	glRenderbufferStorageMultisampleIMG = (glRenderbufferStorageMultisampleIMG_client_proc_t) getProc("glRenderbufferStorageMultisampleIMG", userData);
	glFramebufferTexture2DMultisampleIMG = (glFramebufferTexture2DMultisampleIMG_client_proc_t) getProc("glFramebufferTexture2DMultisampleIMG", userData);
	glDeleteFencesNV = (glDeleteFencesNV_client_proc_t) getProc("glDeleteFencesNV", userData);
	glGenFencesNV = (glGenFencesNV_client_proc_t) getProc("glGenFencesNV", userData);
	glIsFenceNV = (glIsFenceNV_client_proc_t) getProc("glIsFenceNV", userData);
	glTestFenceNV = (glTestFenceNV_client_proc_t) getProc("glTestFenceNV", userData);
	glGetFenceivNV = (glGetFenceivNV_client_proc_t) getProc("glGetFenceivNV", userData);
	glFinishFenceNV = (glFinishFenceNV_client_proc_t) getProc("glFinishFenceNV", userData);
	glSetFenceNV = (glSetFenceNV_client_proc_t) getProc("glSetFenceNV", userData);
	glCoverageMaskNV = (glCoverageMaskNV_client_proc_t) getProc("glCoverageMaskNV", userData);
	glCoverageOperationNV = (glCoverageOperationNV_client_proc_t) getProc("glCoverageOperationNV", userData);
	glGetDriverControlsQCOM = (glGetDriverControlsQCOM_client_proc_t) getProc("glGetDriverControlsQCOM", userData);
	glGetDriverControlStringQCOM = (glGetDriverControlStringQCOM_client_proc_t) getProc("glGetDriverControlStringQCOM", userData);
	glEnableDriverControlQCOM = (glEnableDriverControlQCOM_client_proc_t) getProc("glEnableDriverControlQCOM", userData);
	glDisableDriverControlQCOM = (glDisableDriverControlQCOM_client_proc_t) getProc("glDisableDriverControlQCOM", userData);
	glExtGetTexturesQCOM = (glExtGetTexturesQCOM_client_proc_t) getProc("glExtGetTexturesQCOM", userData);
	glExtGetBuffersQCOM = (glExtGetBuffersQCOM_client_proc_t) getProc("glExtGetBuffersQCOM", userData);
	glExtGetRenderbuffersQCOM = (glExtGetRenderbuffersQCOM_client_proc_t) getProc("glExtGetRenderbuffersQCOM", userData);
	glExtGetFramebuffersQCOM = (glExtGetFramebuffersQCOM_client_proc_t) getProc("glExtGetFramebuffersQCOM", userData);
	glExtGetTexLevelParameterivQCOM = (glExtGetTexLevelParameterivQCOM_client_proc_t) getProc("glExtGetTexLevelParameterivQCOM", userData);
	glExtTexObjectStateOverrideiQCOM = (glExtTexObjectStateOverrideiQCOM_client_proc_t) getProc("glExtTexObjectStateOverrideiQCOM", userData);
	glExtGetTexSubImageQCOM = (glExtGetTexSubImageQCOM_client_proc_t) getProc("glExtGetTexSubImageQCOM", userData);
	glExtGetBufferPointervQCOM = (glExtGetBufferPointervQCOM_client_proc_t) getProc("glExtGetBufferPointervQCOM", userData);
	glExtGetShadersQCOM = (glExtGetShadersQCOM_client_proc_t) getProc("glExtGetShadersQCOM", userData);
	glExtGetProgramsQCOM = (glExtGetProgramsQCOM_client_proc_t) getProc("glExtGetProgramsQCOM", userData);
	glExtIsProgramBinaryQCOM = (glExtIsProgramBinaryQCOM_client_proc_t) getProc("glExtIsProgramBinaryQCOM", userData);
	glExtGetProgramBinarySourceQCOM = (glExtGetProgramBinarySourceQCOM_client_proc_t) getProc("glExtGetProgramBinarySourceQCOM", userData);
	glStartTilingQCOM = (glStartTilingQCOM_client_proc_t) getProc("glStartTilingQCOM", userData);
	glEndTilingQCOM = (glEndTilingQCOM_client_proc_t) getProc("glEndTilingQCOM", userData);
	glVertexAttribPointerData = (glVertexAttribPointerData_client_proc_t) getProc("glVertexAttribPointerData", userData);
	glVertexAttribPointerOffset = (glVertexAttribPointerOffset_client_proc_t) getProc("glVertexAttribPointerOffset", userData);
	glDrawElementsOffset = (glDrawElementsOffset_client_proc_t) getProc("glDrawElementsOffset", userData);
	glDrawElementsData = (glDrawElementsData_client_proc_t) getProc("glDrawElementsData", userData);
	glGetCompressedTextureFormats = (glGetCompressedTextureFormats_client_proc_t) getProc("glGetCompressedTextureFormats", userData);
	glShaderString = (glShaderString_client_proc_t) getProc("glShaderString", userData);
	glFinishRoundTrip = (glFinishRoundTrip_client_proc_t) getProc("glFinishRoundTrip", userData);
	glGenVertexArrays = (glGenVertexArrays_client_proc_t) getProc("glGenVertexArrays", userData);
	glBindVertexArray = (glBindVertexArray_client_proc_t) getProc("glBindVertexArray", userData);
	glDeleteVertexArrays = (glDeleteVertexArrays_client_proc_t) getProc("glDeleteVertexArrays", userData);
	glIsVertexArray = (glIsVertexArray_client_proc_t) getProc("glIsVertexArray", userData);
	glMapBufferRange = (glMapBufferRange_client_proc_t) getProc("glMapBufferRange", userData);
	glUnmapBuffer = (glUnmapBuffer_client_proc_t) getProc("glUnmapBuffer", userData);
	glFlushMappedBufferRange = (glFlushMappedBufferRange_client_proc_t) getProc("glFlushMappedBufferRange", userData);
	glMapBufferRangeAEMU = (glMapBufferRangeAEMU_client_proc_t) getProc("glMapBufferRangeAEMU", userData);
	glUnmapBufferAEMU = (glUnmapBufferAEMU_client_proc_t) getProc("glUnmapBufferAEMU", userData);
	glFlushMappedBufferRangeAEMU = (glFlushMappedBufferRangeAEMU_client_proc_t) getProc("glFlushMappedBufferRangeAEMU", userData);
	glReadPixelsOffsetAEMU = (glReadPixelsOffsetAEMU_client_proc_t) getProc("glReadPixelsOffsetAEMU", userData);
	glCompressedTexImage2DOffsetAEMU = (glCompressedTexImage2DOffsetAEMU_client_proc_t) getProc("glCompressedTexImage2DOffsetAEMU", userData);
	glCompressedTexSubImage2DOffsetAEMU = (glCompressedTexSubImage2DOffsetAEMU_client_proc_t) getProc("glCompressedTexSubImage2DOffsetAEMU", userData);
	glTexImage2DOffsetAEMU = (glTexImage2DOffsetAEMU_client_proc_t) getProc("glTexImage2DOffsetAEMU", userData);
	glTexSubImage2DOffsetAEMU = (glTexSubImage2DOffsetAEMU_client_proc_t) getProc("glTexSubImage2DOffsetAEMU", userData);
	glBindBufferRange = (glBindBufferRange_client_proc_t) getProc("glBindBufferRange", userData);
	glBindBufferBase = (glBindBufferBase_client_proc_t) getProc("glBindBufferBase", userData);
	glCopyBufferSubData = (glCopyBufferSubData_client_proc_t) getProc("glCopyBufferSubData", userData);
	glClearBufferiv = (glClearBufferiv_client_proc_t) getProc("glClearBufferiv", userData);
	glClearBufferuiv = (glClearBufferuiv_client_proc_t) getProc("glClearBufferuiv", userData);
	glClearBufferfv = (glClearBufferfv_client_proc_t) getProc("glClearBufferfv", userData);
	glClearBufferfi = (glClearBufferfi_client_proc_t) getProc("glClearBufferfi", userData);
	glGetBufferParameteri64v = (glGetBufferParameteri64v_client_proc_t) getProc("glGetBufferParameteri64v", userData);
	glGetBufferPointerv = (glGetBufferPointerv_client_proc_t) getProc("glGetBufferPointerv", userData);
	glUniformBlockBinding = (glUniformBlockBinding_client_proc_t) getProc("glUniformBlockBinding", userData);
	glGetUniformBlockIndex = (glGetUniformBlockIndex_client_proc_t) getProc("glGetUniformBlockIndex", userData);
	glGetUniformIndices = (glGetUniformIndices_client_proc_t) getProc("glGetUniformIndices", userData);
	glGetUniformIndicesAEMU = (glGetUniformIndicesAEMU_client_proc_t) getProc("glGetUniformIndicesAEMU", userData);
	glGetActiveUniformBlockiv = (glGetActiveUniformBlockiv_client_proc_t) getProc("glGetActiveUniformBlockiv", userData);
	glGetActiveUniformBlockName = (glGetActiveUniformBlockName_client_proc_t) getProc("glGetActiveUniformBlockName", userData);
	glUniform1ui = (glUniform1ui_client_proc_t) getProc("glUniform1ui", userData);
	glUniform2ui = (glUniform2ui_client_proc_t) getProc("glUniform2ui", userData);
	glUniform3ui = (glUniform3ui_client_proc_t) getProc("glUniform3ui", userData);
	glUniform4ui = (glUniform4ui_client_proc_t) getProc("glUniform4ui", userData);
	glUniform1uiv = (glUniform1uiv_client_proc_t) getProc("glUniform1uiv", userData);
	glUniform2uiv = (glUniform2uiv_client_proc_t) getProc("glUniform2uiv", userData);
	glUniform3uiv = (glUniform3uiv_client_proc_t) getProc("glUniform3uiv", userData);
	glUniform4uiv = (glUniform4uiv_client_proc_t) getProc("glUniform4uiv", userData);
	glUniformMatrix2x3fv = (glUniformMatrix2x3fv_client_proc_t) getProc("glUniformMatrix2x3fv", userData);
	glUniformMatrix3x2fv = (glUniformMatrix3x2fv_client_proc_t) getProc("glUniformMatrix3x2fv", userData);
	glUniformMatrix2x4fv = (glUniformMatrix2x4fv_client_proc_t) getProc("glUniformMatrix2x4fv", userData);
	glUniformMatrix4x2fv = (glUniformMatrix4x2fv_client_proc_t) getProc("glUniformMatrix4x2fv", userData);
	glUniformMatrix3x4fv = (glUniformMatrix3x4fv_client_proc_t) getProc("glUniformMatrix3x4fv", userData);
	glUniformMatrix4x3fv = (glUniformMatrix4x3fv_client_proc_t) getProc("glUniformMatrix4x3fv", userData);
	glGetUniformuiv = (glGetUniformuiv_client_proc_t) getProc("glGetUniformuiv", userData);
	glGetActiveUniformsiv = (glGetActiveUniformsiv_client_proc_t) getProc("glGetActiveUniformsiv", userData);
	glVertexAttribI4i = (glVertexAttribI4i_client_proc_t) getProc("glVertexAttribI4i", userData);
	glVertexAttribI4ui = (glVertexAttribI4ui_client_proc_t) getProc("glVertexAttribI4ui", userData);
	glVertexAttribI4iv = (glVertexAttribI4iv_client_proc_t) getProc("glVertexAttribI4iv", userData);
	glVertexAttribI4uiv = (glVertexAttribI4uiv_client_proc_t) getProc("glVertexAttribI4uiv", userData);
	glVertexAttribIPointer = (glVertexAttribIPointer_client_proc_t) getProc("glVertexAttribIPointer", userData);
	glVertexAttribIPointerOffsetAEMU = (glVertexAttribIPointerOffsetAEMU_client_proc_t) getProc("glVertexAttribIPointerOffsetAEMU", userData);
	glVertexAttribIPointerDataAEMU = (glVertexAttribIPointerDataAEMU_client_proc_t) getProc("glVertexAttribIPointerDataAEMU", userData);
	glGetVertexAttribIiv = (glGetVertexAttribIiv_client_proc_t) getProc("glGetVertexAttribIiv", userData);
	glGetVertexAttribIuiv = (glGetVertexAttribIuiv_client_proc_t) getProc("glGetVertexAttribIuiv", userData);
	glVertexAttribDivisor = (glVertexAttribDivisor_client_proc_t) getProc("glVertexAttribDivisor", userData);
	glDrawArraysInstanced = (glDrawArraysInstanced_client_proc_t) getProc("glDrawArraysInstanced", userData);
	glDrawElementsInstanced = (glDrawElementsInstanced_client_proc_t) getProc("glDrawElementsInstanced", userData);
	glDrawElementsInstancedDataAEMU = (glDrawElementsInstancedDataAEMU_client_proc_t) getProc("glDrawElementsInstancedDataAEMU", userData);
	glDrawElementsInstancedOffsetAEMU = (glDrawElementsInstancedOffsetAEMU_client_proc_t) getProc("glDrawElementsInstancedOffsetAEMU", userData);
	glDrawRangeElements = (glDrawRangeElements_client_proc_t) getProc("glDrawRangeElements", userData);
	glDrawRangeElementsDataAEMU = (glDrawRangeElementsDataAEMU_client_proc_t) getProc("glDrawRangeElementsDataAEMU", userData);
	glDrawRangeElementsOffsetAEMU = (glDrawRangeElementsOffsetAEMU_client_proc_t) getProc("glDrawRangeElementsOffsetAEMU", userData);
	glFenceSync = (glFenceSync_client_proc_t) getProc("glFenceSync", userData);
	glClientWaitSync = (glClientWaitSync_client_proc_t) getProc("glClientWaitSync", userData);
	glWaitSync = (glWaitSync_client_proc_t) getProc("glWaitSync", userData);
	glDeleteSync = (glDeleteSync_client_proc_t) getProc("glDeleteSync", userData);
	glIsSync = (glIsSync_client_proc_t) getProc("glIsSync", userData);
	glGetSynciv = (glGetSynciv_client_proc_t) getProc("glGetSynciv", userData);
	glFenceSyncAEMU = (glFenceSyncAEMU_client_proc_t) getProc("glFenceSyncAEMU", userData);
	glClientWaitSyncAEMU = (glClientWaitSyncAEMU_client_proc_t) getProc("glClientWaitSyncAEMU", userData);
	glWaitSyncAEMU = (glWaitSyncAEMU_client_proc_t) getProc("glWaitSyncAEMU", userData);
	glDeleteSyncAEMU = (glDeleteSyncAEMU_client_proc_t) getProc("glDeleteSyncAEMU", userData);
	glIsSyncAEMU = (glIsSyncAEMU_client_proc_t) getProc("glIsSyncAEMU", userData);
	glGetSyncivAEMU = (glGetSyncivAEMU_client_proc_t) getProc("glGetSyncivAEMU", userData);
	glDrawBuffers = (glDrawBuffers_client_proc_t) getProc("glDrawBuffers", userData);
	glReadBuffer = (glReadBuffer_client_proc_t) getProc("glReadBuffer", userData);
	glBlitFramebuffer = (glBlitFramebuffer_client_proc_t) getProc("glBlitFramebuffer", userData);
	glInvalidateFramebuffer = (glInvalidateFramebuffer_client_proc_t) getProc("glInvalidateFramebuffer", userData);
	glInvalidateSubFramebuffer = (glInvalidateSubFramebuffer_client_proc_t) getProc("glInvalidateSubFramebuffer", userData);
	glFramebufferTextureLayer = (glFramebufferTextureLayer_client_proc_t) getProc("glFramebufferTextureLayer", userData);
	glRenderbufferStorageMultisample = (glRenderbufferStorageMultisample_client_proc_t) getProc("glRenderbufferStorageMultisample", userData);
	glTexStorage2D = (glTexStorage2D_client_proc_t) getProc("glTexStorage2D", userData);
	glGetInternalformativ = (glGetInternalformativ_client_proc_t) getProc("glGetInternalformativ", userData);
	glBeginTransformFeedback = (glBeginTransformFeedback_client_proc_t) getProc("glBeginTransformFeedback", userData);
	glEndTransformFeedback = (glEndTransformFeedback_client_proc_t) getProc("glEndTransformFeedback", userData);
	glGenTransformFeedbacks = (glGenTransformFeedbacks_client_proc_t) getProc("glGenTransformFeedbacks", userData);
	glDeleteTransformFeedbacks = (glDeleteTransformFeedbacks_client_proc_t) getProc("glDeleteTransformFeedbacks", userData);
	glBindTransformFeedback = (glBindTransformFeedback_client_proc_t) getProc("glBindTransformFeedback", userData);
	glPauseTransformFeedback = (glPauseTransformFeedback_client_proc_t) getProc("glPauseTransformFeedback", userData);
	glResumeTransformFeedback = (glResumeTransformFeedback_client_proc_t) getProc("glResumeTransformFeedback", userData);
	glIsTransformFeedback = (glIsTransformFeedback_client_proc_t) getProc("glIsTransformFeedback", userData);
	glTransformFeedbackVaryings = (glTransformFeedbackVaryings_client_proc_t) getProc("glTransformFeedbackVaryings", userData);
	glTransformFeedbackVaryingsAEMU = (glTransformFeedbackVaryingsAEMU_client_proc_t) getProc("glTransformFeedbackVaryingsAEMU", userData);
	glGetTransformFeedbackVarying = (glGetTransformFeedbackVarying_client_proc_t) getProc("glGetTransformFeedbackVarying", userData);
	glGenSamplers = (glGenSamplers_client_proc_t) getProc("glGenSamplers", userData);
	glDeleteSamplers = (glDeleteSamplers_client_proc_t) getProc("glDeleteSamplers", userData);
	glBindSampler = (glBindSampler_client_proc_t) getProc("glBindSampler", userData);
	glSamplerParameterf = (glSamplerParameterf_client_proc_t) getProc("glSamplerParameterf", userData);
	glSamplerParameteri = (glSamplerParameteri_client_proc_t) getProc("glSamplerParameteri", userData);
	glSamplerParameterfv = (glSamplerParameterfv_client_proc_t) getProc("glSamplerParameterfv", userData);
	glSamplerParameteriv = (glSamplerParameteriv_client_proc_t) getProc("glSamplerParameteriv", userData);
	glGetSamplerParameterfv = (glGetSamplerParameterfv_client_proc_t) getProc("glGetSamplerParameterfv", userData);
	glGetSamplerParameteriv = (glGetSamplerParameteriv_client_proc_t) getProc("glGetSamplerParameteriv", userData);
	glIsSampler = (glIsSampler_client_proc_t) getProc("glIsSampler", userData);
	glGenQueries = (glGenQueries_client_proc_t) getProc("glGenQueries", userData);
	glDeleteQueries = (glDeleteQueries_client_proc_t) getProc("glDeleteQueries", userData);
	glBeginQuery = (glBeginQuery_client_proc_t) getProc("glBeginQuery", userData);
	glEndQuery = (glEndQuery_client_proc_t) getProc("glEndQuery", userData);
	glGetQueryiv = (glGetQueryiv_client_proc_t) getProc("glGetQueryiv", userData);
	glGetQueryObjectuiv = (glGetQueryObjectuiv_client_proc_t) getProc("glGetQueryObjectuiv", userData);
	glIsQuery = (glIsQuery_client_proc_t) getProc("glIsQuery", userData);
	glProgramParameteri = (glProgramParameteri_client_proc_t) getProc("glProgramParameteri", userData);
	glProgramBinary = (glProgramBinary_client_proc_t) getProc("glProgramBinary", userData);
	glGetProgramBinary = (glGetProgramBinary_client_proc_t) getProc("glGetProgramBinary", userData);
	glGetFragDataLocation = (glGetFragDataLocation_client_proc_t) getProc("glGetFragDataLocation", userData);
	glGetInteger64v = (glGetInteger64v_client_proc_t) getProc("glGetInteger64v", userData);
	glGetIntegeri_v = (glGetIntegeri_v_client_proc_t) getProc("glGetIntegeri_v", userData);
	glGetInteger64i_v = (glGetInteger64i_v_client_proc_t) getProc("glGetInteger64i_v", userData);
	glTexImage3D = (glTexImage3D_client_proc_t) getProc("glTexImage3D", userData);
	glTexImage3DOffsetAEMU = (glTexImage3DOffsetAEMU_client_proc_t) getProc("glTexImage3DOffsetAEMU", userData);
	glTexStorage3D = (glTexStorage3D_client_proc_t) getProc("glTexStorage3D", userData);
	glTexSubImage3D = (glTexSubImage3D_client_proc_t) getProc("glTexSubImage3D", userData);
	glTexSubImage3DOffsetAEMU = (glTexSubImage3DOffsetAEMU_client_proc_t) getProc("glTexSubImage3DOffsetAEMU", userData);
	glCompressedTexImage3D = (glCompressedTexImage3D_client_proc_t) getProc("glCompressedTexImage3D", userData);
	glCompressedTexImage3DOffsetAEMU = (glCompressedTexImage3DOffsetAEMU_client_proc_t) getProc("glCompressedTexImage3DOffsetAEMU", userData);
	glCompressedTexSubImage3D = (glCompressedTexSubImage3D_client_proc_t) getProc("glCompressedTexSubImage3D", userData);
	glCompressedTexSubImage3DOffsetAEMU = (glCompressedTexSubImage3DOffsetAEMU_client_proc_t) getProc("glCompressedTexSubImage3DOffsetAEMU", userData);
	glCopyTexSubImage3D = (glCopyTexSubImage3D_client_proc_t) getProc("glCopyTexSubImage3D", userData);
	glGetStringi = (glGetStringi_client_proc_t) getProc("glGetStringi", userData);
	glGetBooleani_v = (glGetBooleani_v_client_proc_t) getProc("glGetBooleani_v", userData);
	glMemoryBarrier = (glMemoryBarrier_client_proc_t) getProc("glMemoryBarrier", userData);
	glMemoryBarrierByRegion = (glMemoryBarrierByRegion_client_proc_t) getProc("glMemoryBarrierByRegion", userData);
	glGenProgramPipelines = (glGenProgramPipelines_client_proc_t) getProc("glGenProgramPipelines", userData);
	glDeleteProgramPipelines = (glDeleteProgramPipelines_client_proc_t) getProc("glDeleteProgramPipelines", userData);
	glBindProgramPipeline = (glBindProgramPipeline_client_proc_t) getProc("glBindProgramPipeline", userData);
	glGetProgramPipelineiv = (glGetProgramPipelineiv_client_proc_t) getProc("glGetProgramPipelineiv", userData);
	glGetProgramPipelineInfoLog = (glGetProgramPipelineInfoLog_client_proc_t) getProc("glGetProgramPipelineInfoLog", userData);
	glValidateProgramPipeline = (glValidateProgramPipeline_client_proc_t) getProc("glValidateProgramPipeline", userData);
	glIsProgramPipeline = (glIsProgramPipeline_client_proc_t) getProc("glIsProgramPipeline", userData);
	glUseProgramStages = (glUseProgramStages_client_proc_t) getProc("glUseProgramStages", userData);
	glActiveShaderProgram = (glActiveShaderProgram_client_proc_t) getProc("glActiveShaderProgram", userData);
	glCreateShaderProgramv = (glCreateShaderProgramv_client_proc_t) getProc("glCreateShaderProgramv", userData);
	glCreateShaderProgramvAEMU = (glCreateShaderProgramvAEMU_client_proc_t) getProc("glCreateShaderProgramvAEMU", userData);
	glProgramUniform1f = (glProgramUniform1f_client_proc_t) getProc("glProgramUniform1f", userData);
	glProgramUniform2f = (glProgramUniform2f_client_proc_t) getProc("glProgramUniform2f", userData);
	glProgramUniform3f = (glProgramUniform3f_client_proc_t) getProc("glProgramUniform3f", userData);
	glProgramUniform4f = (glProgramUniform4f_client_proc_t) getProc("glProgramUniform4f", userData);
	glProgramUniform1i = (glProgramUniform1i_client_proc_t) getProc("glProgramUniform1i", userData);
	glProgramUniform2i = (glProgramUniform2i_client_proc_t) getProc("glProgramUniform2i", userData);
	glProgramUniform3i = (glProgramUniform3i_client_proc_t) getProc("glProgramUniform3i", userData);
	glProgramUniform4i = (glProgramUniform4i_client_proc_t) getProc("glProgramUniform4i", userData);
	glProgramUniform1ui = (glProgramUniform1ui_client_proc_t) getProc("glProgramUniform1ui", userData);
	glProgramUniform2ui = (glProgramUniform2ui_client_proc_t) getProc("glProgramUniform2ui", userData);
	glProgramUniform3ui = (glProgramUniform3ui_client_proc_t) getProc("glProgramUniform3ui", userData);
	glProgramUniform4ui = (glProgramUniform4ui_client_proc_t) getProc("glProgramUniform4ui", userData);
	glProgramUniform1fv = (glProgramUniform1fv_client_proc_t) getProc("glProgramUniform1fv", userData);
	glProgramUniform2fv = (glProgramUniform2fv_client_proc_t) getProc("glProgramUniform2fv", userData);
	glProgramUniform3fv = (glProgramUniform3fv_client_proc_t) getProc("glProgramUniform3fv", userData);
	glProgramUniform4fv = (glProgramUniform4fv_client_proc_t) getProc("glProgramUniform4fv", userData);
	glProgramUniform1iv = (glProgramUniform1iv_client_proc_t) getProc("glProgramUniform1iv", userData);
	glProgramUniform2iv = (glProgramUniform2iv_client_proc_t) getProc("glProgramUniform2iv", userData);
	glProgramUniform3iv = (glProgramUniform3iv_client_proc_t) getProc("glProgramUniform3iv", userData);
	glProgramUniform4iv = (glProgramUniform4iv_client_proc_t) getProc("glProgramUniform4iv", userData);
	glProgramUniform1uiv = (glProgramUniform1uiv_client_proc_t) getProc("glProgramUniform1uiv", userData);
	glProgramUniform2uiv = (glProgramUniform2uiv_client_proc_t) getProc("glProgramUniform2uiv", userData);
	glProgramUniform3uiv = (glProgramUniform3uiv_client_proc_t) getProc("glProgramUniform3uiv", userData);
	glProgramUniform4uiv = (glProgramUniform4uiv_client_proc_t) getProc("glProgramUniform4uiv", userData);
	glProgramUniformMatrix2fv = (glProgramUniformMatrix2fv_client_proc_t) getProc("glProgramUniformMatrix2fv", userData);
	glProgramUniformMatrix3fv = (glProgramUniformMatrix3fv_client_proc_t) getProc("glProgramUniformMatrix3fv", userData);
	glProgramUniformMatrix4fv = (glProgramUniformMatrix4fv_client_proc_t) getProc("glProgramUniformMatrix4fv", userData);
	glProgramUniformMatrix2x3fv = (glProgramUniformMatrix2x3fv_client_proc_t) getProc("glProgramUniformMatrix2x3fv", userData);
	glProgramUniformMatrix3x2fv = (glProgramUniformMatrix3x2fv_client_proc_t) getProc("glProgramUniformMatrix3x2fv", userData);
	glProgramUniformMatrix2x4fv = (glProgramUniformMatrix2x4fv_client_proc_t) getProc("glProgramUniformMatrix2x4fv", userData);
	glProgramUniformMatrix4x2fv = (glProgramUniformMatrix4x2fv_client_proc_t) getProc("glProgramUniformMatrix4x2fv", userData);
	glProgramUniformMatrix3x4fv = (glProgramUniformMatrix3x4fv_client_proc_t) getProc("glProgramUniformMatrix3x4fv", userData);
	glProgramUniformMatrix4x3fv = (glProgramUniformMatrix4x3fv_client_proc_t) getProc("glProgramUniformMatrix4x3fv", userData);
	glGetProgramInterfaceiv = (glGetProgramInterfaceiv_client_proc_t) getProc("glGetProgramInterfaceiv", userData);
	glGetProgramResourceiv = (glGetProgramResourceiv_client_proc_t) getProc("glGetProgramResourceiv", userData);
	glGetProgramResourceIndex = (glGetProgramResourceIndex_client_proc_t) getProc("glGetProgramResourceIndex", userData);
	glGetProgramResourceLocation = (glGetProgramResourceLocation_client_proc_t) getProc("glGetProgramResourceLocation", userData);
	glGetProgramResourceName = (glGetProgramResourceName_client_proc_t) getProc("glGetProgramResourceName", userData);
	glBindImageTexture = (glBindImageTexture_client_proc_t) getProc("glBindImageTexture", userData);
	glDispatchCompute = (glDispatchCompute_client_proc_t) getProc("glDispatchCompute", userData);
	glDispatchComputeIndirect = (glDispatchComputeIndirect_client_proc_t) getProc("glDispatchComputeIndirect", userData);
	glBindVertexBuffer = (glBindVertexBuffer_client_proc_t) getProc("glBindVertexBuffer", userData);
	glVertexAttribBinding = (glVertexAttribBinding_client_proc_t) getProc("glVertexAttribBinding", userData);
	glVertexAttribFormat = (glVertexAttribFormat_client_proc_t) getProc("glVertexAttribFormat", userData);
	glVertexAttribIFormat = (glVertexAttribIFormat_client_proc_t) getProc("glVertexAttribIFormat", userData);
	glVertexBindingDivisor = (glVertexBindingDivisor_client_proc_t) getProc("glVertexBindingDivisor", userData);
	glDrawArraysIndirect = (glDrawArraysIndirect_client_proc_t) getProc("glDrawArraysIndirect", userData);
	glDrawArraysIndirectDataAEMU = (glDrawArraysIndirectDataAEMU_client_proc_t) getProc("glDrawArraysIndirectDataAEMU", userData);
	glDrawArraysIndirectOffsetAEMU = (glDrawArraysIndirectOffsetAEMU_client_proc_t) getProc("glDrawArraysIndirectOffsetAEMU", userData);
	glDrawElementsIndirect = (glDrawElementsIndirect_client_proc_t) getProc("glDrawElementsIndirect", userData);
	glDrawElementsIndirectDataAEMU = (glDrawElementsIndirectDataAEMU_client_proc_t) getProc("glDrawElementsIndirectDataAEMU", userData);
	glDrawElementsIndirectOffsetAEMU = (glDrawElementsIndirectOffsetAEMU_client_proc_t) getProc("glDrawElementsIndirectOffsetAEMU", userData);
	glTexStorage2DMultisample = (glTexStorage2DMultisample_client_proc_t) getProc("glTexStorage2DMultisample", userData);
	glSampleMaski = (glSampleMaski_client_proc_t) getProc("glSampleMaski", userData);
	glGetMultisamplefv = (glGetMultisamplefv_client_proc_t) getProc("glGetMultisamplefv", userData);
	glFramebufferParameteri = (glFramebufferParameteri_client_proc_t) getProc("glFramebufferParameteri", userData);
	glGetFramebufferParameteriv = (glGetFramebufferParameteriv_client_proc_t) getProc("glGetFramebufferParameteriv", userData);
	glGetTexLevelParameterfv = (glGetTexLevelParameterfv_client_proc_t) getProc("glGetTexLevelParameterfv", userData);
	glGetTexLevelParameteriv = (glGetTexLevelParameteriv_client_proc_t) getProc("glGetTexLevelParameteriv", userData);
	glMapBufferRangeDMA = (glMapBufferRangeDMA_client_proc_t) getProc("glMapBufferRangeDMA", userData);
	glUnmapBufferDMA = (glUnmapBufferDMA_client_proc_t) getProc("glUnmapBufferDMA", userData);
	glMapBufferRangeDirect = (glMapBufferRangeDirect_client_proc_t) getProc("glMapBufferRangeDirect", userData);
	glUnmapBufferDirect = (glUnmapBufferDirect_client_proc_t) getProc("glUnmapBufferDirect", userData);
	glFlushMappedBufferRangeDirect = (glFlushMappedBufferRangeDirect_client_proc_t) getProc("glFlushMappedBufferRangeDirect", userData);
	glGetGraphicsResetStatusEXT = (glGetGraphicsResetStatusEXT_client_proc_t) getProc("glGetGraphicsResetStatusEXT", userData);
	glReadnPixelsEXT = (glReadnPixelsEXT_client_proc_t) getProc("glReadnPixelsEXT", userData);
	glGetnUniformfvEXT = (glGetnUniformfvEXT_client_proc_t) getProc("glGetnUniformfvEXT", userData);
	glGetnUniformivEXT = (glGetnUniformivEXT_client_proc_t) getProc("glGetnUniformivEXT", userData);
	glDrawArraysNullAEMU = (glDrawArraysNullAEMU_client_proc_t) getProc("glDrawArraysNullAEMU", userData);
	glDrawElementsNullAEMU = (glDrawElementsNullAEMU_client_proc_t) getProc("glDrawElementsNullAEMU", userData);
	glDrawElementsOffsetNullAEMU = (glDrawElementsOffsetNullAEMU_client_proc_t) getProc("glDrawElementsOffsetNullAEMU", userData);
	glDrawElementsDataNullAEMU = (glDrawElementsDataNullAEMU_client_proc_t) getProc("glDrawElementsDataNullAEMU", userData);
	glUnmapBufferAsyncAEMU = (glUnmapBufferAsyncAEMU_client_proc_t) getProc("glUnmapBufferAsyncAEMU", userData);
	glFlushMappedBufferRangeAEMU2 = (glFlushMappedBufferRangeAEMU2_client_proc_t) getProc("glFlushMappedBufferRangeAEMU2", userData);
	glBufferDataSyncAEMU = (glBufferDataSyncAEMU_client_proc_t) getProc("glBufferDataSyncAEMU", userData);
	glTexBufferOES = (glTexBufferOES_client_proc_t) getProc("glTexBufferOES", userData);
	glTexBufferRangeOES = (glTexBufferRangeOES_client_proc_t) getProc("glTexBufferRangeOES", userData);
	glTexBufferEXT = (glTexBufferEXT_client_proc_t) getProc("glTexBufferEXT", userData);
	glTexBufferRangeEXT = (glTexBufferRangeEXT_client_proc_t) getProc("glTexBufferRangeEXT", userData);
	glEnableiEXT = (glEnableiEXT_client_proc_t) getProc("glEnableiEXT", userData);
	glDisableiEXT = (glDisableiEXT_client_proc_t) getProc("glDisableiEXT", userData);
	glBlendEquationiEXT = (glBlendEquationiEXT_client_proc_t) getProc("glBlendEquationiEXT", userData);
	glBlendEquationSeparateiEXT = (glBlendEquationSeparateiEXT_client_proc_t) getProc("glBlendEquationSeparateiEXT", userData);
	glBlendFunciEXT = (glBlendFunciEXT_client_proc_t) getProc("glBlendFunciEXT", userData);
	glBlendFuncSeparateiEXT = (glBlendFuncSeparateiEXT_client_proc_t) getProc("glBlendFuncSeparateiEXT", userData);
	glColorMaskiEXT = (glColorMaskiEXT_client_proc_t) getProc("glColorMaskiEXT", userData);
	glIsEnablediEXT = (glIsEnablediEXT_client_proc_t) getProc("glIsEnablediEXT", userData);
	glBlendBarrier = (glBlendBarrier_client_proc_t) getProc("glBlendBarrier", userData);
	glCopyImageSubData = (glCopyImageSubData_client_proc_t) getProc("glCopyImageSubData", userData);
	glObjectLabel = (glObjectLabel_client_proc_t) getProc("glObjectLabel", userData);
	glGetObjectLabel = (glGetObjectLabel_client_proc_t) getProc("glGetObjectLabel", userData);
	glObjectPtrLabel = (glObjectPtrLabel_client_proc_t) getProc("glObjectPtrLabel", userData);
	glGetObjectPtrLabel = (glGetObjectPtrLabel_client_proc_t) getProc("glGetObjectPtrLabel", userData);
	glEnablei = (glEnablei_client_proc_t) getProc("glEnablei", userData);
	glDisablei = (glDisablei_client_proc_t) getProc("glDisablei", userData);
	glBlendEquationi = (glBlendEquationi_client_proc_t) getProc("glBlendEquationi", userData);
	glBlendEquationSeparatei = (glBlendEquationSeparatei_client_proc_t) getProc("glBlendEquationSeparatei", userData);
	glBlendFunci = (glBlendFunci_client_proc_t) getProc("glBlendFunci", userData);
	glBlendFuncSeparatei = (glBlendFuncSeparatei_client_proc_t) getProc("glBlendFuncSeparatei", userData);
	glColorMaski = (glColorMaski_client_proc_t) getProc("glColorMaski", userData);
	glIsEnabledi = (glIsEnabledi_client_proc_t) getProc("glIsEnabledi", userData);
	glDrawElementsBaseVertex = (glDrawElementsBaseVertex_client_proc_t) getProc("glDrawElementsBaseVertex", userData);
	glDrawRangeElementsBaseVertex = (glDrawRangeElementsBaseVertex_client_proc_t) getProc("glDrawRangeElementsBaseVertex", userData);
	glDrawElementsInstancedBaseVertex = (glDrawElementsInstancedBaseVertex_client_proc_t) getProc("glDrawElementsInstancedBaseVertex", userData);
	glFramebufferTexture = (glFramebufferTexture_client_proc_t) getProc("glFramebufferTexture", userData);
	glPrimitiveBoundingBox = (glPrimitiveBoundingBox_client_proc_t) getProc("glPrimitiveBoundingBox", userData);
	glGetGraphicsResetStatus = (glGetGraphicsResetStatus_client_proc_t) getProc("glGetGraphicsResetStatus", userData);
	glReadnPixels = (glReadnPixels_client_proc_t) getProc("glReadnPixels", userData);
	glGetnUniformfv = (glGetnUniformfv_client_proc_t) getProc("glGetnUniformfv", userData);
	glGetnUniformiv = (glGetnUniformiv_client_proc_t) getProc("glGetnUniformiv", userData);
	glGetnUniformuiv = (glGetnUniformuiv_client_proc_t) getProc("glGetnUniformuiv", userData);
	glMinSampleShading = (glMinSampleShading_client_proc_t) getProc("glMinSampleShading", userData);
	glPatchParameteri = (glPatchParameteri_client_proc_t) getProc("glPatchParameteri", userData);
	glTexParameterIiv = (glTexParameterIiv_client_proc_t) getProc("glTexParameterIiv", userData);
	glTexParameterIuiv = (glTexParameterIuiv_client_proc_t) getProc("glTexParameterIuiv", userData);
	glGetTexParameterIiv = (glGetTexParameterIiv_client_proc_t) getProc("glGetTexParameterIiv", userData);
	glGetTexParameterIuiv = (glGetTexParameterIuiv_client_proc_t) getProc("glGetTexParameterIuiv", userData);
	glSamplerParameterIiv = (glSamplerParameterIiv_client_proc_t) getProc("glSamplerParameterIiv", userData);
	glSamplerParameterIuiv = (glSamplerParameterIuiv_client_proc_t) getProc("glSamplerParameterIuiv", userData);
	glGetSamplerParameterIiv = (glGetSamplerParameterIiv_client_proc_t) getProc("glGetSamplerParameterIiv", userData);
	glGetSamplerParameterIuiv = (glGetSamplerParameterIuiv_client_proc_t) getProc("glGetSamplerParameterIuiv", userData);
	glTexBuffer = (glTexBuffer_client_proc_t) getProc("glTexBuffer", userData);
	glTexBufferRange = (glTexBufferRange_client_proc_t) getProc("glTexBufferRange", userData);
	glTexStorage3DMultisample = (glTexStorage3DMultisample_client_proc_t) getProc("glTexStorage3DMultisample", userData);
	glCopyImageSubDataEXT = (glCopyImageSubDataEXT_client_proc_t) getProc("glCopyImageSubDataEXT", userData);
	glDrawElementsBaseVertexOffset = (glDrawElementsBaseVertexOffset_client_proc_t) getProc("glDrawElementsBaseVertexOffset", userData);
	glBindFragDataLocationEXT = (glBindFragDataLocationEXT_client_proc_t) getProc("glBindFragDataLocationEXT", userData);
	glBindFragDataLocationIndexedEXT = (glBindFragDataLocationIndexedEXT_client_proc_t) getProc("glBindFragDataLocationIndexedEXT", userData);
	glGetFragDataIndexEXT = (glGetFragDataIndexEXT_client_proc_t) getProc("glGetFragDataIndexEXT", userData);
	glGetProgramResourceLocationIndexEXT = (glGetProgramResourceLocationIndexEXT_client_proc_t) getProc("glGetProgramResourceLocationIndexEXT", userData);
	glFramebufferTexture2DMultisampleEXT = (glFramebufferTexture2DMultisampleEXT_client_proc_t) getProc("glFramebufferTexture2DMultisampleEXT", userData);
	glRenderbufferStorageMultisampleEXT = (glRenderbufferStorageMultisampleEXT_client_proc_t) getProc("glRenderbufferStorageMultisampleEXT", userData);
	glTexStorage2DEXT = (glTexStorage2DEXT_client_proc_t) getProc("glTexStorage2DEXT", userData);
	glTexStorage3DEXT = (glTexStorage3DEXT_client_proc_t) getProc("glTexStorage3DEXT", userData);
	glMapBufferRangeEXT = (glMapBufferRangeEXT_client_proc_t) getProc("glMapBufferRangeEXT", userData);
	glFlushMappedBufferRangeEXT = (glFlushMappedBufferRangeEXT_client_proc_t) getProc("glFlushMappedBufferRangeEXT", userData);
	glDebugMessageInsert = (glDebugMessageInsert_client_proc_t) getProc("glDebugMessageInsert", userData);
	glBlendBarrierKHR = (glBlendBarrierKHR_client_proc_t) getProc("glBlendBarrierKHR", userData);
	return 0;
}
