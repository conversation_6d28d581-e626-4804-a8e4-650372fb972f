﻿x-mini-mua,结构体,属性,属性值,属性值说明,备注,状态
,v,内部版本号?,2.9.11,,,
,p,未知,"""a""",,,
,a,java层hardcode初始化传入,"""ECFAAF01""",,,
,t,结构体,,,,
,,c,TODO 计数器 NativeInputEventReceiver::handleEvent 某些事件触发+1,,,
,,d,"void android.view.InputEventReceiver.dispatchInputEvent(int, android.view.InputEvent) -最新一次MotionEvent里面的 getDeviceId",,,
,,t,"void android.view.InputEventReceiver.dispatchInputEvent(int, android.view.InputEvent) - 最新一次MotionEvent里面的getEventTime",,,
,,s,"void android.view.InputEventReceiver.dispatchInputEvent(int, android.view.InputEvent) - 最新一次MotionEvent里面的'getSource' - 4098 -  数字",,,
,,f,"void android.view.InputEventReceiver.dispatchInputEvent(int, android.view.InputEvent) - 最新一次MotionEvent里面的'getSource' - 4098 -  数字",,,
,,tt,"void android.view.InputEventReceiver.dispatchInputEvent(int, android.view.InputEvent) - 最新一次MotionEvent里面的 所有PointerCount 的getToolType 合集",,,
,u,调用 https://as.xiaohongshu.com/api/v1/profile/android 返回的结果json里面的u,,,,
,s,从/dev/urandom 中读取64位bytes 转成128位字符,,,,
,k,todo 没完全理解，由私钥+公钥 计算出来出来一个5*51大整数 格式化字串，可能是临时公钥，也可能是共享密钥,,,,
,x98,云配config版本,,,,
,x20,"__system_property_read读属性 ""ro.product.brand""",,,,
,x21,"__system_property_read读属性 ""ro.product.cpu.abilist""",,,,
,x25,__system_property_read读属性 ro.product.name,,,,
,x231,通过__NR_statfs调用 返回的struct statfs 结构中f_frsize * f_blocks,,,,
,x236,通过__NR_statfs调用 返回的struct statfs 结构中f_frsize * f_bavail,,,,
,x232,通过__NR_statfs调用 返回的struct statfs 结构中f_frsize * f_blocks(这个和x231暂时看不出分别，它里面用了两个一模一样的函数做了相同的事情）,,,,
,x237,通过__NR_statfs调用 返回的struct statfs 结构中f_frsize * f_bavail （这个和x236，看不出太多分别，目前仅知道x231/x236 是在同一个函数内计算的，x232/x237 又是在另外一个函数内计算的）,,,,
,x0,pkg包名,,,,
,x1,APP版本号,,,,
,x2,自身PackageInfo 的versionCode,,,,
,x3,自身PackageInfo 的firstInstallTime 整数,,,,
,x4,自身PackageInfo 的lastUpdateTime,,,,
,x92,自身ApplicationInfo 的targetSdkVersion,,,,
,x5,"获取一些market渠道信息，优先从
    ***
    Build.MANUFACTURER=‘meizu’ 或者 ro.build.display.id 含 flyme 则 从 /system/etc/meizu_channel.red 解密数据
    属性ro.vivo.os.version有值 或者 Build.BRAND=‘vivo’ 则 从 ro.preinstall.path + ‘vivo_channel.txt’ 解密数据
    ro.build.version.oplusrom有值 或者 Build.BRAND=‘oppo/realme/oneplus’ 则 从 /data/etc/appchannel/red 解密数据
    Build.BRAND=‘honor’ 则 读 ro.channel.red 属性
    Build.BRAND=‘redmi’ 且 ro.miui.ui.version.code有值  则 调用 miui.os.MiuiInit::getMiuiChannelPath(pkgName)
    ***
    中读取，没有信息的时候，从apk包体内获取。",,,,
,x6,实时时间的毫秒数(Realtime_ms) 减去 启动时间的毫秒数(Boottime_ms)，两个时间均从clock_gettime 中获取,,,,
,x7,"__system_property_read读属性 ""ro.board.platform""",,,,已伪造
,x8,"__system_property_read读属性 ""ro.build.date.utc""乘以 1000",,,,已伪造
,X9,"__system_property_read读属性 ""ro.build.display.id""",,,,已伪造
,x10,"__system_property_read读属性 ""ro.build.fingerprint""",,,,已伪造
,x11,__system_property_read读属性 ro.build.host,,,,已伪造
,x12,__system_property_read读属性 ro.build.id,,,,已伪造
,x13,__system_property_read读属性 ro.build.tags,,,,已伪造
,x14,__system_property_read读属性 ro.build.type,,,,已伪造
,x15,"__system_property_read读属性 ""ro.build.version.incremental""",,,,已伪造
,x16,"__system_property_read读属性 ""ro.build.version.release""",,,,已伪造
,x17,"__system_property_read读属性 ""ro.build.version.sdk"" - 整数",,,,已伪造
,x18,"__system_property_read读属性 ""ro.build.version.security_patch""",,,,已伪造
,x19,"__system_property_read读属性 ""ro.product.board""",,,,已伪造
,x22,"__system_property_read读属性 ""ro.product.device""",,,,已伪造
,x51,TODO 未知,,,,
,x23,__system_property_read读属性 ro.product.manufacturer,,,,已伪造
,x24,"__system_property_read读属性 ""ro.product.model""",,,,已伪造
,x26,uname syscall返回的 struct old_utsname里面的 machine字段,,,,
,x27,uname syscall返回的 struct old_utsname里面的 release字段,,,,
,x28,uname syscall返回的 struct old_utsname里面的 version字段,,,,
,x29,"__system_property_read读属性 ""gsm.version.baseband""",,,,
,x30,android.view.WindowManager.getDefaultDisplay().getRealMetrics() 返回的 DisplayMetrics中的 widthPixels / heightPixels / densityDpi,,,,
,x31,"android.provider.Settings$System.getInt(""screen_brightness"")",,,,
,x37,"android.provider.Settings$Secure.getInt(""accessibility_enabled"")",,,,
,x38,"android.provider.Settings$Global.getInt(""adb_enabled"")",,,,
,x93,"android.provider.Settings$Secure.getInt(""location_mode"")",,,,
,x267,"android.provider.Settings$System.getInt(""screen_brightness_mode"")",,,,
,x32,"android.content.Intent#ACTION_BATTERY_CHANGED 消息内的 ""present""的值",,,,
,x33,"android.content.Intent#ACTION_BATTERY_CHANGED 消息内的 ""status""的值",,,,
,x34,"android.content.Intent#ACTION_BATTERY_CHANGED 消息内的 ""scale""的值",,,,
,x35,"android.content.Intent#ACTION_BATTERY_CHANGED 消息内的 ""level""的值",,,,
,x36,"android.content.Intent#ACTION_BATTERY_CHANGED 消息内的 ""plugged""的值",,,,
,x39,"__system_property_read读属性 ""sys.usb.state""",,,,
,x40,TelephonyManager::getSimState,,,,
,x42,TelephonyManager::getSimOperator,,,,
,x41,TelephonyManager::getSimOperatorName,,,,
,x43,"通过 ConnectivityManager::getActiveNetworkInfo::isConnected 和 ConnectivityManager::getActiveNetworkInfo::getType 构造字串：
已知值有 unknown / bluetooth / ethernet / wifi / mobile / none",,,,
,x44,clock_gettime获取当前时间戳，单位毫秒,,,,
,x70,"从私有配置.tistore 中读取出""launch_count"" 计数，每次app进程启动+1，覆盖安装、升级之后又从1开始计算，默认值1",,,,
,x72,"从私有配置.tistore 中读取出""first_launch_time""，理论上应该有值，实测文件常判别损坏，默认值-1",,,,
,x73,"从私有配置.tistore 中读取出""last_launch_time""，理论上应该有值，实测文件常判别损坏，默认值-1",,,,
,x78,getuid返回值,,,,
,x79,getpid返回值,,,,
,x80,getppid返回值,,,,
,x87,当前进程启动初始化时， 用clock_gettime记录下来的时间戳，单位毫秒,,,,
,x120,ActivityManager::isUserAMonkey - 字符串，0假1真,,,,
,x131,syscall触发 gettid和 getpid判断是否相等,,,,
,x146,可能是 网络请求 https://as.xiaohongshu.com/api/v1/register/android  的返回值json中的“g”字段,,,,
,x185,"进程运行步骤打点，每个步骤指代一个特定的字符，每执行到一个步骤会增加一个字符，如 I指代初始化进入，S指代初始化结尾，s指代子线程初始化进入等，可能是某种运行轨迹记录器
    - IiGgSsKkCVvEePcp / IiGgSsKkCVvEePpc （如这里的 Pcp , Ppc 指代了两条不同线程的 特定位置的先后执行）",,,,
,x186,"某个初始化线程在做完一些事情时候之后，调用clock_gettime(0, 获取系统时间，单位毫秒，进程启动只获取一次",,,,
,x187,"网络请求 https://as.xiaohongshu.com/api/v1/register/android 成功获取g字段之后，会mark一下当前时间。clock_gettime(0, 获取当前时间，单位毫秒",,,,
,x194,WifiManager::isWifiEnabled字符串,,,,
,x202,从getRunningAppProcesses 拿进程名，根据进程名 判断当前是否是主进程,,,,
,x203,判断初始化，边长参数的入参的个数是否为11个，这里应该是固定的,,,,
,x206,TODO 没找到赋值点，默认值就是0 ,,,,
,x207,TODO 没找到赋值点，默认值就是0,,,,
,x234,结构体,"使用的是 fstatat 去检查下面 12 个路径的值：

""/storage/emulated/0/Movies"" ，
""/storage/emulated/0/Music"" ，
""/storage/emulated/0/Ringtones"" ，
""/storage/emulated/0/Notifications"" ，
""/storage/emulated/0/Pictures"" ， 
""/storage/emulated/0/Podcasts""，
""/storage/emulated/0/Android/obb/.nomedia""，
""/storage/emulated/0/Download""， 
""/storage/emulated/0/Android/data/.nomedia""，
""/storage/emulated/0/Alarms""，
""/data/vendor_ce/0""，
""/data/vendor_ce""",,,
,,1,最近一年内“最旧的访问活动”时间点。,,,
,,2,最近一年内“最旧的状态变更活动”时间点。,,,
,,3,最近一年内“最旧的修改活动”时间点。,,,
,x235,BatteryManager::getLongProperty(1)返回值,,,,
,x242,todo 值为空数组，内容未知,,,,
,x243,加载so时，.init_array内的某个函数在加载的时候（时机早于JNI_Onload），用clock_gettime(0 获取到的当前系统时间，单位毫秒,,,,
,x247,结构体,android.media.AudioManager.getStreamVolume除以 android.media.AudioManager.getStreamMaxVolume浮点数,,,
,,0,STREAM_VOICE_CALL,,,
,,1,STREAM_SYSTEM,,,
,,2,STREAM_RING,,,
,,3,STREAM_MUSIC,,,
,,4,STREAM_ALARM,,,
,,5,STREAM_NOTIFICATION,,,
,x258,有一个子线程，每隔9秒（不可中断、系统时间）执行一个任务，任务具体内容未知，不过这个任务执行开始会 进行计数+1，这个指代的就是此计数,,,,
,x259,"另外一个子线程，每隔586秒（不可中断，系统时间）执行一个任务，这个任务执行开始会先做检测，检测用户当前环境是否存在
            ""/system/bin/su""
            ""/system/xbin/su""
            ""/system/xbin/daemonsu""
        这些文件，如果存在，则开始一些未知的任务，并在进行 计数+1， 这个指代的就是此计数。",,,,
,x260,一个计算式(x258的值- x259的值+ 0（未观察到有地方赋值，不代表没有地方赋值TODO ）) ^ （clock_gettime(0 获取到的当前系统时间，单位毫秒）,,,,
,x261,调用TelephonyManager.getPhoneType() 返回值,,,,
,x263,"调用prctl(PR_GET_DUMPABLE, 0, 0, 0, 0);",,,,
,x264,"调用prctl(PR_GET_NO_NEW_PRIVS, 0, 0, 0, 0); 的返回值",,,,
,x269,"调用__NR_fstatat 检查""/system/build.prop"" 获取 最后修改时间（mtime），单位是毫秒",,,,
,x272,"TODO 估计是涉及到""android.tencent.TencentCommon"" 一个特殊的类的一些方法调用返回值，当前设备上没有这个类，默认值0",,,,
,x290,调用BiometricManager::canAuthenticate(33023) 返回值对应 （返回值对应 11 -》 1， 0 -》 0，其余 -》 2，异常 -》 -1 ）,,,,
,x97,结构体,,,,
,,s1,标志位,,,
,,,flag1,"判断是否""ro.secure""=1 和""ro.debuggable""=0",,符合要求
,,,flag2,通过 挂载的/apex/com.android.runtime/bin/linker64 找g_ld_preloads， 判断是否为空,,
,,,flag3,读文件/proc/self/mountinfo 判断是否包含magisk,,
,,,flag4,"在目录下   
""/product/bin""，
""/apex/com.android.runtime/bin""，
""/apex/com.android.art/bin""，
""/system_ext/bin""，         
""/system/bin""，
""/system/xbin""，
""/odm/bin""，
""/vendor/bin""，
""/vendor/xbin""
使用stat/lstat/statfs/access/open/bind
找文件su，magisk，shuamesu，daemonsu，bdsu，.su，ku.sud，sudo，sutemp",,
,,,flag5,"TODO 初始化赋值为""0""，没找到变更的地方",,
,,,flag6,"通过 挂载的 /apex/com.android.runtime/bin/linker64 找 __dl_g_soinfo_handles_map，枚举通过linker加载的elf文件，
一、检查路径是否包含 ""zigisk"" ，  ""magisk""， ""/preload.so""， 有则为“1”
二、检查路径是否包含 /system/bin/ 且 不包含 /linker， 有则为“1”
三、检查路径有两个路径 包含 /system/bin/ 且 包含 app_process，有则为“1”
四、其余为 “0”",,
,,,flag7,向“通过fd2x1e4e2x3f1v2b1s.dex启动的daemon”发送查询指令，查询该daemon是否有被trace，通过/proc/<pid>/status等方式判断。,,
,,s2,标志位,,,
,,,flag1,hardcode常量 “1”,,
,,,flag2,hardcode常量 “*”,,
,,,flag3,通过在daemon中获取packageInfo.signatures回传到主进程进行校验签名是否和代码中hardcode的进行匹配,,
,,s3,标志位,,,
,,,flag1,hardcode常量 “*”,,
,,,flag2,"一、通过获取所有网络的NetworkCapabilities.mTransportTypes检查有无TRANSPORT_VPN，
二、通过getLinkProperties获取LinkProperties，判断是否有ConnectivityManager.TYPE_VPN
三、通过getLinkProperties获取LinkProperties，判断其mIfaceName是否等于 'ppp0' or 'tun0'",,
,,,flag3,限定获取TYPE_MOBILE/TYPE_MOBILE_DUN/TYPE_MOBILE_HIPRI三种网络类型的LinkProperties，检查其内的mHttpProxy是否为空,,
,,,flag4,限定获取TYPE_WIFI/TYPE_WIMAX两种网络类型的LinkProperties，检查其内的mHttpProxy是否为空,,
,,,flag5,限定获取TYPE_BLUETOOTH一种网络类型的LinkProperties，检查其内的mHttpProxy是否为空,,
,,,flag6,限定获取TYPE_ETHERNET一种网络类型的LinkProperties，检查其内的mHttpProxy是否为空,,
,,,flag7,"通过System.getProperty(""http.proxy"")检查是否为空",,
,,s4,标志位,,,
,,,flag1,hardcode常量 “*”,,
,,,flag2,hardcode常量 “*”,,
,,,flag3,hardcode常量 “*”,,
,,,flag4,hardcode常量 “*”,,
,,s5,标志位,,,
,,,flag1,通过解析libart.so获取gJdwpAllowed，检查其值,,
,,,flag2,TODO 疑似非hardcode常量 “*” ,,
,,,flag3,hardcode常量 “*”,,
,,,flag4,hardcode常量 “*”,,
,,,flag5,"__system_property_read读属性 ""ro.debuggable""",,符合要求
,,,flag6,调用VMDebug::isDebuggerConnected 返回值,,
,,,flag7,调用Debug::isDebuggerConnected 返回值,,
,,,flag8,调用getPackageManager().getPackageInfo(getPackageName()).applicationInfo.flags 检查FLAG_DEBUGGABLE,,
,,,flag9,"读取当前进程下的所有/proc/<pid>/task/<tid>/status，取出Name，判断是否包含""JDWP Command""",,
,,,flag10,hardcode常量 “*”,,
,,,flag11,通过bind + connect检查本地端口23946是否能连通,,
,,,flag12,"读取当前进程下的所有/proc/<pid>/task/<tid>/status，取出Name，判断是否包含""ADB-JDWP""或“JDWP”",,
,,s6,标志位,,,
,,,flag1,hardcode常量 “*”,,
,,,flag2,"1. 通过 mmap 系统调用分配了一个大小为 4KB 的匿名、私有、可读可写的内存空间,
2. 对刚刚 mmap 的内存页调用 mincore, 检查其是否在物理内存当中。
3. 继续通过读取 /proc/self/pagemap， 检查其是否在物理内存当中。
4. 对mmap的内存写入一个字节，重复上述的检查。
检查通过设置""0""，不通过设置""1""",,
,,,flag3,"1。 mmap分配一块可执行内存，不断循环写入不同的 "" MOV W0, #32（具体数字会变）; RET; "" 指令，
2。写入后立马调用指令进行执行，进行100次测试，如果 执行完成的值都 完全等于指令写入的值， 则判定失败。
3。如果某一个不等于，对该可执行内存，执行内存屏障刷新指令缓存，重新执行，如果 执行完成的值 等于 指令写入的值，判定成功。否则判定失败。",,
,,,flag4,hardcode常量 “*”,,
,,,flag5,"检查下列文件是否存在(文件列表来源于 某个配置文件 ['x97']['s']['3'])
                ""/system/lib/arm/houdini""
                ""/system/lib/libhoudini.so""
                ""/system/lib64/arm64/houdini""
                ""/system/lib64/libhoudini.so""
                ""/system/vendor/etc/Generic/lib64/arm64/houdini64""
                ""/system/vendor/etc/Generic/lib64/libhoudini.so""
                ""/data/downloads/.oh/libhoudini.so""
                ""/data/downloads/.oh/arm/houdini""
                ""/data/downloads/.oh/arm/libhoudini.so""
                ""/boot/android/android/system/lib/libhoudini.so""
                ""/boot/android/android/system/lib/arm/houdini""",,
,,,flag6,"TODO - 值""0""，应该也是要去检查某些文件是否存在，但可能配置为空。实测没有东西。",,
,,,flag7,"检查下列文件是否存在
                ""/dev/socket/genyd""
                ""/dev/socket/baseband_genyd""
                ""/system/bin/androVM-prop""
                ""/system/bin/microvirt-prop""
                ""/system/lib/libdroid4x.so""
                ""/system/bin/windroyed""
                ""/system/bin/microvirtd""
                ""/system/bin/nox-prop""
                ""/system/bin/ttVM-prop""
                ""/system/bin/droid4x-prop""
                ""/data/.bluestacks.prop""
                ""/data/dalvik-cache/x86""
                ""/data/dalvik-cache/x86_64""
                ""/boot/android/android/system/framework/oat/x86""
                ""/boot/android/android/system/framework/x86""
                ""/boot/android/android/system/lib/hw/audio.primary.x86.so""
                ""/boot/android/android/system/lib/hw/camera.x86.so""
                ""/boot/android/android/system/lib/hw/power.x86.so""
                ""/boot/android/android/system/lib/libclcore_x86.bc""
                ""/data/downloads/com.android.chrome/lib/x86""
                ""/data/downloads/com.bluestacks.appmart/oat/x86""
                ""/data/downloads/com.bluestacks.filemanager/oat/x86""
                ""/data/downloads/com.bluestacks.home/oat/x86""
                ""/data/downloads/com.location.provider/oat/x86""
                ""/data/downloads/com.uncube.account/oat/x86""
                ""/fstab_sdcard.android_x86""
                ""/fstab.android_x86""
                ""/fstab.android_x86_64""
                ""/init.android_x86_64.rc""
                ""/init.android_x86.rc""
                ""/sys/kernel/debug/tracing/events/irq_vectors/x86_platform_ipi_entry""
                ""/sys/kernel/debug/tracing/events/irq_vectors/x86_platform_ipi_exit""
                ""/sys/kernel/debug/tracing/events/x86_fpu""
                ""/sys/kernel/debug/x86""
                ""/system/framework/oat/x86""
                ""/system/framework/oat/x86_64""
                ""/system/framework/x86""
                ""/system/framework/x86_64""
                ""/system/lib/hw/audio.primary.x86.so""
                ""/system/lib/hw/camera.x86.so""
                ""/system/lib/hw/power.x86.so""
                ""/system/lib/libclcore_x86.bc""
                ""/system/lib64/libclcore_x86.bc""
                ""/system/priv-app/com.bluestacks.BstCommandProcessor/oat/x86""
                ""/system/priv-app/com.bluestacks.settings/oat/x86""
                ""/ueventd.android_x86_64.rc""
                ""/ueventd.android_x86.rc""
                ""/sys/module/vboxsf""
                ""/system/droid4x""
                ""/system/bin/droid4x-vbox-sf""
                ""/system/lib/egl/libEGL_tiDetectanVM.so""
                ""/system/bin/ttVM-vbox-sf""
                ""/system/lib/libnox.so""
                ""/system/bin/nox-vbox-sf""
                ""/system/bin/androVM-vbox-sf""
                ""/ueventd.vbox86.rc""
                ""/dev/com.bluestacks.superuser.daemon""
                ""/system/bin/yiwan-prop""
                ""/system/bin/yiwan-sf""
                ""/system/bin/androVM-prop""
                ""/system/bin/microvirt-prop""
                ""/system/lib/libdroid4x.so""
                ""/data/.bluestacks.prop""
                ""/system/app/com.mumu.launcher""
                ""/system/lib/vboxguest.ko""
                ""/system/lib/vboxsf.ko""
                ""/sys/class/misc/vboxguest""
                ""/sys/class/misc/vboxuser""",,符合要求
,,,flag8,"一、__system_property_read 读属性 persist.sys.nativebridge 判断是否等于 false，
 二、__system_property_read 读属性 ro.enable.native.bridge.exec 判断是否等于 false，
三、__system_property_read 读属性 ro.enable.native.bridge.exec64 判断是否等于 false。",,
,,,flag9,"调用dladdr检查当前进程下dlopen符号表属于哪个so，如果是 /system/lib64/arm64/nb/libdl.so 则判定失败。
               读取dlopen指向的第一条指令，如果为svc指令，则判定失败。",,
,,,flag10,hardcode常量 “*”,,
,,,flag11,打开 /system/bin/linker64 读取文件头ELF Header中的 e_machine字段进行判断是否未 EM_386 或 EM_X86_64,,
,,,flag12,"使用openat+fdopen+fgets读取/proc/self/mountinfo的内容，检查是否包含""/dorker""",,
,,s7,标志位,,,
,,,flag1,检查 android.os.ServiceManager.sCache 里面的values是否都是继承自 android.os.BinderProxy,,
,,,flag2,利用vfork创建子进程，子进程内会去修改父进程的数据，待子进程修改完成结束后，父进程会判定 子进程的修改 是否符合预期。,,
,,,flag3,"1. 判断getuid < 100000 是否成立
2. 向子进程（通过fd2x1e4e2x3f1v2b1s.dex启动的daemon）发送查询指令，子进程执行getPackageManager().getPackageInfo(pkgName, 0x8000000) 并把PackageInfo返回给父进程，父进程取出applicationInfo.dataDir，进行readlinkat解析，然后提取出里面的数字（如，/data/user/10/com.xingin.xhs里面的数字10）进行判断是否为0",,
,,,flag4,"1. 进程内执行 getPackageManager().getPackageInfo(pkgName, 0x8000000)取其 packageName
2. 向子进程（通过fd2x1e4e2x3f1v2b1s.dex启动的daemon）发送查询指令，子进程执行getPackageManager().getPackageInfo(pkgName, 0x8000000)，取其packageName
3. 进程内执行 getPackagesForUid(getuid())

检查三个来源的包名是否一致",,
,,s8,标志位,,,
,,,flag1,判断当前线程是不是主线程（pid == tid),,
,,,flag2,"使用openat+fdopen+fgets读取/proc/self/mountinfo的内容，检查是否包含""riru-core""",,
,,,flag3,"1. 判断openat打开/proc/self/maps的时候，路径是否有被串改
2. 探测程序内的openat执行svc指令是否有被替换成nop指令
3. 父进程通过 sigaltstack 设置安全备用栈后创建子进程，子进程主动触发SIGSEGV，并在信号处理函数中捕获异常时的帧栈，然后将该帧栈信息传回父进程进行分析和判定。",,
,,,flag4,"1. 通过调用 android.os.ServiceManager.sCache[""package""] 获得 IBinder，判断其类是否属于 android.os.BinderProxy
2. 通过调用 getPackageManager()::mPM 获得 IPackageManager，判断其类是否属于 android.os.BinderProxy",,
,,,flag5,"1. 通过getSystemService(""location"").isProviderEnabled(""fused"") 判断
2. 通过getSystemService(""location"").getProviderProperties(""fused"") 判断
3.通过getSystemService(""location"").getProviders(false) 判断是否包含 ""fused""",,
,,,flag6,"使用 ServiceManager.getIServiceManager().checkService 检查 ""service_fl_ml"" / ""service_mock_location"" 是否存在",,
,,,flag7,"1. 检查是否 int isMockGps，数据源 com.tencent.map.geolocation.TencentLocation
2. 判定 ""fake"" == com.tencent.map.geolocation.TencentLocation.getSourceProvider() 
且 com.tencent.map.geolocation.TencentLocation.getFakeReason() == 16",,
,,s9,标志位,,,
,,,flag1,hardcode常量 “*”,,
,,,flag2,"一、设置 自定义Thread.uncaughtExceptionHandler，手动抛出一个异常，然后通过 Thread.dispatchUncaughtException 传递进 自身自定义的 UncaughtExceptionHandler 内，
二、自定义UncaughtExceptionHandler内 对so函数进行调用， so函数内通过 dalvik.system.VMStack.getThreadStackTrace(Thread.currentThread())
和 Throwable.nativeGetStackTrace(Throwable.nativeFillInStackTrace()) 获取调用堆栈（两者取较长的那一个），
三、检查堆栈StackTraceElement内的declaringClass 转小写后是否包含 xposed/invokeoriginalmethod/hook",,
,,,flag3,按行读文件/proc/self/mountinfo 判断是否包含lsposed,,
,,,flag4,"打开文件 ""/system/bin/am""，取出fd，填入""/proc/self/fd/%d""，用以执行am monitor --gdb命令，等待5秒，读取期间的所有输出，
检查是否包含 replaceShellCommand / BridgeService 字串",,
,,s10,标志位,,,
,,,flag1,"读取当前进程下的所有 /proc/<pid>/task/<tid>/status，取出 Name，判断是否包含 ""gum-js-loop"" 或 “gmain”",,
,,,flag2,hardcode常量 “*”,,
,,,flag3,"一、__NR_openat打开 /proc/self/fd 目录，使用fdopendir 和 readdir遍历所有条目，
二、 继续对子项 /proc/self/fd/<fd_number> 调用__NR_readlinkat 获取 真实路径
三、剔除掉 ""socket:["" 和 ""pipe:["" 开头的，剩余的进行检查是否包含字串 ""linjector""",,
,,,flag4,hardcode常量 “*”,,
,,,flag5,hardcode常量 “*”,,
,,s11,标志位,,,
,,,flag1,hardcode常量 “*”,,
,,,flag2,"通过 挂载的 /apex/com.android.runtime/bin/linker64 找 g_ld_preloads， 枚举里面的so，
                除去5个白名单 路径（完美匹配）
                    /system/lib64/libTcpOptimizer.mobiledata.samsung.so
                    /vendor/lib64/libdirect-coredump.so
                    /system/lib64/libdirect-coredump.so
                    /system/lib64/libina.preload.samsung.so
                    /system/vendor/lib64/libNimsWrap.so
                检查是否还有除此之外的so",,
,,,flag3,"使用__NR_openat + fdopen打开/proc/self/maps，使用fgets来读取数据。
                检查读出来的数据是否会包含多个下面的字串：
                 ""jit-cache (deleted)""
                 ""jit-zygote-cache (deleted)""
                 ""/dev/ashmem/dalvik-jit-code""
                 ""/dev/ashmem/jit-zygote-cache""
                 ""[anon:dalvik-jit-code-cache]""
                 ""[anon:dalvik-zygote-jit-code-cache]""
                 （每个字串最多只能有一个，检查是否有单一项，有两个及以上）",,
,,d1,"使用stat/lstat/statfs/access/open/bind
         检查配置里面的 [""x97""][""d""][""1""] 中的文件列表，按配置顺序检查，若检查存在，则记录 顺序索引，
         如，第59个、60个、63个存在，则记录为 “59|60|63”","""/data/downloads/qemu_list.txt"",
""/data/bluestacks.prop"",
""/dev/__properties__/u:object_r:vendor_qemu_adb_prop:s0"",
""/dev/__properties__/u:object_r:vendor_qemu_prop:s0"",
""/dev/qemu_pipe"",
""/dev/socket/qemud"",
""/ro.kernel.android.qemud"",
""/sys/bus/platform/drivers/qemu_pipe"",
""/sys/bus/platform/drivers/qemu_trace"",
""/sys/class/misc/qemu_pipe"",
""/sys/devices/virtual/misc/qemu_pipe"",
""/sys/qemu_trace"",
""/system_ext/bin/init.qemu-adb-keys.sh"",
""/system/bin/qemu_props"",
""/system/framework/libqemu_wl.txt"",
""/system/lib/libc_malloc_debug_qemu.so-arm"",
""/system/lib/libc_malloc_debug_qemu.so"",
""/vendor/bin/qemu-adb-keys"",
""/vendor/bin/qemu-device-state"",
""/vendor/bin/qemu-props"",
""/vendor/lib64/libqemupipe.ranchu.so"",
""/androVM.vbox_graph_mode"",
""/androVM.vbox_dpi"",
""/init.vbox86.rc"",
""/init.svc.vbox86-setup"",
""/init.svc.noxd"",
""/init.svc.microvirtd"",
""/init.svc.droid4x"",
""/data/data/com.androVM.vmconfig"",
""/init.nox.rc"",
""/init.android_x86.rc"",
""/system/bin/androVM_setprop"",
""/system/bin/get_androVM_host"",
""/system/bin/mount.vboxsf"",
""/system/lib/egl/libGLES_emulation.so"",
""/system/lib/egl/libGLESv1_CM_emulation.so"",
""/system/lib/egl/libGLESv2_emulation.so"",
""/system/bin/nox-setprop"",
""/system/bin/droid4x-setprop"",
""/system/bin/ttVM-setprop"",
""/system/lib/egl/libEGL_tiantianVM.so"",
""/system/lib/egl/libGLESv1_CM_tiantianVM.so"",
""/system/lib/egl/libGLESv2_tiantianVM.so"",
""/system/bin/bstcmd_shim"",
""/system/bin/bstfolderd"",
""/system/bin/bstsyncfs"",
""/system/lib/egl/libGLES_bst.so"",
""/system/lib/egl/libGLES_bst.so-arm"",
""/goldfish"",
""/fstab.vbox86"",
""/fstab.nox"",
""/dev/vboxuser"",
""/dev/socket/genyd"",
""/system/bin/windroyedsutemp"",
""/system/lib/egl/libGLES"",
""/system/bin/ttVM-setpropinit.svc.ttVM_x86-setup"",
""/system/lib/egl/libEGL_tiantianVM.sottVM.vbox_dp"",
""/sbin/su"",
""/system/bin/su"",
""/system/xbin/su"",
""/system/bin/magiskinit"",
""/system/bin/magiskpolicy"",
""/system/lib64/libriruloader.so"",
""/sbin/.magisk/"",
""/system/bin/shuamesu"",
""/system/xbin/bdsu"",
""/system/bin/360s"",
""/system/usr/ikm/ikmsu"",
""/system/bin/debuggerd-ku.bak"",
""/system/bin/genybaseband"",
""/system/lib/lic"",
""/system/lib/ccc"",
""/system/lib/.aa"",
""/dev/wgzs"",
""/sbin/.core/img"",
""/sbin/.core/mirror"",
""/sbin/.core/db-0/magisk.db"",
""/data/client.conf"",
""/yunrpc-android.app"",
""/init.hikay960q4.rc"",
""/anbox-init.sh"",
""/openvmi-init.sh"",
""/system/lib64/libriru_edxp.so"",
""/system/lib/libriru_edxp.so"",
""/system/bin/app_process32_xposed"",
""/system/lib/libxposed_art.so"",
""/data/dalvik-cache/x86/data@dalvik-cache@xposed_XTypedArraySuperClass.dex"",
""/system/lib64/<EMAIL>"",
""/system/lib/libmiro_zoomer.so"",
""/sdk/system/execShell"",
""/sys/devcfg/wifimac"",
""/data/system/device_sensors.json"",
""/data/system/d1"",
""/system/addon.d"",
""/data/fakeloc"",
""/sbin/.magisk/modules/riru_lsposed"",
""/sbin/.magisk/modules/zygisk_lsposed"",
""/sbin/.magisk/modules/riru_edxposed"",
""/sbin/.magisk/modules/taichi"",
""/sbin/.magisk/modules/taichi-zygisk"",
""/data/adb/modules/riru_lsposed"",
""/data/adb/modules/zygisk_lsposed"",
""/data/adb/modules/riru_edxposed"",
""/data/adb/modules/taichi"",
""/data/adb/modules/taichi-zygisk"",
""/data/adb/lspd"",
""/data/adb/edxp"",
""/sdcard/Android/data/org.meowcat.edxposed.manager"",
""/sdcard/Android/me.weishu.exp"",
""/sdcard/Android/org.lsposed.manager"",
""/system/bin/app_process.orig"",
""/system/xposed.prop"",
""/system/framework/XposedBridge.jar"",
""/system/lib/libxposed_art.so.no_orig"",
""/system/lib64/libxposed_art.so"",
""/system/lib64/libxposed_art.so.no_orig"",
""/dev/input/event0"",
""/sbin/magisk64"",
""/sbin/magiskhide"",
""/sbin/magiskinit"",
""/sbin/.magisk"",
""/sdcard/Android/data/com.topjohnwu.magisk"",
""/cache/.disable_magisk"",
""/dev/magisk/img"",
""/bin/.magisk"",
""/system/bin/magisk"",
""/cache/magisk.log"",
""/data/adb/magisk"",
""/mnt/vendor/persist/magisk"",
""/system/lib/libzygisk.so"",
""/system/lib64/libzygisk.so"",
""/data/magisk.apk"",
""/data/adb/ksu"",
""/data/adb/apd"",
""/data/adb/kpatch"",
""/system/bin/microvirt-prop"",
""/system/lib/libdroid4x.so"",
""/system/bin/windroyed"",
""/system/bin/microvirtd"",
""/system/bin/nox-prop"",
""/system/bin/ttVM-prop"",
""/system/bin/droid4x-prop"",
""/data/.bluestacks.prop"",
""/data/app/com.bluestacks.appmart-1.apk"",
""/data/app/com.bluestacks.home-1.apk"",
""/data/app/com.bluestacks.searchapp-1.apk"",
""/sys/bus/pci/drivers/vboxguest/module"",
""/sys/bus/pci/drivers/vboxguest/new_id"",
""/sys/bus/pci/drivers/vboxguest/remove_id"",
""/sys/bus/pci/drivers/vboxguest/uevent"",
""/system/bin/androVM-prop"",
""/system/lib/arm/libhoudini.so"",
""/system/lib/libaligl2.so"",
""/system/lib/libalitag.so"",
""/system/lib/libcloudletevent.so"",
""/system/lib/libcloudletos.so"",
""/dev/socket/baseband_genyd"",
""/ueventd.android_x86.rc"",
""/system/lib/libnoxd.so"",
""/system/lib/libnoxspeedup.so"",
""/ueventd.nox.rc"",
""/system/lib/vboxsf.ko"",
""/system/lib/vboxguest.ko"",
""/system/lib/nemuguest.ko"",
""/system/lib/nemusf.ko"",
""/system/lib/nemuvideo.ko"",
""/system/lib/vpipe.ko"",
""/system/lib/vpipe_novt.ko"",
""/dev/haima-rfifo"",
""/dev/haima-wfifo"",
""/system/etc/haima.agent.d"",
""/system/bin/ws-server"",
""/system/app/CloudLauncher"",
""/system/priv-app/MonitorAppOEM"",
""/system/priv-app/RedFingerLauncher"",
""/system/priv-app/RedFingerFileManager"",
""/sys/bus/platform/devices/ionfb_redfinger.0"",
""/sys/devices/platform/ionfb_redfinger.0"",
""/sys/class/redfinger_camera"",
""/sys/class/redfinger_audio"",
""/sys/devices/virtual/redfinger_audio"",
""/sdcard/Android/data/org.appspot.apprtc"",
""/sdcard/Android/data/com.longene.lglocations"",
""/sdcard/Android/data/com.longene.lglocation"",
""/sdcard/Android/data/com.migu.agent"",
""/sdcard/Android/data/com.migu.controller"",
""/sdcard/Android/data/com.redfinger.monitor"",
""/sdcard/Android/data/com.redfinger.filemanager"",
""/data/data/org.appspot.apprtc"",
""/data/data/com.longene.lglocations"",
""/data/data/com.longene.lglocation"",
""/data/data/com.migu.agent"",
""/data/data/com.migu.controller"",
""/data/data/com.redfinger.monitor"",
""/data/data/com.redfinger.filemanager"",
""/system/etc/permissions/monbox.xml"",
""anbox-init.sh"",
""/init.hi3660.rc"",
""/data/android_info.conf"",
""/data/mac"",
""/acct/docker"",
""/sys/fs/cgroup/memory/docker"",
""/dev/cpuset/docker"",
""/dev/cpuctl/docker"",
""/dev/memcg/docker"",
""/dev/cpuset/kube-proxy/"",
""/dev/sdb"",
""/dev/sdc"",
""/lxc/lxcfs/"",
""/system/bin/android_ttcp"",
""/system/bin/eeprom_tools"",
""/system/bin/vcDistributor"",
""/system/bin/vclusters_server"",
""/system/bin/iptables_yp"",
""/system/bin/yp_log"",
""/system/bin/ypremoteview"",
""/system/xbin/yp_init.sh"",
""/system/xbin/ysu"",
""/fstab.pscloud"",
""/init.dundi.rc"",
""/init.pscloud.rc"",
""/system/bin/client"",
""/system/bin/lzbaseband"",
""/system/bin/socat"",
""/sys/class/lginput/"",
""/dev/lginput"",
""/system/bin/lgserver"",
""/system/lib/libRkTeeGatekeeper.so"",
""/vendor/bin/tenc-apk-installer.sh"",
""/vendor/bin/tenc-ethernet.sh"",
""/vendor/etc/fstab.tenc"",
""/vendor/lib/hw/gatekeeper.tenc.so"",
""/vendor/lib/hw/gps.tenc.so"",
""/sys/class/dmi/id/power"",
""/sys/class/dmi/id/product_name"",
""/proc/self/root//data/data/com.f1player/2ndos/rootfs"",
""/x8/config/root.pkg.blacklist"",
""/x8/config/full_vm"",
""/x8.prop"",
""/proc/self/root//data/data/com.x8zs.sandbox/2ndos/rootfs"",
""/vendor/lib64/libtitan-ril.so"",
""/init.titan.rc"",
""/ueventd.titan.rc"",
""/proc/self/root//data/data/com.vphonegaga.faqweb"",
""/proc/self/root//data/data/io.twoyi/rootfs"",
""/proc/self/root//data/data/com.clone.android.dual.space/vm"",
""/data/local/tmp/minicap"",
""/data/local/tmp/minicap.so""",,
,,d4,"通过bind + connect 按顺序 检查本地端口 1990,2021,5555,5620,12345,27042,27043,27047 是否能连通，
         若能连通，则记录 顺序索引。",,,
,,d6,"TODO 可能取配置[""x97""][""d""][""6""] 去做一些事情，但内容为空，逻辑不明。",,,
,,d11,"按顺序使用 __system_property_read 检查以下属性是否存在
	""wg.cust.Build_HOST"" 
	""wg.cust.Build_ID"" 
	""wg.cust.config.phone.bootid"" 
	""wg.cust.hide_xxxx""
	""wg.cust.config.pkg.name"" 
	""wg.cust.Build_DEVICE"" 
	""ro.build.version.sdk"" 
	""ro.product.brand""

若检查存在，则记录索引，如第1个，2个存在，则记录为 “1|2”",,,
,,d9,"TODO 可能取配置[""x97""][""d""][""15""] 去做一些事情，但内容为空，逻辑不明。",,,
,,d10,"TODO 可能取配置[""x97""][""d""][""17""] 去做一些事情，但内容为空，逻辑不明。",,,
,,d12,"TODO 可能取配置[""x97""][""d""][""12""] 去做一些事情，但内容为空，逻辑不明。",,,
,,d13,"TODO 可能取配置[""x97""][""d""][""13""] 去做一些事情，但内容为空，逻辑不明。",,,
,x99,"d1,d10,d11,d12,d13,d4,d6,d9,s1,s10,s11,s2,s3,s4,s5,s6,s7,s8,s9数据点的CRC32 签名值（标准常量）",,,,
x-mini-s1,"数据量62字节的 base64 字节输出
    有4个字节 计数器，每执行一次加1
    有32字节为 sha256(post+x-mini-mua) 的输出变换
    有1个字节是 常量0x1
    有4个字节是补全padding
    TODO 其余21个字节 内容未知",,,,,
x-mini-gid,内容同 x146 - 返回g值,,,,,
x-mini-sig,"数据量32字节的 转64位字串输出
    32字节 源自 sha256(post+url+mua等) 的输出",,,,,
,,,,,,
,,,,,,
,,,,,,
,,,,,,
,,,,,,
,,,,,,
,,,,,,
,,,,,,
,,,,,,
