// Copyright 2021 Google Inc. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
syntax = "proto2";

package file_sections;

message SectionDescriptior {
  // Name of the section (e.g. .rodata)
  optional string name = 1;

  // Size of that section as part of the file.
  optional uint64 file_size = 2;

  // Size of that section when loaded in memory.
  optional uint64 vm_size = 3;
}

message File {
  // Relative path from $OUT_DIR.
  optional string path = 1;

  // File sections.
  repeated SectionDescriptior sections = 2;
}

message FileSizeMetrics {
  repeated File files = 1;
}
