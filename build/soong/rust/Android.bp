package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

bootstrap_go_package {
    name: "soong-rust",
    pkgPath: "android/soong/rust",
    deps: [
        "soong",
        "soong-android",
        "soong-bloaty",
        "soong-cc",
        "soong-rust-config",
    ],
    srcs: [
        "androidmk.go",
        "benchmark.go",
        "binary.go",
        "bindgen.go",
        "builder.go",
        "clippy.go",
        "compiler.go",
        "coverage.go",
        "doc.go",
        "fuzz.go",
        "image.go",
        "library.go",
        "prebuilt.go",
        "proc_macro.go",
        "project_json.go",
        "protobuf.go",
        "rust.go",
        "sanitize.go",
        "source_provider.go",
        "snapshot_utils.go",
        "strip.go",
        "test.go",
        "testing.go",
    ],
    testSrcs: [
        "benchmark_test.go",
        "binary_test.go",
        "bindgen_test.go",
        "builder_test.go",
        "clippy_test.go",
        "compiler_test.go",
        "coverage_test.go",
        "fuzz_test.go",
        "image_test.go",
        "library_test.go",
        "project_json_test.go",
        "protobuf_test.go",
        "rust_test.go",
        "source_provider_test.go",
        "test_test.go",
    ],
    pluginFor: ["soong_build"],
}
