// Code generated by protoc-gen-go. DO NOT EDIT.
// source: metrics.proto

package soong_metrics_proto

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type MetricsBase_BuildVariant int32

const (
	MetricsBase_USER      MetricsBase_BuildVariant = 0
	MetricsBase_USERDEBUG MetricsBase_BuildVariant = 1
	MetricsBase_ENG       MetricsBase_BuildVariant = 2
)

var MetricsBase_BuildVariant_name = map[int32]string{
	0: "USER",
	1: "USERDEBUG",
	2: "ENG",
}

var MetricsBase_BuildVariant_value = map[string]int32{
	"USER":      0,
	"USERDEBUG": 1,
	"ENG":       2,
}

func (x MetricsBase_BuildVariant) Enum() *MetricsBase_BuildVariant {
	p := new(MetricsBase_BuildVariant)
	*p = x
	return p
}

func (x MetricsBase_BuildVariant) String() string {
	return proto.EnumName(MetricsBase_BuildVariant_name, int32(x))
}

func (x *MetricsBase_BuildVariant) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MetricsBase_BuildVariant_value, data, "MetricsBase_BuildVariant")
	if err != nil {
		return err
	}
	*x = MetricsBase_BuildVariant(value)
	return nil
}

func (MetricsBase_BuildVariant) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_6039342a2ba47b72, []int{0, 0}
}

type MetricsBase_Arch int32

const (
	MetricsBase_UNKNOWN MetricsBase_Arch = 0
	MetricsBase_ARM     MetricsBase_Arch = 1
	MetricsBase_ARM64   MetricsBase_Arch = 2
	MetricsBase_X86     MetricsBase_Arch = 3
	MetricsBase_X86_64  MetricsBase_Arch = 4
)

var MetricsBase_Arch_name = map[int32]string{
	0: "UNKNOWN",
	1: "ARM",
	2: "ARM64",
	3: "X86",
	4: "X86_64",
}

var MetricsBase_Arch_value = map[string]int32{
	"UNKNOWN": 0,
	"ARM":     1,
	"ARM64":   2,
	"X86":     3,
	"X86_64":  4,
}

func (x MetricsBase_Arch) Enum() *MetricsBase_Arch {
	p := new(MetricsBase_Arch)
	*p = x
	return p
}

func (x MetricsBase_Arch) String() string {
	return proto.EnumName(MetricsBase_Arch_name, int32(x))
}

func (x *MetricsBase_Arch) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MetricsBase_Arch_value, data, "MetricsBase_Arch")
	if err != nil {
		return err
	}
	*x = MetricsBase_Arch(value)
	return nil
}

func (MetricsBase_Arch) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_6039342a2ba47b72, []int{0, 1}
}

type ModuleTypeInfo_BuildSystem int32

const (
	ModuleTypeInfo_UNKNOWN ModuleTypeInfo_BuildSystem = 0
	ModuleTypeInfo_SOONG   ModuleTypeInfo_BuildSystem = 1
	ModuleTypeInfo_MAKE    ModuleTypeInfo_BuildSystem = 2
)

var ModuleTypeInfo_BuildSystem_name = map[int32]string{
	0: "UNKNOWN",
	1: "SOONG",
	2: "MAKE",
}

var ModuleTypeInfo_BuildSystem_value = map[string]int32{
	"UNKNOWN": 0,
	"SOONG":   1,
	"MAKE":    2,
}

func (x ModuleTypeInfo_BuildSystem) Enum() *ModuleTypeInfo_BuildSystem {
	p := new(ModuleTypeInfo_BuildSystem)
	*p = x
	return p
}

func (x ModuleTypeInfo_BuildSystem) String() string {
	return proto.EnumName(ModuleTypeInfo_BuildSystem_name, int32(x))
}

func (x *ModuleTypeInfo_BuildSystem) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ModuleTypeInfo_BuildSystem_value, data, "ModuleTypeInfo_BuildSystem")
	if err != nil {
		return err
	}
	*x = ModuleTypeInfo_BuildSystem(value)
	return nil
}

func (ModuleTypeInfo_BuildSystem) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_6039342a2ba47b72, []int{5, 0}
}

type MetricsBase struct {
	// Timestamp generated when the build starts.
	BuildDateTimestamp *int64 `protobuf:"varint,1,opt,name=build_date_timestamp,json=buildDateTimestamp" json:"build_date_timestamp,omitempty"`
	// It is usually used to specify the branch name [and release candidate].
	BuildId *string `protobuf:"bytes,2,opt,name=build_id,json=buildId" json:"build_id,omitempty"`
	// The platform version codename, eg. P, Q, REL.
	PlatformVersionCodename *string `protobuf:"bytes,3,opt,name=platform_version_codename,json=platformVersionCodename" json:"platform_version_codename,omitempty"`
	// The target product information, eg. aosp_arm.
	TargetProduct *string `protobuf:"bytes,4,opt,name=target_product,json=targetProduct" json:"target_product,omitempty"`
	// The target build variant information, eg. eng.
	TargetBuildVariant *MetricsBase_BuildVariant `protobuf:"varint,5,opt,name=target_build_variant,json=targetBuildVariant,enum=soong_build_metrics.MetricsBase_BuildVariant,def=2" json:"target_build_variant,omitempty"`
	// The target arch information, eg. arm.
	TargetArch *MetricsBase_Arch `protobuf:"varint,6,opt,name=target_arch,json=targetArch,enum=soong_build_metrics.MetricsBase_Arch,def=0" json:"target_arch,omitempty"`
	// The target arch variant information, eg. armv7-a-neon.
	TargetArchVariant *string `protobuf:"bytes,7,opt,name=target_arch_variant,json=targetArchVariant" json:"target_arch_variant,omitempty"`
	// The target cpu variant information, eg. generic.
	TargetCpuVariant *string `protobuf:"bytes,8,opt,name=target_cpu_variant,json=targetCpuVariant" json:"target_cpu_variant,omitempty"`
	// The host arch information, eg. x86_64.
	HostArch *MetricsBase_Arch `protobuf:"varint,9,opt,name=host_arch,json=hostArch,enum=soong_build_metrics.MetricsBase_Arch,def=0" json:"host_arch,omitempty"`
	// The host 2nd arch information, eg. x86.
	Host_2NdArch *MetricsBase_Arch `protobuf:"varint,10,opt,name=host_2nd_arch,json=host2ndArch,enum=soong_build_metrics.MetricsBase_Arch,def=0" json:"host_2nd_arch,omitempty"`
	// The host os information, eg. linux.
	HostOs *string `protobuf:"bytes,11,opt,name=host_os,json=hostOs" json:"host_os,omitempty"`
	// The host os extra information, eg. Linux-4.17.0-3rodete2-amd64-x86_64-Debian-GNU.
	HostOsExtra *string `protobuf:"bytes,12,opt,name=host_os_extra,json=hostOsExtra" json:"host_os_extra,omitempty"`
	// The host cross os information, eg. windows.
	HostCrossOs *string `protobuf:"bytes,13,opt,name=host_cross_os,json=hostCrossOs" json:"host_cross_os,omitempty"`
	// The host cross arch information, eg. x86.
	HostCrossArch *string `protobuf:"bytes,14,opt,name=host_cross_arch,json=hostCrossArch" json:"host_cross_arch,omitempty"`
	// The host cross 2nd arch information, eg. x86_64.
	HostCross_2NdArch *string `protobuf:"bytes,15,opt,name=host_cross_2nd_arch,json=hostCross2ndArch" json:"host_cross_2nd_arch,omitempty"`
	// The directory for generated built artifacts installation, eg. out.
	OutDir *string `protobuf:"bytes,16,opt,name=out_dir,json=outDir" json:"out_dir,omitempty"`
	// The metrics for calling various tools (microfactory) before Soong_UI starts.
	SetupTools []*PerfInfo `protobuf:"bytes,17,rep,name=setup_tools,json=setupTools" json:"setup_tools,omitempty"`
	// The metrics for calling Kati by multiple times.
	KatiRuns []*PerfInfo `protobuf:"bytes,18,rep,name=kati_runs,json=katiRuns" json:"kati_runs,omitempty"`
	// The metrics for calling Soong.
	SoongRuns []*PerfInfo `protobuf:"bytes,19,rep,name=soong_runs,json=soongRuns" json:"soong_runs,omitempty"`
	// The metrics for calling Ninja.
	NinjaRuns []*PerfInfo `protobuf:"bytes,20,rep,name=ninja_runs,json=ninjaRuns" json:"ninja_runs,omitempty"`
	// The metrics for the whole build
	Total             *PerfInfo          `protobuf:"bytes,21,opt,name=total" json:"total,omitempty"`
	SoongBuildMetrics *SoongBuildMetrics `protobuf:"bytes,22,opt,name=soong_build_metrics,json=soongBuildMetrics" json:"soong_build_metrics,omitempty"`
	BuildConfig       *BuildConfig       `protobuf:"bytes,23,opt,name=build_config,json=buildConfig" json:"build_config,omitempty"`
	// The hostname of the machine.
	Hostname *string `protobuf:"bytes,24,opt,name=hostname" json:"hostname,omitempty"`
	// The system resource information such as total physical memory.
	SystemResourceInfo *SystemResourceInfo `protobuf:"bytes,25,opt,name=system_resource_info,json=systemResourceInfo" json:"system_resource_info,omitempty"`
	// The build command that the user entered to the build system.
	BuildCommand *string `protobuf:"bytes,26,opt,name=build_command,json=buildCommand" json:"build_command,omitempty"`
	// The metrics for calling Bazel.
	BazelRuns            []*PerfInfo `protobuf:"bytes,27,rep,name=bazel_runs,json=bazelRuns" json:"bazel_runs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *MetricsBase) Reset()         { *m = MetricsBase{} }
func (m *MetricsBase) String() string { return proto.CompactTextString(m) }
func (*MetricsBase) ProtoMessage()    {}
func (*MetricsBase) Descriptor() ([]byte, []int) {
	return fileDescriptor_6039342a2ba47b72, []int{0}
}

func (m *MetricsBase) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MetricsBase.Unmarshal(m, b)
}
func (m *MetricsBase) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MetricsBase.Marshal(b, m, deterministic)
}
func (m *MetricsBase) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricsBase.Merge(m, src)
}
func (m *MetricsBase) XXX_Size() int {
	return xxx_messageInfo_MetricsBase.Size(m)
}
func (m *MetricsBase) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricsBase.DiscardUnknown(m)
}

var xxx_messageInfo_MetricsBase proto.InternalMessageInfo

const Default_MetricsBase_TargetBuildVariant MetricsBase_BuildVariant = MetricsBase_ENG
const Default_MetricsBase_TargetArch MetricsBase_Arch = MetricsBase_UNKNOWN
const Default_MetricsBase_HostArch MetricsBase_Arch = MetricsBase_UNKNOWN
const Default_MetricsBase_Host_2NdArch MetricsBase_Arch = MetricsBase_UNKNOWN

func (m *MetricsBase) GetBuildDateTimestamp() int64 {
	if m != nil && m.BuildDateTimestamp != nil {
		return *m.BuildDateTimestamp
	}
	return 0
}

func (m *MetricsBase) GetBuildId() string {
	if m != nil && m.BuildId != nil {
		return *m.BuildId
	}
	return ""
}

func (m *MetricsBase) GetPlatformVersionCodename() string {
	if m != nil && m.PlatformVersionCodename != nil {
		return *m.PlatformVersionCodename
	}
	return ""
}

func (m *MetricsBase) GetTargetProduct() string {
	if m != nil && m.TargetProduct != nil {
		return *m.TargetProduct
	}
	return ""
}

func (m *MetricsBase) GetTargetBuildVariant() MetricsBase_BuildVariant {
	if m != nil && m.TargetBuildVariant != nil {
		return *m.TargetBuildVariant
	}
	return Default_MetricsBase_TargetBuildVariant
}

func (m *MetricsBase) GetTargetArch() MetricsBase_Arch {
	if m != nil && m.TargetArch != nil {
		return *m.TargetArch
	}
	return Default_MetricsBase_TargetArch
}

func (m *MetricsBase) GetTargetArchVariant() string {
	if m != nil && m.TargetArchVariant != nil {
		return *m.TargetArchVariant
	}
	return ""
}

func (m *MetricsBase) GetTargetCpuVariant() string {
	if m != nil && m.TargetCpuVariant != nil {
		return *m.TargetCpuVariant
	}
	return ""
}

func (m *MetricsBase) GetHostArch() MetricsBase_Arch {
	if m != nil && m.HostArch != nil {
		return *m.HostArch
	}
	return Default_MetricsBase_HostArch
}

func (m *MetricsBase) GetHost_2NdArch() MetricsBase_Arch {
	if m != nil && m.Host_2NdArch != nil {
		return *m.Host_2NdArch
	}
	return Default_MetricsBase_Host_2NdArch
}

func (m *MetricsBase) GetHostOs() string {
	if m != nil && m.HostOs != nil {
		return *m.HostOs
	}
	return ""
}

func (m *MetricsBase) GetHostOsExtra() string {
	if m != nil && m.HostOsExtra != nil {
		return *m.HostOsExtra
	}
	return ""
}

func (m *MetricsBase) GetHostCrossOs() string {
	if m != nil && m.HostCrossOs != nil {
		return *m.HostCrossOs
	}
	return ""
}

func (m *MetricsBase) GetHostCrossArch() string {
	if m != nil && m.HostCrossArch != nil {
		return *m.HostCrossArch
	}
	return ""
}

func (m *MetricsBase) GetHostCross_2NdArch() string {
	if m != nil && m.HostCross_2NdArch != nil {
		return *m.HostCross_2NdArch
	}
	return ""
}

func (m *MetricsBase) GetOutDir() string {
	if m != nil && m.OutDir != nil {
		return *m.OutDir
	}
	return ""
}

func (m *MetricsBase) GetSetupTools() []*PerfInfo {
	if m != nil {
		return m.SetupTools
	}
	return nil
}

func (m *MetricsBase) GetKatiRuns() []*PerfInfo {
	if m != nil {
		return m.KatiRuns
	}
	return nil
}

func (m *MetricsBase) GetSoongRuns() []*PerfInfo {
	if m != nil {
		return m.SoongRuns
	}
	return nil
}

func (m *MetricsBase) GetNinjaRuns() []*PerfInfo {
	if m != nil {
		return m.NinjaRuns
	}
	return nil
}

func (m *MetricsBase) GetTotal() *PerfInfo {
	if m != nil {
		return m.Total
	}
	return nil
}

func (m *MetricsBase) GetSoongBuildMetrics() *SoongBuildMetrics {
	if m != nil {
		return m.SoongBuildMetrics
	}
	return nil
}

func (m *MetricsBase) GetBuildConfig() *BuildConfig {
	if m != nil {
		return m.BuildConfig
	}
	return nil
}

func (m *MetricsBase) GetHostname() string {
	if m != nil && m.Hostname != nil {
		return *m.Hostname
	}
	return ""
}

func (m *MetricsBase) GetSystemResourceInfo() *SystemResourceInfo {
	if m != nil {
		return m.SystemResourceInfo
	}
	return nil
}

func (m *MetricsBase) GetBuildCommand() string {
	if m != nil && m.BuildCommand != nil {
		return *m.BuildCommand
	}
	return ""
}

func (m *MetricsBase) GetBazelRuns() []*PerfInfo {
	if m != nil {
		return m.BazelRuns
	}
	return nil
}

type BuildConfig struct {
	UseGoma              *bool    `protobuf:"varint,1,opt,name=use_goma,json=useGoma" json:"use_goma,omitempty"`
	UseRbe               *bool    `protobuf:"varint,2,opt,name=use_rbe,json=useRbe" json:"use_rbe,omitempty"`
	ForceUseGoma         *bool    `protobuf:"varint,3,opt,name=force_use_goma,json=forceUseGoma" json:"force_use_goma,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuildConfig) Reset()         { *m = BuildConfig{} }
func (m *BuildConfig) String() string { return proto.CompactTextString(m) }
func (*BuildConfig) ProtoMessage()    {}
func (*BuildConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_6039342a2ba47b72, []int{1}
}

func (m *BuildConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuildConfig.Unmarshal(m, b)
}
func (m *BuildConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuildConfig.Marshal(b, m, deterministic)
}
func (m *BuildConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuildConfig.Merge(m, src)
}
func (m *BuildConfig) XXX_Size() int {
	return xxx_messageInfo_BuildConfig.Size(m)
}
func (m *BuildConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_BuildConfig.DiscardUnknown(m)
}

var xxx_messageInfo_BuildConfig proto.InternalMessageInfo

func (m *BuildConfig) GetUseGoma() bool {
	if m != nil && m.UseGoma != nil {
		return *m.UseGoma
	}
	return false
}

func (m *BuildConfig) GetUseRbe() bool {
	if m != nil && m.UseRbe != nil {
		return *m.UseRbe
	}
	return false
}

func (m *BuildConfig) GetForceUseGoma() bool {
	if m != nil && m.ForceUseGoma != nil {
		return *m.ForceUseGoma
	}
	return false
}

type SystemResourceInfo struct {
	// The total physical memory in bytes.
	TotalPhysicalMemory *uint64 `protobuf:"varint,1,opt,name=total_physical_memory,json=totalPhysicalMemory" json:"total_physical_memory,omitempty"`
	// The total of available cores for building
	AvailableCpus        *int32   `protobuf:"varint,2,opt,name=available_cpus,json=availableCpus" json:"available_cpus,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SystemResourceInfo) Reset()         { *m = SystemResourceInfo{} }
func (m *SystemResourceInfo) String() string { return proto.CompactTextString(m) }
func (*SystemResourceInfo) ProtoMessage()    {}
func (*SystemResourceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_6039342a2ba47b72, []int{2}
}

func (m *SystemResourceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SystemResourceInfo.Unmarshal(m, b)
}
func (m *SystemResourceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SystemResourceInfo.Marshal(b, m, deterministic)
}
func (m *SystemResourceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SystemResourceInfo.Merge(m, src)
}
func (m *SystemResourceInfo) XXX_Size() int {
	return xxx_messageInfo_SystemResourceInfo.Size(m)
}
func (m *SystemResourceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SystemResourceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SystemResourceInfo proto.InternalMessageInfo

func (m *SystemResourceInfo) GetTotalPhysicalMemory() uint64 {
	if m != nil && m.TotalPhysicalMemory != nil {
		return *m.TotalPhysicalMemory
	}
	return 0
}

func (m *SystemResourceInfo) GetAvailableCpus() int32 {
	if m != nil && m.AvailableCpus != nil {
		return *m.AvailableCpus
	}
	return 0
}

type PerfInfo struct {
	// The description for the phase/action/part while the tool running.
	Desc *string `protobuf:"bytes,1,opt,name=desc" json:"desc,omitempty"`
	// The name for the running phase/action/part.
	Name *string `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	// The absolute start time.
	// The number of nanoseconds elapsed since January 1, 1970 UTC.
	StartTime *uint64 `protobuf:"varint,3,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	// The real running time.
	// The number of nanoseconds elapsed since start_time.
	RealTime *uint64 `protobuf:"varint,4,opt,name=real_time,json=realTime" json:"real_time,omitempty"`
	// The number of MB for memory use (deprecated as it is too generic).
	MemoryUse *uint64 `protobuf:"varint,5,opt,name=memory_use,json=memoryUse" json:"memory_use,omitempty"` // Deprecated: Do not use.
	// The resource information of each executed process.
	ProcessesResourceInfo []*ProcessResourceInfo `protobuf:"bytes,6,rep,name=processes_resource_info,json=processesResourceInfo" json:"processes_resource_info,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}               `json:"-"`
	XXX_unrecognized      []byte                 `json:"-"`
	XXX_sizecache         int32                  `json:"-"`
}

func (m *PerfInfo) Reset()         { *m = PerfInfo{} }
func (m *PerfInfo) String() string { return proto.CompactTextString(m) }
func (*PerfInfo) ProtoMessage()    {}
func (*PerfInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_6039342a2ba47b72, []int{3}
}

func (m *PerfInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PerfInfo.Unmarshal(m, b)
}
func (m *PerfInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PerfInfo.Marshal(b, m, deterministic)
}
func (m *PerfInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PerfInfo.Merge(m, src)
}
func (m *PerfInfo) XXX_Size() int {
	return xxx_messageInfo_PerfInfo.Size(m)
}
func (m *PerfInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PerfInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PerfInfo proto.InternalMessageInfo

func (m *PerfInfo) GetDesc() string {
	if m != nil && m.Desc != nil {
		return *m.Desc
	}
	return ""
}

func (m *PerfInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *PerfInfo) GetStartTime() uint64 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *PerfInfo) GetRealTime() uint64 {
	if m != nil && m.RealTime != nil {
		return *m.RealTime
	}
	return 0
}

// Deprecated: Do not use.
func (m *PerfInfo) GetMemoryUse() uint64 {
	if m != nil && m.MemoryUse != nil {
		return *m.MemoryUse
	}
	return 0
}

func (m *PerfInfo) GetProcessesResourceInfo() []*ProcessResourceInfo {
	if m != nil {
		return m.ProcessesResourceInfo
	}
	return nil
}

type ProcessResourceInfo struct {
	// The name of the process for identification.
	Name *string `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	// The amount of time spent executing in user space in microseconds.
	UserTimeMicros *uint64 `protobuf:"varint,2,opt,name=user_time_micros,json=userTimeMicros" json:"user_time_micros,omitempty"`
	// The amount of time spent executing in kernel mode in microseconds.
	SystemTimeMicros *uint64 `protobuf:"varint,3,opt,name=system_time_micros,json=systemTimeMicros" json:"system_time_micros,omitempty"`
	// The maximum resident set size memory used in kilobytes.
	MaxRssKb *uint64 `protobuf:"varint,4,opt,name=max_rss_kb,json=maxRssKb" json:"max_rss_kb,omitempty"`
	// The number of minor page faults serviced without any I/O activity.
	MinorPageFaults *uint64 `protobuf:"varint,5,opt,name=minor_page_faults,json=minorPageFaults" json:"minor_page_faults,omitempty"`
	// The number of major page faults serviced that required I/O activity.
	MajorPageFaults *uint64 `protobuf:"varint,6,opt,name=major_page_faults,json=majorPageFaults" json:"major_page_faults,omitempty"`
	// Total IO input in kilobytes.
	IoInputKb *uint64 `protobuf:"varint,7,opt,name=io_input_kb,json=ioInputKb" json:"io_input_kb,omitempty"`
	// Total IO output in kilobytes.
	IoOutputKb *uint64 `protobuf:"varint,8,opt,name=io_output_kb,json=ioOutputKb" json:"io_output_kb,omitempty"`
	// The number of voluntary context switches
	VoluntaryContextSwitches *uint64 `protobuf:"varint,9,opt,name=voluntary_context_switches,json=voluntaryContextSwitches" json:"voluntary_context_switches,omitempty"`
	// The number of involuntary context switches
	InvoluntaryContextSwitches *uint64  `protobuf:"varint,10,opt,name=involuntary_context_switches,json=involuntaryContextSwitches" json:"involuntary_context_switches,omitempty"`
	XXX_NoUnkeyedLiteral       struct{} `json:"-"`
	XXX_unrecognized           []byte   `json:"-"`
	XXX_sizecache              int32    `json:"-"`
}

func (m *ProcessResourceInfo) Reset()         { *m = ProcessResourceInfo{} }
func (m *ProcessResourceInfo) String() string { return proto.CompactTextString(m) }
func (*ProcessResourceInfo) ProtoMessage()    {}
func (*ProcessResourceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_6039342a2ba47b72, []int{4}
}

func (m *ProcessResourceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcessResourceInfo.Unmarshal(m, b)
}
func (m *ProcessResourceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcessResourceInfo.Marshal(b, m, deterministic)
}
func (m *ProcessResourceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcessResourceInfo.Merge(m, src)
}
func (m *ProcessResourceInfo) XXX_Size() int {
	return xxx_messageInfo_ProcessResourceInfo.Size(m)
}
func (m *ProcessResourceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcessResourceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ProcessResourceInfo proto.InternalMessageInfo

func (m *ProcessResourceInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *ProcessResourceInfo) GetUserTimeMicros() uint64 {
	if m != nil && m.UserTimeMicros != nil {
		return *m.UserTimeMicros
	}
	return 0
}

func (m *ProcessResourceInfo) GetSystemTimeMicros() uint64 {
	if m != nil && m.SystemTimeMicros != nil {
		return *m.SystemTimeMicros
	}
	return 0
}

func (m *ProcessResourceInfo) GetMaxRssKb() uint64 {
	if m != nil && m.MaxRssKb != nil {
		return *m.MaxRssKb
	}
	return 0
}

func (m *ProcessResourceInfo) GetMinorPageFaults() uint64 {
	if m != nil && m.MinorPageFaults != nil {
		return *m.MinorPageFaults
	}
	return 0
}

func (m *ProcessResourceInfo) GetMajorPageFaults() uint64 {
	if m != nil && m.MajorPageFaults != nil {
		return *m.MajorPageFaults
	}
	return 0
}

func (m *ProcessResourceInfo) GetIoInputKb() uint64 {
	if m != nil && m.IoInputKb != nil {
		return *m.IoInputKb
	}
	return 0
}

func (m *ProcessResourceInfo) GetIoOutputKb() uint64 {
	if m != nil && m.IoOutputKb != nil {
		return *m.IoOutputKb
	}
	return 0
}

func (m *ProcessResourceInfo) GetVoluntaryContextSwitches() uint64 {
	if m != nil && m.VoluntaryContextSwitches != nil {
		return *m.VoluntaryContextSwitches
	}
	return 0
}

func (m *ProcessResourceInfo) GetInvoluntaryContextSwitches() uint64 {
	if m != nil && m.InvoluntaryContextSwitches != nil {
		return *m.InvoluntaryContextSwitches
	}
	return 0
}

type ModuleTypeInfo struct {
	// The build system, eg. Soong or Make.
	BuildSystem *ModuleTypeInfo_BuildSystem `protobuf:"varint,1,opt,name=build_system,json=buildSystem,enum=soong_build_metrics.ModuleTypeInfo_BuildSystem,def=0" json:"build_system,omitempty"`
	// The module type, eg. java_library, cc_binary, and etc.
	ModuleType *string `protobuf:"bytes,2,opt,name=module_type,json=moduleType" json:"module_type,omitempty"`
	// The number of logical modules.
	NumOfModules         *uint32  `protobuf:"varint,3,opt,name=num_of_modules,json=numOfModules" json:"num_of_modules,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModuleTypeInfo) Reset()         { *m = ModuleTypeInfo{} }
func (m *ModuleTypeInfo) String() string { return proto.CompactTextString(m) }
func (*ModuleTypeInfo) ProtoMessage()    {}
func (*ModuleTypeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_6039342a2ba47b72, []int{5}
}

func (m *ModuleTypeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleTypeInfo.Unmarshal(m, b)
}
func (m *ModuleTypeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleTypeInfo.Marshal(b, m, deterministic)
}
func (m *ModuleTypeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleTypeInfo.Merge(m, src)
}
func (m *ModuleTypeInfo) XXX_Size() int {
	return xxx_messageInfo_ModuleTypeInfo.Size(m)
}
func (m *ModuleTypeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleTypeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleTypeInfo proto.InternalMessageInfo

const Default_ModuleTypeInfo_BuildSystem ModuleTypeInfo_BuildSystem = ModuleTypeInfo_UNKNOWN

func (m *ModuleTypeInfo) GetBuildSystem() ModuleTypeInfo_BuildSystem {
	if m != nil && m.BuildSystem != nil {
		return *m.BuildSystem
	}
	return Default_ModuleTypeInfo_BuildSystem
}

func (m *ModuleTypeInfo) GetModuleType() string {
	if m != nil && m.ModuleType != nil {
		return *m.ModuleType
	}
	return ""
}

func (m *ModuleTypeInfo) GetNumOfModules() uint32 {
	if m != nil && m.NumOfModules != nil {
		return *m.NumOfModules
	}
	return 0
}

type CriticalUserJourneyMetrics struct {
	// The name of a critical user journey test.
	Name *string `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	// The metrics produced when running the critical user journey test.
	Metrics              *MetricsBase `protobuf:"bytes,2,opt,name=metrics" json:"metrics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CriticalUserJourneyMetrics) Reset()         { *m = CriticalUserJourneyMetrics{} }
func (m *CriticalUserJourneyMetrics) String() string { return proto.CompactTextString(m) }
func (*CriticalUserJourneyMetrics) ProtoMessage()    {}
func (*CriticalUserJourneyMetrics) Descriptor() ([]byte, []int) {
	return fileDescriptor_6039342a2ba47b72, []int{6}
}

func (m *CriticalUserJourneyMetrics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CriticalUserJourneyMetrics.Unmarshal(m, b)
}
func (m *CriticalUserJourneyMetrics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CriticalUserJourneyMetrics.Marshal(b, m, deterministic)
}
func (m *CriticalUserJourneyMetrics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CriticalUserJourneyMetrics.Merge(m, src)
}
func (m *CriticalUserJourneyMetrics) XXX_Size() int {
	return xxx_messageInfo_CriticalUserJourneyMetrics.Size(m)
}
func (m *CriticalUserJourneyMetrics) XXX_DiscardUnknown() {
	xxx_messageInfo_CriticalUserJourneyMetrics.DiscardUnknown(m)
}

var xxx_messageInfo_CriticalUserJourneyMetrics proto.InternalMessageInfo

func (m *CriticalUserJourneyMetrics) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CriticalUserJourneyMetrics) GetMetrics() *MetricsBase {
	if m != nil {
		return m.Metrics
	}
	return nil
}

type CriticalUserJourneysMetrics struct {
	// A set of metrics from a run of the critical user journey tests.
	Cujs                 []*CriticalUserJourneyMetrics `protobuf:"bytes,1,rep,name=cujs" json:"cujs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *CriticalUserJourneysMetrics) Reset()         { *m = CriticalUserJourneysMetrics{} }
func (m *CriticalUserJourneysMetrics) String() string { return proto.CompactTextString(m) }
func (*CriticalUserJourneysMetrics) ProtoMessage()    {}
func (*CriticalUserJourneysMetrics) Descriptor() ([]byte, []int) {
	return fileDescriptor_6039342a2ba47b72, []int{7}
}

func (m *CriticalUserJourneysMetrics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CriticalUserJourneysMetrics.Unmarshal(m, b)
}
func (m *CriticalUserJourneysMetrics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CriticalUserJourneysMetrics.Marshal(b, m, deterministic)
}
func (m *CriticalUserJourneysMetrics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CriticalUserJourneysMetrics.Merge(m, src)
}
func (m *CriticalUserJourneysMetrics) XXX_Size() int {
	return xxx_messageInfo_CriticalUserJourneysMetrics.Size(m)
}
func (m *CriticalUserJourneysMetrics) XXX_DiscardUnknown() {
	xxx_messageInfo_CriticalUserJourneysMetrics.DiscardUnknown(m)
}

var xxx_messageInfo_CriticalUserJourneysMetrics proto.InternalMessageInfo

func (m *CriticalUserJourneysMetrics) GetCujs() []*CriticalUserJourneyMetrics {
	if m != nil {
		return m.Cujs
	}
	return nil
}

type SoongBuildMetrics struct {
	// The number of modules handled by soong_build.
	Modules *uint32 `protobuf:"varint,1,opt,name=modules" json:"modules,omitempty"`
	// The total number of variants handled by soong_build.
	Variants *uint32 `protobuf:"varint,2,opt,name=variants" json:"variants,omitempty"`
	// The total number of allocations in soong_build.
	TotalAllocCount *uint64 `protobuf:"varint,3,opt,name=total_alloc_count,json=totalAllocCount" json:"total_alloc_count,omitempty"`
	// The total size of allocations in soong_build in bytes.
	TotalAllocSize *uint64 `protobuf:"varint,4,opt,name=total_alloc_size,json=totalAllocSize" json:"total_alloc_size,omitempty"`
	// The approximate maximum size of the heap in soong_build in bytes.
	MaxHeapSize          *uint64  `protobuf:"varint,5,opt,name=max_heap_size,json=maxHeapSize" json:"max_heap_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SoongBuildMetrics) Reset()         { *m = SoongBuildMetrics{} }
func (m *SoongBuildMetrics) String() string { return proto.CompactTextString(m) }
func (*SoongBuildMetrics) ProtoMessage()    {}
func (*SoongBuildMetrics) Descriptor() ([]byte, []int) {
	return fileDescriptor_6039342a2ba47b72, []int{8}
}

func (m *SoongBuildMetrics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SoongBuildMetrics.Unmarshal(m, b)
}
func (m *SoongBuildMetrics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SoongBuildMetrics.Marshal(b, m, deterministic)
}
func (m *SoongBuildMetrics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SoongBuildMetrics.Merge(m, src)
}
func (m *SoongBuildMetrics) XXX_Size() int {
	return xxx_messageInfo_SoongBuildMetrics.Size(m)
}
func (m *SoongBuildMetrics) XXX_DiscardUnknown() {
	xxx_messageInfo_SoongBuildMetrics.DiscardUnknown(m)
}

var xxx_messageInfo_SoongBuildMetrics proto.InternalMessageInfo

func (m *SoongBuildMetrics) GetModules() uint32 {
	if m != nil && m.Modules != nil {
		return *m.Modules
	}
	return 0
}

func (m *SoongBuildMetrics) GetVariants() uint32 {
	if m != nil && m.Variants != nil {
		return *m.Variants
	}
	return 0
}

func (m *SoongBuildMetrics) GetTotalAllocCount() uint64 {
	if m != nil && m.TotalAllocCount != nil {
		return *m.TotalAllocCount
	}
	return 0
}

func (m *SoongBuildMetrics) GetTotalAllocSize() uint64 {
	if m != nil && m.TotalAllocSize != nil {
		return *m.TotalAllocSize
	}
	return 0
}

func (m *SoongBuildMetrics) GetMaxHeapSize() uint64 {
	if m != nil && m.MaxHeapSize != nil {
		return *m.MaxHeapSize
	}
	return 0
}

func init() {
	proto.RegisterEnum("soong_build_metrics.MetricsBase_BuildVariant", MetricsBase_BuildVariant_name, MetricsBase_BuildVariant_value)
	proto.RegisterEnum("soong_build_metrics.MetricsBase_Arch", MetricsBase_Arch_name, MetricsBase_Arch_value)
	proto.RegisterEnum("soong_build_metrics.ModuleTypeInfo_BuildSystem", ModuleTypeInfo_BuildSystem_name, ModuleTypeInfo_BuildSystem_value)
	proto.RegisterType((*MetricsBase)(nil), "soong_build_metrics.MetricsBase")
	proto.RegisterType((*BuildConfig)(nil), "soong_build_metrics.BuildConfig")
	proto.RegisterType((*SystemResourceInfo)(nil), "soong_build_metrics.SystemResourceInfo")
	proto.RegisterType((*PerfInfo)(nil), "soong_build_metrics.PerfInfo")
	proto.RegisterType((*ProcessResourceInfo)(nil), "soong_build_metrics.ProcessResourceInfo")
	proto.RegisterType((*ModuleTypeInfo)(nil), "soong_build_metrics.ModuleTypeInfo")
	proto.RegisterType((*CriticalUserJourneyMetrics)(nil), "soong_build_metrics.CriticalUserJourneyMetrics")
	proto.RegisterType((*CriticalUserJourneysMetrics)(nil), "soong_build_metrics.CriticalUserJourneysMetrics")
	proto.RegisterType((*SoongBuildMetrics)(nil), "soong_build_metrics.SoongBuildMetrics")
}

func init() {
	proto.RegisterFile("metrics.proto", fileDescriptor_6039342a2ba47b72)
}

var fileDescriptor_6039342a2ba47b72 = []byte{
	// 1380 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x57, 0xef, 0x52, 0x1b, 0x37,
	0x10, 0x8f, 0xc1, 0x60, 0x7b, 0xfd, 0x07, 0x23, 0xa0, 0x5c, 0x48, 0xd2, 0x52, 0xb7, 0x49, 0x99,
	0x4e, 0x43, 0x32, 0x34, 0xc3, 0x64, 0x98, 0x4c, 0xa7, 0xe0, 0xd0, 0x34, 0x65, 0xc0, 0x8c, 0x08,
	0x69, 0xda, 0x7e, 0x50, 0xe5, 0xb3, 0x0c, 0x97, 0xdc, 0x9d, 0x6e, 0x24, 0x1d, 0xc5, 0x79, 0xb3,
	0x7e, 0xee, 0x4b, 0xf4, 0x05, 0xfa, 0x04, 0x7d, 0x81, 0x8e, 0x56, 0x77, 0xe6, 0x20, 0x6e, 0xc2,
	0xe4, 0xdb, 0xe9, 0xb7, 0xbf, 0xdf, 0x6a, 0xb5, 0xd2, 0xee, 0xda, 0xd0, 0x8c, 0x84, 0x51, 0x81,
	0xaf, 0xd7, 0x13, 0x25, 0x8d, 0x24, 0x0b, 0x5a, 0xca, 0xf8, 0x84, 0xf5, 0xd3, 0x20, 0x1c, 0xb0,
	0xcc, 0xd4, 0xf9, 0xbb, 0x01, 0xf5, 0x7d, 0xf7, 0xbd, 0xc3, 0xb5, 0x20, 0x0f, 0x61, 0xd1, 0x11,
	0x06, 0xdc, 0x08, 0x66, 0x82, 0x48, 0x68, 0xc3, 0xa3, 0xc4, 0x2b, 0xad, 0x96, 0xd6, 0xa6, 0x29,
	0x41, 0xdb, 0x53, 0x6e, 0xc4, 0x8b, 0xdc, 0x42, 0x6e, 0x42, 0xd5, 0x29, 0x82, 0x81, 0x37, 0xb5,
	0x5a, 0x5a, 0xab, 0xd1, 0x0a, 0xae, 0x9f, 0x0f, 0xc8, 0x16, 0xdc, 0x4c, 0x42, 0x6e, 0x86, 0x52,
	0x45, 0xec, 0x4c, 0x28, 0x1d, 0xc8, 0x98, 0xf9, 0x72, 0x20, 0x62, 0x1e, 0x09, 0x6f, 0x1a, 0xb9,
	0xcb, 0x39, 0xe1, 0xa5, 0xb3, 0x77, 0x33, 0x33, 0xb9, 0x0b, 0x2d, 0xc3, 0xd5, 0x89, 0x30, 0x2c,
	0x51, 0x72, 0x90, 0xfa, 0xc6, 0x2b, 0xa3, 0xa0, 0xe9, 0xd0, 0x43, 0x07, 0x92, 0x01, 0x2c, 0x66,
	0x34, 0x17, 0xc4, 0x19, 0x57, 0x01, 0x8f, 0x8d, 0x37, 0xb3, 0x5a, 0x5a, 0x6b, 0x6d, 0xdc, 0x5f,
	0x9f, 0x70, 0xe6, 0xf5, 0xc2, 0x79, 0xd7, 0x77, 0xac, 0xe5, 0xa5, 0x13, 0x6d, 0x4d, 0xef, 0x1e,
	0x3c, 0xa3, 0xc4, 0xf9, 0x2b, 0x1a, 0x48, 0x0f, 0xea, 0xd9, 0x2e, 0x5c, 0xf9, 0xa7, 0xde, 0x2c,
	0x3a, 0xbf, 0xfb, 0x41, 0xe7, 0xdb, 0xca, 0x3f, 0xdd, 0xaa, 0x1c, 0x1f, 0xec, 0x1d, 0xf4, 0x7e,
	0x3e, 0xa0, 0xe0, 0x5c, 0x58, 0x90, 0xac, 0xc3, 0x42, 0xc1, 0xe1, 0x38, 0xea, 0x0a, 0x1e, 0x71,
	0xfe, 0x82, 0x98, 0x07, 0xf0, 0x0d, 0x64, 0x61, 0x31, 0x3f, 0x49, 0xc7, 0xf4, 0x2a, 0xd2, 0xdb,
	0xce, 0xd2, 0x4d, 0xd2, 0x9c, 0xbd, 0x07, 0xb5, 0x53, 0xa9, 0xb3, 0x60, 0x6b, 0x1f, 0x15, 0x6c,
	0xd5, 0x3a, 0xc0, 0x50, 0x29, 0x34, 0xd1, 0xd9, 0x46, 0x3c, 0x70, 0x0e, 0xe1, 0xa3, 0x1c, 0xd6,
	0xad, 0x93, 0x8d, 0x78, 0x80, 0x3e, 0x97, 0xa1, 0x82, 0x3e, 0xa5, 0xf6, 0xea, 0x78, 0x86, 0x59,
	0xbb, 0xec, 0x69, 0xd2, 0xc9, 0x36, 0x93, 0x9a, 0x89, 0x73, 0xa3, 0xb8, 0xd7, 0x40, 0x73, 0xdd,
	0x99, 0x77, 0x2d, 0x34, 0xe6, 0xf8, 0x4a, 0x6a, 0x6d, 0x5d, 0x34, 0x2f, 0x38, 0x5d, 0x8b, 0xf5,
	0x34, 0xb9, 0x07, 0x73, 0x05, 0x0e, 0x86, 0xdd, 0x72, 0xcf, 0x67, 0xcc, 0xc2, 0x40, 0xee, 0xc3,
	0x42, 0x81, 0x37, 0x3e, 0xe2, 0x9c, 0x4b, 0xec, 0x98, 0x5b, 0x88, 0x5b, 0xa6, 0x86, 0x0d, 0x02,
	0xe5, 0xb5, 0x5d, 0xdc, 0x32, 0x35, 0x4f, 0x03, 0x45, 0xbe, 0x83, 0xba, 0x16, 0x26, 0x4d, 0x98,
	0x91, 0x32, 0xd4, 0xde, 0xfc, 0xea, 0xf4, 0x5a, 0x7d, 0xe3, 0xce, 0xc4, 0x14, 0x1d, 0x0a, 0x35,
	0x7c, 0x1e, 0x0f, 0x25, 0x05, 0x54, 0xbc, 0xb0, 0x02, 0xb2, 0x05, 0xb5, 0x37, 0xdc, 0x04, 0x4c,
	0xa5, 0xb1, 0xf6, 0xc8, 0x75, 0xd4, 0x55, 0xcb, 0xa7, 0x69, 0xac, 0xc9, 0x13, 0x00, 0xc7, 0x44,
	0xf1, 0xc2, 0x75, 0xc4, 0x35, 0xb4, 0xe6, 0xea, 0x38, 0x88, 0x5f, 0x73, 0xa7, 0x5e, 0xbc, 0x96,
	0x1a, 0x05, 0xa8, 0xfe, 0x16, 0x66, 0x8c, 0x34, 0x3c, 0xf4, 0x96, 0x56, 0x4b, 0x1f, 0x16, 0x3a,
	0x2e, 0x79, 0x09, 0x93, 0x5a, 0x91, 0xf7, 0x09, 0xba, 0xb8, 0x37, 0xd1, 0xc5, 0x91, 0xc5, 0xb0,
	0x24, 0xb3, 0x17, 0x46, 0xe7, 0xf5, 0x55, 0x88, 0x74, 0xa1, 0xe1, 0x54, 0xbe, 0x8c, 0x87, 0xc1,
	0x89, 0xb7, 0x8c, 0x0e, 0x57, 0x27, 0x3a, 0x44, 0x61, 0x17, 0x79, 0xb4, 0xde, 0xbf, 0x58, 0x90,
	0x15, 0xc0, 0xa7, 0x8f, 0x2d, 0xca, 0xc3, 0x3b, 0x1e, 0xaf, 0xc9, 0x2f, 0xb0, 0xa8, 0x47, 0xda,
	0x88, 0x88, 0x29, 0xa1, 0x65, 0xaa, 0x7c, 0xc1, 0x82, 0x78, 0x28, 0xbd, 0x9b, 0xb8, 0xd1, 0x57,
	0x93, 0x23, 0x47, 0x01, 0xcd, 0xf8, 0x98, 0x06, 0xa2, 0xdf, 0xc1, 0xc8, 0x17, 0xd0, 0xcc, 0x63,
	0x8f, 0x22, 0x1e, 0x0f, 0xbc, 0x15, 0xdc, 0xbb, 0x91, 0x85, 0x86, 0x98, 0xbd, 0xab, 0x3e, 0x7f,
	0x2b, 0x42, 0x77, 0x57, 0xb7, 0xae, 0x75, 0x57, 0x28, 0xb0, 0x77, 0xd5, 0x79, 0x08, 0x8d, 0x4b,
	0x4d, 0xad, 0x0a, 0xe5, 0xe3, 0xa3, 0x5d, 0xda, 0xbe, 0x41, 0x9a, 0x50, 0xb3, 0x5f, 0x4f, 0x77,
	0x77, 0x8e, 0x9f, 0xb5, 0x4b, 0xa4, 0x02, 0xb6, 0x11, 0xb6, 0xa7, 0x3a, 0x4f, 0xa0, 0x8c, 0xcf,
	0xbe, 0x0e, 0x79, 0x19, 0xb7, 0x6f, 0x58, 0xeb, 0x36, 0xdd, 0x6f, 0x97, 0x48, 0x0d, 0x66, 0xb6,
	0xe9, 0xfe, 0xe6, 0xa3, 0xf6, 0x94, 0xc5, 0x5e, 0x3d, 0xde, 0x6c, 0x4f, 0x13, 0x80, 0xd9, 0x57,
	0x8f, 0x37, 0xd9, 0xe6, 0xa3, 0x76, 0xb9, 0x73, 0x02, 0xf5, 0x42, 0x96, 0xed, 0x9c, 0x48, 0xb5,
	0x60, 0x27, 0x32, 0xe2, 0x38, 0x4d, 0xaa, 0xb4, 0x92, 0x6a, 0xf1, 0x4c, 0x46, 0xdc, 0x96, 0x95,
	0x35, 0xa9, 0xbe, 0xc0, 0x09, 0x52, 0xa5, 0xb3, 0xa9, 0x16, 0xb4, 0x2f, 0xc8, 0x97, 0xd0, 0x1a,
	0x4a, 0x9b, 0xe6, 0xb1, 0x72, 0x1a, 0xed, 0x0d, 0x44, 0x8f, 0x9d, 0xbc, 0x23, 0x81, 0xbc, 0x9b,
	0x65, 0xb2, 0x01, 0x4b, 0xf8, 0xdc, 0x58, 0x72, 0x3a, 0xd2, 0x81, 0xcf, 0x43, 0x16, 0x89, 0x48,
	0xaa, 0x11, 0x6e, 0x5e, 0xa6, 0x0b, 0x68, 0x3c, 0xcc, 0x6c, 0xfb, 0x68, 0xb2, 0x43, 0x87, 0x9f,
	0xf1, 0x20, 0xe4, 0xfd, 0x50, 0xd8, 0x4e, 0xab, 0x31, 0x9e, 0x19, 0xda, 0x1c, 0xa3, 0xdd, 0x24,
	0xd5, 0x9d, 0x7f, 0x4b, 0x50, 0xcd, 0x33, 0x4c, 0x08, 0x94, 0x07, 0x42, 0xfb, 0xe8, 0xb6, 0x46,
	0xf1, 0xdb, 0x62, 0xf8, 0x80, 0xdc, 0x3c, 0xc4, 0x6f, 0x72, 0x07, 0x40, 0x1b, 0xae, 0x0c, 0x0e,
	0x55, 0x3c, 0x47, 0x99, 0xd6, 0x10, 0xb1, 0xb3, 0x94, 0xdc, 0x82, 0x9a, 0x12, 0x3c, 0x74, 0xd6,
	0x32, 0x5a, 0xab, 0x16, 0x40, 0xe3, 0xe7, 0x00, 0x2e, 0x78, 0x9b, 0x08, 0x9c, 0x6d, 0xe5, 0x9d,
	0x29, 0xaf, 0x44, 0x6b, 0x0e, 0x3d, 0xd6, 0x82, 0xfc, 0x0e, 0xcb, 0x89, 0x92, 0xbe, 0xd0, 0x5a,
	0xe8, 0x2b, 0xcf, 0x73, 0x16, 0x1f, 0xca, 0xda, 0xe4, 0x87, 0xe2, 0x34, 0x97, 0xde, 0xe7, 0xd2,
	0xd8, 0x51, 0x11, 0xee, 0xfc, 0x39, 0x0d, 0x0b, 0x13, 0xe8, 0xe3, 0xc3, 0x96, 0x0a, 0x87, 0x5d,
	0x83, 0x76, 0xaa, 0x85, 0xc2, 0xd3, 0xb0, 0x28, 0xb0, 0xed, 0x15, 0x93, 0x51, 0xa6, 0x2d, 0x8b,
	0xdb, 0x43, 0xed, 0x23, 0x6a, 0x27, 0x5b, 0x56, 0x53, 0x45, 0xae, 0x4b, 0x4f, 0xdb, 0x59, 0x0a,
	0xec, 0xdb, 0x00, 0x11, 0x3f, 0x67, 0x4a, 0x6b, 0xf6, 0xa6, 0x9f, 0xa7, 0x29, 0xe2, 0xe7, 0x54,
	0xeb, 0xbd, 0x3e, 0xf9, 0x1a, 0xe6, 0xa3, 0x20, 0x96, 0x8a, 0x25, 0xfc, 0x44, 0xb0, 0x21, 0x4f,
	0x43, 0xa3, 0x5d, 0xb6, 0xe8, 0x1c, 0x1a, 0x0e, 0xf9, 0x89, 0xf8, 0x01, 0x61, 0xe4, 0xf2, 0xd7,
	0x57, 0xb8, 0xb3, 0x19, 0xd7, 0x1a, 0x0a, 0xdc, 0x4f, 0xa1, 0x1e, 0x48, 0x16, 0xc4, 0x49, 0x6a,
	0xec, 0xb6, 0x15, 0x77, 0x77, 0x81, 0x7c, 0x6e, 0x91, 0xbd, 0x3e, 0x59, 0x85, 0x46, 0x20, 0x99,
	0x4c, 0x4d, 0x46, 0xa8, 0x22, 0x01, 0x02, 0xd9, 0x43, 0x68, 0xaf, 0x4f, 0x9e, 0xc0, 0xca, 0x99,
	0x0c, 0xd3, 0xd8, 0x70, 0x35, 0xb2, 0xed, 0xc9, 0x88, 0x73, 0xc3, 0xf4, 0x1f, 0x81, 0xf1, 0x4f,
	0x85, 0xc6, 0x11, 0x5d, 0xa6, 0xde, 0x98, 0xd1, 0x75, 0x84, 0xa3, 0xcc, 0x4e, 0xbe, 0x87, 0xdb,
	0x41, 0xfc, 0x1e, 0x3d, 0xa0, 0x7e, 0xa5, 0xc0, 0xb9, 0xe2, 0xa1, 0xf3, 0x4f, 0x09, 0x5a, 0xfb,
	0x72, 0x90, 0x86, 0xe2, 0xc5, 0x28, 0x71, 0xd7, 0xf6, 0x5b, 0xde, 0x2d, 0x5d, 0x92, 0xf1, 0xfa,
	0x5a, 0x1b, 0x0f, 0x26, 0x8f, 0xf5, 0x4b, 0x52, 0xd7, 0x3c, 0x5d, 0xc9, 0x15, 0x06, 0x7c, 0xff,
	0x02, 0x25, 0x9f, 0x41, 0x3d, 0x42, 0x0d, 0x33, 0xa3, 0x24, 0xaf, 0x03, 0x88, 0xc6, 0x6e, 0x6c,
	0x65, 0xc7, 0x69, 0xc4, 0xe4, 0x90, 0x39, 0xd0, 0x5d, 0x79, 0x93, 0x36, 0xe2, 0x34, 0xea, 0x0d,
	0xdd, 0x7e, 0xba, 0xf3, 0x20, 0x6b, 0x21, 0x99, 0xd7, 0x4b, 0x7d, 0xa8, 0x06, 0x33, 0x47, 0xbd,
	0xde, 0x81, 0x6d, 0x58, 0x55, 0x28, 0xef, 0x6f, 0xef, 0xed, 0xb6, 0xa7, 0x3a, 0x21, 0xac, 0x74,
	0x55, 0x60, 0x6c, 0x49, 0x1f, 0x6b, 0xa1, 0x7e, 0x92, 0xa9, 0x8a, 0xc5, 0x28, 0x1f, 0x10, 0x93,
	0x5e, 0xea, 0x16, 0x54, 0xf2, 0x01, 0x34, 0xf5, 0x9e, 0x79, 0x51, 0xf8, 0x61, 0x43, 0x73, 0x41,
	0xa7, 0x0f, 0xb7, 0x26, 0xec, 0xa6, 0x2f, 0xe6, 0x51, 0xd9, 0x4f, 0x5f, 0x6b, 0xaf, 0x84, 0xf5,
	0x37, 0x39, 0xb3, 0xff, 0x1f, 0x2d, 0x45, 0x71, 0xe7, 0xaf, 0x12, 0xcc, 0xbf, 0x33, 0xfd, 0x88,
	0x07, 0x95, 0x3c, 0x6f, 0x25, 0xcc, 0x5b, 0xbe, 0xb4, 0xf3, 0x2b, 0xfb, 0x79, 0xe8, 0x0e, 0xd4,
	0xa4, 0xe3, 0xb5, 0x7d, 0xf3, 0xae, 0x25, 0xf2, 0x30, 0x94, 0x3e, 0xf3, 0x65, 0x1a, 0x9b, 0xac,
	0xd4, 0xe6, 0xd0, 0xb0, 0x6d, 0xf1, 0xae, 0x85, 0x6d, 0x05, 0x17, 0xb9, 0x3a, 0x78, 0x9b, 0xb7,
	0xa5, 0xd6, 0x05, 0xf5, 0x28, 0x78, 0x2b, 0xec, 0xef, 0x31, 0x5b, 0x93, 0xa7, 0x82, 0x27, 0x8e,
	0xe6, 0x2a, 0xae, 0x1e, 0xf1, 0xf3, 0x1f, 0x05, 0x4f, 0x2c, 0x67, 0x67, 0xe9, 0xd7, 0x6c, 0xe4,
	0x67, 0xe7, 0x66, 0xf8, 0x97, 0xe4, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xcb, 0xfb, 0x8e, 0xf5,
	0xa2, 0x0c, 0x00, 0x00,
}
