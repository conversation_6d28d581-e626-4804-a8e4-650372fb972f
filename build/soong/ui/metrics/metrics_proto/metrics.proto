// Copyright 2018 Google Inc. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package soong_build_metrics;
option go_package = "soong_metrics_proto";

message MetricsBase {
  // Timestamp generated when the build starts.
  optional int64 build_date_timestamp = 1;

  // It is usually used to specify the branch name [and release candidate].
  optional string build_id  = 2;

  // The platform version codename, eg. P, Q, REL.
  optional string platform_version_codename = 3;

  // The target product information, eg. aosp_arm.
  optional string target_product = 4;

  enum BuildVariant {
    USER = 0;
    USERDEBUG = 1;
    ENG = 2;
  }
  // The target build variant information, eg. eng.
  optional BuildVariant target_build_variant = 5 [default = ENG];

  enum Arch {
    UNKNOWN = 0;
    ARM = 1;
    ARM64 = 2;
    X86 = 3;
    X86_64 = 4;
  }
  // The target arch information, eg. arm.
  optional Arch target_arch = 6 [default = UNKNOWN];

  // The target arch variant information, eg. armv7-a-neon.
  optional string target_arch_variant = 7;

  // The target cpu variant information, eg. generic.
  optional string target_cpu_variant = 8;

  // The host arch information, eg. x86_64.
  optional Arch host_arch = 9 [default = UNKNOWN];

  // The host 2nd arch information, eg. x86.
  optional Arch host_2nd_arch = 10 [default = UNKNOWN];

  // The host os information, eg. linux.
  optional string host_os = 11;

  // The host os extra information, eg. Linux-4.17.0-3rodete2-amd64-x86_64-Debian-GNU.
  optional string host_os_extra = 12;

  // The host cross os information, eg. windows.
  optional string host_cross_os = 13;

  // The host cross arch information, eg. x86.
  optional string host_cross_arch = 14;

  // The host cross 2nd arch information, eg. x86_64.
  optional string host_cross_2nd_arch = 15;

  // The directory for generated built artifacts installation, eg. out.
  optional string out_dir = 16;

  // The metrics for calling various tools (microfactory) before Soong_UI starts.
  repeated PerfInfo setup_tools = 17;

  // The metrics for calling Kati by multiple times.
  repeated PerfInfo kati_runs = 18;

  // The metrics for calling Soong.
  repeated PerfInfo soong_runs = 19;

  // The metrics for calling Ninja.
  repeated PerfInfo ninja_runs = 20;

  // The metrics for the whole build
  optional PerfInfo total = 21;

  optional SoongBuildMetrics soong_build_metrics = 22;

  optional BuildConfig build_config = 23;

  // The hostname of the machine.
  optional string hostname = 24;

  // The system resource information such as total physical memory.
  optional SystemResourceInfo system_resource_info = 25;

  // The build command that the user entered to the build system.
  optional string build_command = 26;

  // The metrics for calling Bazel.
  repeated PerfInfo bazel_runs = 27;
}

message BuildConfig {
  optional bool use_goma = 1;

  optional bool use_rbe = 2;

  optional bool force_use_goma = 3;
}

message SystemResourceInfo {
  // The total physical memory in bytes.
  optional uint64 total_physical_memory = 1;

  // The total of available cores for building
  optional int32 available_cpus = 2;
}

message PerfInfo {
  // The description for the phase/action/part while the tool running.
  optional string desc = 1;

  // The name for the running phase/action/part.
  optional string name = 2;

  // The absolute start time.
  // The number of nanoseconds elapsed since January 1, 1970 UTC.
  optional uint64 start_time = 3;

  // The real running time.
  // The number of nanoseconds elapsed since start_time.
  optional uint64 real_time = 4;

  // The number of MB for memory use (deprecated as it is too generic).
  optional uint64 memory_use = 5 [deprecated=true];

  // The resource information of each executed process.
  repeated ProcessResourceInfo processes_resource_info = 6;
}

message ProcessResourceInfo {
  // The name of the process for identification.
  optional string name = 1;

  // The amount of time spent executing in user space in microseconds.
  optional uint64 user_time_micros = 2;

  // The amount of time spent executing in kernel mode in microseconds.
  optional uint64 system_time_micros = 3;

  // The maximum resident set size memory used in kilobytes.
  optional uint64 max_rss_kb = 4;

  // The number of minor page faults serviced without any I/O activity.
  optional uint64 minor_page_faults = 5;

  // The number of major page faults serviced that required I/O activity.
  optional uint64 major_page_faults = 6;

  // Total IO input in kilobytes.
  optional uint64 io_input_kb= 7;

  // Total IO output in kilobytes.
  optional uint64 io_output_kb = 8;

  // The number of voluntary context switches
  optional uint64 voluntary_context_switches = 9;

  // The number of involuntary context switches
  optional uint64 involuntary_context_switches = 10;
}

message ModuleTypeInfo {
  enum BuildSystem {
    UNKNOWN = 0;
    SOONG = 1;
    MAKE = 2;
  }
  // The build system, eg. Soong or Make.
  optional BuildSystem build_system = 1 [default = UNKNOWN];

  // The module type, eg. java_library, cc_binary, and etc.
  optional string module_type = 2;

  // The number of logical modules.
  optional uint32 num_of_modules = 3;
}

message CriticalUserJourneyMetrics {
  // The name of a critical user journey test.
  optional string name = 1;

  // The metrics produced when running the critical user journey test.
  optional MetricsBase metrics = 2;
}

message CriticalUserJourneysMetrics {
  // A set of metrics from a run of the critical user journey tests.
  repeated CriticalUserJourneyMetrics cujs = 1;
}

message SoongBuildMetrics {
  // The number of modules handled by soong_build.
  optional uint32 modules = 1;

  // The total number of variants handled by soong_build.
  optional uint32 variants = 2;

  // The total number of allocations in soong_build.
  optional uint64 total_alloc_count = 3;

  // The total size of allocations in soong_build in bytes.
  optional uint64 total_alloc_size = 4;

  // The approximate maximum size of the heap in soong_build in bytes.
  optional uint64 max_heap_size = 5;
}
