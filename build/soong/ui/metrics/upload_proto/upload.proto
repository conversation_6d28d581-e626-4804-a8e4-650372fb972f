// Copyright 2020 Google Inc. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package soong_metrics_upload;
option go_package = "soong_metrics_upload_proto";

message Upload {
  // The timestamp in milliseconds that the build was created.
  optional uint64 creation_timestamp_ms = 1;

  // The timestamp in milliseconds when the build was completed.
  optional uint64 completion_timestamp_ms = 2;

  // The branch name.
  optional string branch_name = 3;

  // The target name.
  optional string target_name = 4;

  // A list of metrics filepaths to upload.
  repeated string metrics_files = 5;

  // A list of directories to delete after the copy of metrics files
  // is completed for uploading.
  repeated string directories_to_delete = 6;
}
