// Copyright 2017 Google Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

bootstrap_go_package {
    name: "soong-ui-build-paths",
    pkgPath: "android/soong/ui/build/paths",
    srcs: [
        "paths/config.go",
        "paths/logs.go",
    ],
    testSrcs: [
        "paths/logs_test.go",
    ],
}

bootstrap_go_package {
    name: "soong-ui-build",
    pkgPath: "android/soong/ui/build",
    deps: [
        "blueprint",
        "blueprint-bootstrap",
        "soong-ui-build-paths",
        "soong-ui-logger",
        "soong-ui-metrics",
        "soong-ui-status",
        "soong-ui-terminal",
        "soong-ui-tracer",
        "soong-shared",
        "soong-finder",
        "blueprint-microfactory",
    ],
    srcs: [
        "bazel.go",
        "build.go",
        "cleanbuild.go",
        "config.go",
        "context.go",
        "dumpvars.go",
        "environment.go",
        "exec.go",
        "finder.go",
        "goma.go",
        "kati.go",
        "ninja.go",
        "path.go",
        "proc_sync.go",
        "rbe.go",
        "signal.go",
        "soong.go",
        "test_build.go",
        "upload.go",
        "util.go",
    ],
    testSrcs: [
        "cleanbuild_test.go",
        "config_test.go",
        "environment_test.go",
        "rbe_test.go",
        "upload_test.go",
        "util_test.go",
        "proc_sync_test.go",
    ],
    darwin: {
        srcs: [
            "config_darwin.go",
            "sandbox_darwin.go",
        ],
    },
    linux: {
        srcs: [
            "config_linux.go",
            "sandbox_linux.go",
        ],
    },
}
