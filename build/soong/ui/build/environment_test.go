// Copyright 2017 Google Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package build

import (
	"reflect"
	"strings"
	"testing"
)

func TestEnvUnset(t *testing.T) {
	initial := &Environment{"TEST=1", "TEST2=0"}
	initial.Unset("TEST")
	got := initial.Environ()
	if len(got) != 1 || got[0] != "TEST2=0" {
		t.Errorf("Expected [TEST2=0], got: %v", got)
	}
}

func TestEnvUnsetMissing(t *testing.T) {
	initial := &Environment{"TEST2=0"}
	initial.Unset("TEST")
	got := initial.Environ()
	if len(got) != 1 || got[0] != "TEST2=0" {
		t.Errorf("Expected [TEST2=0], got: %v", got)
	}
}

func TestEnvSet(t *testing.T) {
	initial := &Environment{}
	initial.Set("TEST", "0")
	got := initial.Environ()
	if len(got) != 1 || got[0] != "TEST=0" {
		t.Errorf("Expected [TEST=0], got: %v", got)
	}
}

func TestEnvSetDup(t *testing.T) {
	initial := &Environment{"TEST=1"}
	initial.Set("TEST", "0")
	got := initial.Environ()
	if len(got) != 1 || got[0] != "TEST=0" {
		t.Errorf("Expected [TEST=0], got: %v", got)
	}
}

func TestEnvAllow(t *testing.T) {
	initial := &Environment{"TEST=1", "TEST2=0", "TEST3=2"}
	initial.Allow("TEST3", "TEST")
	got := initial.Environ()
	if len(got) != 2 || got[0] != "TEST=1" || got[1] != "TEST3=2" {
		t.Errorf("Expected [TEST=1 TEST3=2], got: %v", got)
	}
}

const testKatiEnvFileContents = `#!/bin/sh
# Generated by kati unknown

unset 'CLANG'
export 'BUILD_ID'='NYC'
`

func TestEnvAppendFromKati(t *testing.T) {
	initial := &Environment{"CLANG=/usr/bin/clang", "TEST=0"}
	err := initial.appendFromKati(strings.NewReader(testKatiEnvFileContents))
	if err != nil {
		t.Fatalf("Unexpected error from %v", err)
	}

	got := initial.Environ()
	expected := []string{"TEST=0", "BUILD_ID=NYC"}
	if !reflect.DeepEqual(got, expected) {
		t.Errorf("Environment list does not match")
		t.Errorf("expected: %v", expected)
		t.Errorf("     got: %v", got)
	}
}
