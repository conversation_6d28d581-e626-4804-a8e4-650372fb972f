// Copyright 2018 Google Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

bootstrap_go_package {
    name: "soong-ui-status",
    pkgPath: "android/soong/ui/status",
    deps: [
        "golang-protobuf-proto",
        "soong-ui-logger",
        "soong-ui-status-ninja_frontend",
        "soong-ui-status-build_error_proto",
        "soong-ui-status-build_progress_proto",
    ],
    srcs: [
        "critical_path.go",
        "kati.go",
        "log.go",
        "ninja.go",
        "status.go",
    ],
    testSrcs: [
        "critical_path_test.go",
        "kati_test.go",
        "ninja_test.go",
        "status_test.go",
    ],
}

bootstrap_go_package {
    name: "soong-ui-status-ninja_frontend",
    pkgPath: "android/soong/ui/status/ninja_frontend",
    deps: ["golang-protobuf-proto"],
    srcs: [
        "ninja_frontend/frontend.pb.go",
    ],
}

bootstrap_go_package {
    name: "soong-ui-status-build_error_proto",
    pkgPath: "android/soong/ui/status/build_error_proto",
    deps: ["golang-protobuf-proto"],
    srcs: [
        "build_error_proto/build_error.pb.go",
    ],
}

bootstrap_go_package {
    name: "soong-ui-status-build_progress_proto",
    pkgPath: "android/soong/ui/status/build_progress_proto",
    deps: ["golang-protobuf-proto"],
    srcs: [
        "build_progress_proto/build_progress.pb.go",
    ],
}
