// Copyright 2017 Google Inc. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

option optimize_for = LITE_RUNTIME;

package ninja;
option go_package = "ninja_frontend";

message Status {
  message TotalEdges {
    // New value for total edges in the build.
    optional uint32 total_edges = 1;
  }

  message BuildStarted {
    // Number of jobs Ninja will run in parallel.
    optional uint32 parallelism = 1;
    // Verbose value passed to ninja.
    optional bool verbose = 2;
  }

  message BuildFinished {
  }

  message EdgeStarted {
    // Edge identification number, unique to a Ninja run.
    optional uint32 id = 1;
    // Edge start time in milliseconds since Ninja started.
    optional uint32 start_time = 2;
    // List of edge inputs.
    repeated string inputs = 3;
    // List of edge outputs.
    repeated string outputs = 4;
    // Description field from the edge.
    optional string desc = 5;
    // Command field from the edge.
    optional string command = 6;
    // Edge uses console.
    optional bool console = 7;
  }

  message EdgeFinished {
    // Edge identification number, unique to a Ninja run.
    optional uint32 id = 1;
    // Edge end time in milliseconds since Ninja started.
    optional uint32 end_time = 2;
    // Exit status (0 for success).
    optional sint32 status = 3;
    // Edge output, may contain ANSI codes.
    optional string output = 4;
    // Number of milliseconds spent executing in user mode
    optional uint32 user_time = 5;
    // Number of milliseconds spent executing in kernel mode
    optional uint32 system_time = 6;
    // Max resident set size in kB
    optional uint64 max_rss_kb = 7;
    // Minor page faults
    optional uint64 minor_page_faults = 8;
    // Major page faults
    optional uint64 major_page_faults = 9;
    // IO input in kB
    optional uint64 io_input_kb = 10;
    // IO output in kB
    optional uint64 io_output_kb = 11;
    // Voluntary context switches
    optional uint64 voluntary_context_switches = 12;
    // Involuntary context switches
    optional uint64 involuntary_context_switches = 13;
  }

  message Message {
    enum Level {
      INFO = 0;
      WARNING = 1;
      ERROR = 2;
      DEBUG = 3;
    }
    // Message priority level (DEBUG, INFO, WARNING, ERROR).
    optional Level level = 1 [default = INFO];
    // Info/warning/error message from Ninja.
    optional string message = 2;
  }

  optional TotalEdges total_edges = 1;
  optional BuildStarted build_started = 2;
  optional BuildFinished build_finished = 3;
  optional EdgeStarted edge_started = 4;
  optional EdgeFinished edge_finished = 5;
  optional Message message = 6;
}
