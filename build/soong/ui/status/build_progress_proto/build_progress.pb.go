// Code generated by protoc-gen-go. DO NOT EDIT.
// source: build_progress.proto

package soong_build_progress_proto

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type BuildProgress struct {
	// Total number of actions in a build. The total actions will increase
	// and might decrease during the course of a build.
	TotalActions *uint64 `protobuf:"varint,1,opt,name=total_actions,json=totalActions" json:"total_actions,omitempty"`
	// Total number of completed build actions. This value will never decrease
	// and finished_actions <= total_actions. At one point of the build, the
	// finished_actions will be equal to total_actions. This may not represent
	// that the build is completed as the total_actions may be increased for
	// additional counted work or is doing non-counted work.
	FinishedActions *uint64 `protobuf:"varint,2,opt,name=finished_actions,json=finishedActions" json:"finished_actions,omitempty"`
	// Total number of current actions being executed during a course of a
	// build and current_actions + finished_actions <= total_actions.
	CurrentActions *uint64 `protobuf:"varint,3,opt,name=current_actions,json=currentActions" json:"current_actions,omitempty"`
	// Total number of actions that reported as a failure.
	FailedActions        *uint64  `protobuf:"varint,4,opt,name=failed_actions,json=failedActions" json:"failed_actions,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuildProgress) Reset()         { *m = BuildProgress{} }
func (m *BuildProgress) String() string { return proto.CompactTextString(m) }
func (*BuildProgress) ProtoMessage()    {}
func (*BuildProgress) Descriptor() ([]byte, []int) {
	return fileDescriptor_a8a463f8e30dab2e, []int{0}
}

func (m *BuildProgress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuildProgress.Unmarshal(m, b)
}
func (m *BuildProgress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuildProgress.Marshal(b, m, deterministic)
}
func (m *BuildProgress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuildProgress.Merge(m, src)
}
func (m *BuildProgress) XXX_Size() int {
	return xxx_messageInfo_BuildProgress.Size(m)
}
func (m *BuildProgress) XXX_DiscardUnknown() {
	xxx_messageInfo_BuildProgress.DiscardUnknown(m)
}

var xxx_messageInfo_BuildProgress proto.InternalMessageInfo

func (m *BuildProgress) GetTotalActions() uint64 {
	if m != nil && m.TotalActions != nil {
		return *m.TotalActions
	}
	return 0
}

func (m *BuildProgress) GetFinishedActions() uint64 {
	if m != nil && m.FinishedActions != nil {
		return *m.FinishedActions
	}
	return 0
}

func (m *BuildProgress) GetCurrentActions() uint64 {
	if m != nil && m.CurrentActions != nil {
		return *m.CurrentActions
	}
	return 0
}

func (m *BuildProgress) GetFailedActions() uint64 {
	if m != nil && m.FailedActions != nil {
		return *m.FailedActions
	}
	return 0
}

func init() {
	proto.RegisterType((*BuildProgress)(nil), "soong_build_progress.BuildProgress")
}

func init() { proto.RegisterFile("build_progress.proto", fileDescriptor_a8a463f8e30dab2e) }

var fileDescriptor_a8a463f8e30dab2e = []byte{
	// 165 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x49, 0x2a, 0xcd, 0xcc,
	0x49, 0x89, 0x2f, 0x28, 0xca, 0x4f, 0x2f, 0x4a, 0x2d, 0x2e, 0xd6, 0x2b, 0x28, 0xca, 0x2f, 0xc9,
	0x17, 0x12, 0x29, 0xce, 0xcf, 0xcf, 0x4b, 0x8f, 0x47, 0x95, 0x53, 0x5a, 0xcf, 0xc8, 0xc5, 0xeb,
	0x04, 0x12, 0x0a, 0x80, 0x8a, 0x08, 0x29, 0x73, 0xf1, 0x96, 0xe4, 0x97, 0x24, 0xe6, 0xc4, 0x27,
	0x26, 0x97, 0x64, 0xe6, 0xe7, 0x15, 0x4b, 0x30, 0x2a, 0x30, 0x6a, 0xb0, 0x04, 0xf1, 0x80, 0x05,
	0x1d, 0x21, 0x62, 0x42, 0x9a, 0x5c, 0x02, 0x69, 0x99, 0x79, 0x99, 0xc5, 0x19, 0xa9, 0x29, 0x70,
	0x75, 0x4c, 0x60, 0x75, 0xfc, 0x30, 0x71, 0x98, 0x52, 0x75, 0x2e, 0xfe, 0xe4, 0xd2, 0xa2, 0xa2,
	0xd4, 0xbc, 0x12, 0xb8, 0x4a, 0x66, 0xb0, 0x4a, 0x3e, 0xa8, 0x30, 0x4c, 0xa1, 0x2a, 0x17, 0x5f,
	0x5a, 0x62, 0x66, 0x0e, 0x92, 0x89, 0x2c, 0x60, 0x75, 0xbc, 0x10, 0x51, 0xa8, 0x32, 0x27, 0x99,
	0x28, 0x29, 0x6c, 0x3e, 0x89, 0x07, 0xfb, 0x12, 0x10, 0x00, 0x00, 0xff, 0xff, 0x3f, 0x6e, 0xc1,
	0xef, 0xfc, 0x00, 0x00, 0x00,
}
