// Copyright 2020 Google Inc. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package soong_build_progress;
option go_package = "soong_build_progress_proto";

message BuildProgress {
  // Total number of actions in a build. The total actions will increase
  // and might decrease during the course of a build.
  optional uint64 total_actions = 1;

  // Total number of completed build actions. This value will never decrease
  // and finished_actions <= total_actions. At one point of the build, the
  // finished_actions will be equal to total_actions. This may not represent
  // that the build is completed as the total_actions may be increased for
  // additional counted work or is doing non-counted work.
  optional uint64 finished_actions = 2;

  // Total number of current actions being executed during a course of a
  // build and current_actions + finished_actions <= total_actions.
  optional uint64 current_actions = 3;

  // Total number of actions that reported as a failure.
  optional uint64 failed_actions = 4;
}
