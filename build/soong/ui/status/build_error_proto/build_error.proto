// Copyright 2019 Google Inc. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package soong_build_error;
option go_package = "soong_build_error_proto";

message BuildError {
  // List of error messages of the overall build. The error messages
  // are not associated with a build action.
  repeated string error_messages = 1;

  // List of build action errors.
  repeated BuildActionError action_errors = 2;
}

// Build is composed of a list of build action. There can be a set of build
// actions that can failed.
message BuildActionError {
  // Description of the command.
  optional string description = 1;

  // The command name that raised the error.
  optional string command = 2;

  // The command output stream.
  optional string output = 3;

  // List of artifacts (i.e. files) that was produced by the command.
  repeated string artifacts = 4;

  // The error string produced by the build action.
  optional string error = 5;
}
