/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// This format file defines configuration file for linkerconfig. Details on this
// format can be found from
// https://android.googlesource.com/platform/system/linkerconfig/+/master/README.md

syntax = "proto3";

package android.linkerconfig.proto;

message LinkerConfig {
  // Extra permitted paths
  repeated string permittedPaths = 1;

  // Force APEX namespace visible
  bool visible = 2;

  // Providing libs from the module
  repeated string provideLibs = 3;

  // Required libs from the module
  repeated string requireLibs = 4;
}
