//
// Copyright (C) 2020 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

python_library_host {
    name: "symbolfile",
    pkg_path: "symbolfile",
    srcs: [
        "__init__.py",
    ],
}

python_test_host {
    name: "test_symbolfile",
    srcs: [
        "test_symbolfile.py",
    ],
    libs: [
        "symbolfile",
    ],
}
