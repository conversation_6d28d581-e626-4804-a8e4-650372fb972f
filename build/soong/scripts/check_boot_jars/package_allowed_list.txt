# Boot jar package name allowed list.
# Each line is interpreted as a regular expression.

###################################################
# core-libart.jar & core-oj.jar
java\.awt\.font
java\.beans
java\.io
java\.lang
java\.lang\.annotation
java\.lang\.invoke
java\.lang\.ref
java\.lang\.reflect
java\.math
java\.net
java\.nio
java\.nio\.file
java\.nio\.file\.spi
java\.nio\.file\.attribute
java\.nio\.channels
java\.nio\.channels\.spi
java\.nio\.charset
java\.nio\.charset\.spi
java\.security
java\.security\.acl
java\.security\.cert
java\.security\.interfaces
java\.security\.spec
java\.sql
java\.text
java\.text\.spi
java\.time
java\.time\.chrono
java\.time\.format
java\.time\.temporal
java\.time\.zone
java\.util
java\.util\.concurrent
java\.util\.concurrent\.atomic
java\.util\.concurrent\.locks
java\.util\.function
java\.util\.jar
java\.util\.logging
java\.util\.prefs
java\.util\.regex
java\.util\.spi
java\.util\.stream
java\.util\.zip
# TODO: Remove javax.annotation.processing if possible, see http://b/132338110:
javax\.annotation\.processing
javax\.crypto
javax\.crypto\.interfaces
javax\.crypto\.spec
javax\.net
javax\.net\.ssl
javax\.security\.auth
javax\.security\.auth\.callback
javax\.security\.auth\.login
javax\.security\.auth\.x500
javax\.security\.cert
javax\.sql
javax\.xml
javax\.xml\.datatype
javax\.xml\.namespace
javax\.xml\.parsers
javax\.xml\.transform
javax\.xml\.transform\.dom
javax\.xml\.transform\.sax
javax\.xml\.transform\.stream
javax\.xml\.validation
javax\.xml\.xpath
jdk\.internal\.util
jdk\.internal\.vm\.annotation
jdk\.net
org\.w3c\.dom
org\.w3c\.dom\.ls
org\.w3c\.dom\.traversal
# OpenJdk internal implementation.
sun\.invoke\.util
sun\.invoke\.empty
sun\.misc
sun\.util.*
sun\.text.*
sun\.security.*
sun\.reflect.*
sun\.nio.*
sun\.net.*
com\.sun\..*

# TODO: Move these internal org.apache.harmony classes to libcore.*
org\.apache\.harmony\.crypto\.internal
org\.apache\.harmony\.dalvik
org\.apache\.harmony\.dalvik\.ddmc
org\.apache\.harmony\.luni\.internal\.util
org\.apache\.harmony\.security
org\.apache\.harmony\.security\.asn1
org\.apache\.harmony\.security\.fortress
org\.apache\.harmony\.security\.pkcs10
org\.apache\.harmony\.security\.pkcs7
org\.apache\.harmony\.security\.pkcs8
org\.apache\.harmony\.security\.provider\.crypto
org\.apache\.harmony\.security\.utils
org\.apache\.harmony\.security\.x501
org\.apache\.harmony\.security\.x509
org\.apache\.harmony\.security\.x509\.tsp
org\.apache\.harmony\.xml
org\.apache\.harmony\.xml\.dom
org\.apache\.harmony\.xml\.parsers

org\.json
org\.xmlpull\.v1
org\.xmlpull\.v1\.sax2

# TODO:  jarjar org.kxml2.io to com.android org\.kxml2\.io
org\.kxml2\.io
org\.xml
org\.xml\.sax
org\.xml\.sax\.ext
org\.xml\.sax\.helpers

dalvik\..*
libcore\..*
android\..*
com\.android\..*
###################################################
# android.test.base.jar
junit\.extensions
junit\.framework
android\.test
android\.test\.suitebuilder\.annotation


###################################################
# ext.jar
# TODO: jarjar javax.sip to com.android
javax\.sip
javax\.sip\.address
javax\.sip\.header
javax\.sip\.message

# TODO: jarjar org.apache.commons to com.android
org\.apache\.commons\.codec
org\.apache\.commons\.codec\.binary
org\.apache\.commons\.codec\.language
org\.apache\.commons\.codec\.net
org\.apache\.commons\.logging
org\.apache\.commons\.logging\.impl
org\.apache\.http
org\.apache\.http\.auth
org\.apache\.http\.auth\.params
org\.apache\.http\.client
org\.apache\.http\.client\.entity
org\.apache\.http\.client\.methods
org\.apache\.http\.client\.params
org\.apache\.http\.client\.protocol
org\.apache\.http\.client\.utils
org\.apache\.http\.conn
org\.apache\.http\.conn\.params
org\.apache\.http\.conn\.routing
org\.apache\.http\.conn\.scheme
org\.apache\.http\.conn\.ssl
org\.apache\.http\.conn\.util
org\.apache\.http\.cookie
org\.apache\.http\.cookie\.params
org\.apache\.http\.entity
org\.apache\.http\.impl
org\.apache\.http\.impl\.auth
org\.apache\.http\.impl\.client
org\.apache\.http\.impl\.client
org\.apache\.http\.impl\.conn
org\.apache\.http\.impl\.conn\.tsccm
org\.apache\.http\.impl\.cookie
org\.apache\.http\.impl\.entity
org\.apache\.http\.impl\.io
org\.apache\.http\.impl\.io
org\.apache\.http\.io
org\.apache\.http\.message
org\.apache\.http\.params
org\.apache\.http\.protocol
org\.apache\.http\.util

# TODO: jarjar gov.nist to com.android
gov\.nist\.core
gov\.nist\.core\.net
gov\.nist\.javax\.sip
gov\.nist\.javax\.sip\.address
gov\.nist\.javax\.sip\.clientauthutils
gov\.nist\.javax\.sip\.header
gov\.nist\.javax\.sip\.header\.extensions
gov\.nist\.javax\.sip\.header\.ims
gov\.nist\.javax\.sip\.message
gov\.nist\.javax\.sip\.parser
gov\.nist\.javax\.sip\.parser\.extensions
gov\.nist\.javax\.sip\.parser\.ims
gov\.nist\.javax\.sip\.stack

org\.ccil\.cowan\.tagsoup
org\.ccil\.cowan\.tagsoup\.jaxp

###################################################
# framework.jar
javax\.microedition\.khronos\.opengles
javax\.microedition\.khronos\.egl

android

###################################################
# apache-xml.jar
org\.apache\.xml\.res
org\.apache\.xml\.utils
org\.apache\.xml\.utils\.res
org\.apache\.xml\.dtm
org\.apache\.xml\.dtm\.ref
org\.apache\.xml\.dtm\.ref\.dom2dtm
org\.apache\.xml\.dtm\.ref\.sax2dtm
org\.apache\.xml\.serializer
org\.apache\.xml\.serializer\.utils
org\.apache\.xml\.serializer\.dom3
org\.apache\.xpath
org\.apache\.xpath\.operations
org\.apache\.xpath\.domapi
org\.apache\.xpath\.functions
org\.apache\.xpath\.res
org\.apache\.xpath\.axes
org\.apache\.xpath\.objects
org\.apache\.xpath\.patterns
org\.apache\.xpath\.jaxp
org\.apache\.xpath\.compiler
org\.apache\.xalan
org\.apache\.xalan\.res
org\.apache\.xalan\.templates
org\.apache\.xalan\.serialize
org\.apache\.xalan\.extensions
org\.apache\.xalan\.processor
org\.apache\.xalan\.transformer
org\.apache\.xalan\.xslt

###################################################
# Packages in the google namespace across all bootclasspath jars.
com\.google\.android\..*
com\.google\.vr\.platform.*
com\.google\.i18n\.phonenumbers\..*
com\.google\.i18n\.phonenumbers

###################################################
# Packages used for Android in Chrome OS
org\.chromium\.arc
org\.chromium\.arc\..*
