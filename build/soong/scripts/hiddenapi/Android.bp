/*
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

python_binary_host {
    name: "merge_csv",
    main: "merge_csv.py",
    srcs: ["merge_csv.py"],
    version: {
        py2: {
            enabled: false,
        },
        py3: {
            enabled: true,
            embedded_launcher: true,
        },
    },
}

python_binary_host {
    name: "generate_hiddenapi_lists",
    main: "generate_hiddenapi_lists.py",
    srcs: ["generate_hiddenapi_lists.py"],
    version: {
        py2: {
            enabled: false,
        },
        py3: {
            enabled: true,
            embedded_launcher: true,
        },
    },
}

python_binary_host {
    name: "verify_overlaps",
    main: "verify_overlaps.py",
    srcs: ["verify_overlaps.py"],
    version: {
        py2: {
            enabled: false,
        },
        py3: {
            enabled: true,
            embedded_launcher: true,
        },
    },
}
