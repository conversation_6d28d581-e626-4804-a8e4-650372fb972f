// Copyright 2019 Google Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

python_test_host {
    name: "par_test",
    main: "par_test.py",
    srcs: [
        "par_test.py",
        "testpkg/par_test.py",
    ],
    // Is not implemented as a python unittest
    test_options: {
        unit_test: false,
    },
    version: {
        py2: {
            enabled: true,
            embedded_launcher: true,
        },
        py3: {
            enabled: false,
            embedded_launcher: true,
        },
    },
}

python_test_host {
    name: "par_test3",
    main: "par_test.py",
    srcs: [
        "par_test.py",
        "testpkg/par_test.py",
    ],
    // Is not implemented as a python unittest
    test_options: {
        unit_test: false,
    },
    version: {
        py3: {
            embedded_launcher: true,
        },
    },
}
