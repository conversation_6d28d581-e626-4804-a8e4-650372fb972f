// Copyright 2018 Google Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package symbol_inject

import (
	"debug/pe"
)

/*
Generated from: prebuilts/gcc/linux-x86/host/x86_64-w64-mingw32-4.8/bin/x86_64-w64-mingw32-g++ -o a.exe test.c

#include <unistd.h>

char soong_build_number[128] = "PLACEHOLDER";

int main() {
  write(STDOUT_FILENO, soong_build_number, sizeof(soong_build_number));
*/

var peSymbolTable1 = &pe.File{
	FileHeader: pe.FileHeader{
		Machine: pe.IMAGE_FILE_MACHINE_AMD64,
	},
	Sections: []*pe.Section{
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".text", VirtualSize: 0x1cc0, VirtualAddress: 0x1000, Size: 0x1e00, Offset: 0x600, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x60500020}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".data", VirtualSize: 0x120, VirtualAddress: 0x3000, Size: 0x200, Offset: 0x2400, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0xc0600040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".rdata", VirtualSize: 0x5e0, VirtualAddress: 0x4000, Size: 0x600, Offset: 0x2600, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x40500040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".pdata", VirtualSize: 0x234, VirtualAddress: 0x5000, Size: 0x400, Offset: 0x2c00, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x40300040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".xdata", VirtualSize: 0x200, VirtualAddress: 0x6000, Size: 0x200, Offset: 0x3000, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x40300040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".bss", VirtualSize: 0x9b0, VirtualAddress: 0x7000, Size: 0x0, Offset: 0x0, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0xc0600080}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".idata", VirtualSize: 0x7c8, VirtualAddress: 0x8000, Size: 0x800, Offset: 0x3200, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0xc0300040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".CRT", VirtualSize: 0x68, VirtualAddress: 0x9000, Size: 0x200, Offset: 0x3a00, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0xc0400040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".tls", VirtualSize: 0x68, VirtualAddress: 0xa000, Size: 0x200, Offset: 0x3c00, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0xc0600040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_aranges", VirtualSize: 0x420, VirtualAddress: 0xb000, Size: 0x600, Offset: 0x3e00, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42500040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_info", VirtualSize: 0xc125, VirtualAddress: 0xc000, Size: 0xc200, Offset: 0x4400, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42100040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_abbrev", VirtualSize: 0x1a80, VirtualAddress: 0x19000, Size: 0x1c00, Offset: 0x10600, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42100040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_line", VirtualSize: 0x2182, VirtualAddress: 0x1b000, Size: 0x2200, Offset: 0x12200, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42100040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_frame", VirtualSize: 0xb28, VirtualAddress: 0x1e000, Size: 0xc00, Offset: 0x14400, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42400040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_str", VirtualSize: 0x300, VirtualAddress: 0x1f000, Size: 0x400, Offset: 0x15000, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42100040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_loc", VirtualSize: 0x2b76, VirtualAddress: 0x20000, Size: 0x2c00, Offset: 0x15400, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42100040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_ranges", VirtualSize: 0x4f0, VirtualAddress: 0x23000, Size: 0x600, Offset: 0x18000, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42100040}},
	},
	Symbols: []*pe.Symbol{
		&pe.Symbol{Name: ".file", Value: 0x35, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__mingw_invalidParameterHandler", Value: 0x0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "pre_c_init", Value: 0x10, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "managedapp", Value: 0x8, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "pre_cpp_init", Value: 0x130, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "envp", Value: 0x18, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "argv", Value: 0x20, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "argc", Value: 0x28, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "startinfo", Value: 0x0, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "argret", Value: 0x10, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__tmainCRTStartup", Value: 0x180, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "has_cctor", Value: 0x4, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "mainret", Value: 0xc, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "WinMainCRTStartup", Value: 0x4b0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".l_startw", Value: 0x4b4, SectionNumber: 1, Type: 0x0, StorageClass: 0x6},
		&pe.Symbol{Name: ".l_endw", Value: 0x4c9, SectionNumber: 1, Type: 0x0, StorageClass: 0x6},
		&pe.Symbol{Name: "mainCRTStartup", Value: 0x4d0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".l_start", Value: 0x4d4, SectionNumber: 1, Type: 0x0, StorageClass: 0x6},
		&pe.Symbol{Name: ".l_end", Value: 0x4e9, SectionNumber: 1, Type: 0x0, StorageClass: 0x6},
		&pe.Symbol{Name: ".text", Value: 0x0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x0, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x0, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x0, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XCAA", Value: 0x8, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XIAA", Value: 0x20, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x0, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x0, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x0, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_ranges", Value: 0x0, SectionNumber: 17, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x0, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x0, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x280, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x0, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x3f, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x4f0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x10, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x30, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x2a0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x4f, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "main", Value: 0x4f0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x4f0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x20, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x30, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x68, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x48, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x2c0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x6c, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "mingw_onexit", Value: 0x520, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "atexit", Value: 0x5d0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x520, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xa0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x30, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x74, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x54, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x27d0, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x433, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x510, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x30, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x3de, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x2e0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x120, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x8c, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__do_global_dtors", Value: 0x5f0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "p.68721", Value: 0xa0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__do_global_ctors", Value: 0x630, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__main", Value: 0x690, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "initialized", Value: 0x30, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x5f0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xa0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x30, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x84, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x6c, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x2ed5, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x5ae, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x607, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x60, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x5a3, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x300, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x190, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0xa0, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x6b0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xb0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x40, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x34aa, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x6c2, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x90, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x710, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x21f, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x320, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0xb2, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x6b0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xc0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x40, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x3a06, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x744, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0xb0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x894, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x340, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0xce, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "my_lconv_init", Value: 0x6b0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x6b0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xc0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x50, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x9c, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x90, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XIC", Value: 0x28, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x3ae7, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x76e, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0xd0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x91f, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x360, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x218, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0xe8, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "_setargv", Value: 0x6c0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x6c0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x60, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0xa0, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x9c, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x3ffe, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x808, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x100, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0xab8, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x380, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x248, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x110, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__security_init_cookie", Value: 0x6d0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".data$__security_cookie", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data$__security_cookie_complement", Value: 0x110, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__report_gsfailure", Value: 0x7a0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "GS_ContextRecord", Value: 0x60, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "GS_ExceptionRecord", Value: 0x540, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "GS_ExceptionPointers", Value: 0x0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x6d0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x60, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0xa4, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0xa8, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata", Value: 0x0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x41c8, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x83f, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x6ae, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x130, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0xb56, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x237, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x3a0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x278, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x122, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x8a0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x5e0, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x506e, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xa10, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x160, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0xcde, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x3c0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x158, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__dyn_tls_dtor", Value: 0x8a0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "__dyn_tls_init", Value: 0x8d0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__xd_a", Value: 0x58, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__xd_z", Value: 0x60, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__tlregdtor", Value: 0x940, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x8a0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x5f0, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0xc4, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0xc0, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XLD", Value: 0x48, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XLC", Value: 0x40, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata", Value: 0x10, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XDZ", Value: 0x60, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XDA", Value: 0x58, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".tls", Value: 0x20, SectionNumber: 9, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XLZ", Value: 0x50, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XLA", Value: 0x38, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".tls$ZZZ", Value: 0x60, SectionNumber: 9, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".tls$AAA", Value: 0x0, SectionNumber: 9, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x514c, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xa3a, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x816, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x180, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0xd69, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x250, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x3e0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x330, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x172, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x950, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x600, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XCZ", Value: 0x10, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XCA", Value: 0x0, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XIZ", Value: 0x30, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XIA", Value: 0x18, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x5854, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xbce, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x1b0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0xec8, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x400, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x193, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__mingw_raise_matherr", Value: 0x950, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "stUserMathErr", Value: 0x600, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__mingw_setusermatherr", Value: 0x9a0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_matherr", Value: 0x9b0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x950, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x600, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0xdc, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0xe4, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata", Value: 0x20, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x5a33, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xc27, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0xb6f, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x1d0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0xf53, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x420, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x3d0, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x1ae, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "_fpreset", Value: 0xab0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "fpreset", Value: 0xab0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0xab0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x610, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x100, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x108, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x61bd, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xda1, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x200, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1129, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x440, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x460, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x1c9, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "_decode_pointer", Value: 0xac0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_encode_pointer", Value: 0xad0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0xac0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x610, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x104, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x114, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x62a8, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xdcb, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x230, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x11c8, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x460, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x490, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x1f0, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__report_error", Value: 0xae0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "__write_memory.part.0", Value: 0xb50, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "maxSections", Value: 0x624, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "the_secs", Value: 0x628, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "_pei386_runtime_relocator", Value: 0xcd0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "was_init.70054", Value: 0x620, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0xae0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x620, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata", Value: 0x160, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x10c, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x12c, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x67b9, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xe52, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0xda1, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x260, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_ranges", Value: 0xe0, SectionNumber: 17, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x12f7, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x26f, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x480, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x4d8, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x202, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0xff0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x630, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x79a7, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x1174, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x290, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x157b, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x4a0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x227, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__mingw_SEH_error_handler", Value: 0xff0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_init_ehandler", Value: 0x11b0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "was_here.69886", Value: 0x648, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "emu_pdata", Value: 0x760, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "emu_xdata", Value: 0x660, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "_gnu_exception_handler", Value: 0x12a0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0xff0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x640, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x144, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x150, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata", Value: 0x270, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x7a83, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x119e, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x161e, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x2b0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1606, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x278, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x4c0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x5d0, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x24b, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__mingwthr_run_key_dtors.part.0", Value: 0x1450, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "__mingwthr_cs", Value: 0x900, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "key_dtor_list", Value: 0x8e0, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "___w64_mingwthr_add_key_dtor", Value: 0x14c0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingwthr_cs_init", Value: 0x8e8, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "___w64_mingwthr_remove_key_dtor", Value: 0x1540, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_TLScallback", Value: 0x15e0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x1450, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x8e0, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x168, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x174, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x8f89, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x1402, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x1f13, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x2e0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_ranges", Value: 0x330, SectionNumber: 17, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x185b, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x4e0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x6d8, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x25d, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x16d0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x940, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x98ad, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x15e4, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x310, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1a30, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x500, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x26f, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x16d0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xe0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x940, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x9989, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x160e, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x330, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1aba, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x520, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x298, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "_ValidateImageBase.part.0", Value: 0x16d0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "_ValidateImageBase", Value: 0x16f0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_FindPESection", Value: 0x1710, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_FindPESectionByName", Value: 0x1760, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_GetSectionForAddress", Value: 0x1810, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_GetSectionCount", Value: 0x1860, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_FindPESectionExec", Value: 0x18b0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_GetPEImageBase", Value: 0x1950, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_IsNonwritableInCurrentImage", Value: 0x19a0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_enum_import_library_names", Value: 0x1a00, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x16d0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xe0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x198, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x1a4, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x9ab9, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x1638, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x25ce, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x350, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_ranges", Value: 0x360, SectionNumber: 17, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1b4e, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x2ac, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x540, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x808, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x2aa, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".debug_info", Value: 0xad5d, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x18a1, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1d87, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1ab0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xe0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x380, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0xa98, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x2bc, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x1af0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xe0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0xae44, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x18b5, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x3b0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1e2a, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x560, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x399, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x1af0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xe0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0xb9a0, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x1928, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x3d0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1f18, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x580, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1af0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7b8, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x38c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1dc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x6d2, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1af8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7b4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x384, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1d4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x6c6, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b00, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7b0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x37c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1cc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x6bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b08, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7ac, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x374, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1c4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x6b2, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b10, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7a8, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x36c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x6a8, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b18, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7a4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x364, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1b4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x69e, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b20, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7a0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x35c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1ac, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x694, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b28, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x79c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x354, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1a4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x68a, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b30, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x798, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x34c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x19c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x682, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b38, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x794, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x344, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x194, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x678, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b40, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x790, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x33c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x18c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x670, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b48, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x78c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x334, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x184, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x666, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b50, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x788, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x32c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x17c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x65e, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b58, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x784, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x324, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x174, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x654, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b60, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x780, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x31c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x16c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x64a, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b68, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x77c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x314, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x164, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x642, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b70, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x778, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x30c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x15c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x636, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b78, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x774, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x304, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x154, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x62c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b78, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x770, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2fc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x14c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x622, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b80, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x76c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2f4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x144, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x614, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b88, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x768, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x13c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x60a, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b88, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x764, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2e4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x134, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x5f6, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b90, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x760, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2dc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x12c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x5e4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b98, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x75c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2d4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x124, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x5d4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1ba0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x758, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2cc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x11c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x5c6, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1ba8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x754, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2c4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x114, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x5ba, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1ba8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x750, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x10c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x5aa, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1bb0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x74c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2b4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x104, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x59c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1bb8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x748, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2ac, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xfc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x584, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x3b7, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "mingw_get_invalid_parameter_handler", Value: 0x1bc0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "handler", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "_get_invalid_parameter_handler", Value: 0x1bc0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_set_invalid_parameter_handler", Value: 0x1bd0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "_set_invalid_parameter_handler", Value: 0x1bd0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x1bc0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x1f8, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x21c, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0xba98, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x1952, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x3f0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1fa9, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x5a0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0xae0, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x3c5, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "hname", Value: 0xfc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "fthunk", Value: 0x2ac, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1be0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$2", Value: 0x14, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xfc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2ac, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x474, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x1be0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1e4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x394, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1be0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x734, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x29c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xec, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x574, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1be8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x730, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x294, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xe4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x562, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1bf0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x72c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x28c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xdc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x546, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1bf8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x728, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x284, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xd4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x538, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c00, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x724, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x27c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xcc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x524, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c08, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x720, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x274, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xc4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x51c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c10, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x71c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x26c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xbc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x4fe, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c18, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x718, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x264, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xb4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x4ea, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c20, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x714, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x25c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xac, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x4d0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c28, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x710, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x254, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xa4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x4bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c30, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x70c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x24c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x9c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x4a6, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c38, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x708, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x244, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x94, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x48c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c40, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x704, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x23c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x8c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x474, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c48, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x700, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x234, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x84, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x458, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c50, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6fc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x22c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x7c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x448, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c58, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6f8, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x224, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x74, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x42e, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c60, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6f4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x21c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x6c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x41c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c68, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6f0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x214, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x64, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x40c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c70, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x20c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x5c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x3f6, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c78, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6e8, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x204, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x54, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x3e0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c80, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6e4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x1fc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x4c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x3cc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c88, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6e0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x1f4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x44, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x3b4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c90, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6dc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x1ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x3c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x39c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x482, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "hname", Value: 0x3c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "fthunk", Value: 0x1ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1ca0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$2", Value: 0x0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x3c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x1ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x490, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x1ca0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xf4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2a4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x738, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x49a, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x1ca0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x5c0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__xc_z", Value: 0x10, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___RUNTIME_PSEUDO_RELOC_LIST__", Value: 0x4045e0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetStartupInfoA", Value: 0x21c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_abort", Value: 0x32c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__lib64_libkernel32_a_iname", Value: 0x738, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__data_start__", Value: 0x0, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___DTOR_LIST__", Value: 0x1cb0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__fmode", Value: 0x304, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__lock", Value: 0x314, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_RtlVirtualUnwind", Value: 0x264, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "SetUnhandledExceptionFilter", Value: 0x1c10, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_calloc", Value: 0x334, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_lock", Value: 0x1b68, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "___tls_start__", Value: 0x0, SectionNumber: 9, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__ImageBase", Value: 0x400000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xl_a", Value: 0x38, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "GetLastError", Value: 0x1c68, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "GetSystemTimeAsFileTime", Value: 0x1c58, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_initltssuo_force", Value: 0x5f0, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__rt_psrelocs_start", Value: 0x5e0, SectionNumber: 3, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_cexit", Value: 0x1b78, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___dllonexit", Value: 0x2b4, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__dll_characteristics__", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__size_of_stack_commit__", Value: 0x1000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__iob_func", Value: 0x1ba0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__acmdln", Value: 0x2ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__size_of_stack_reserve__", Value: 0x200000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__major_subsystem_version__", Value: 0x5, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xl_start__", Value: 0x38, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_DeleteCriticalSection", Value: 0x1ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xl_d", Value: 0x48, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__set_invalid_parameter_handler", Value: 0xf8, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_tls_end", Value: 0x60, SectionNumber: 9, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "VirtualQuery", Value: 0x1be0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xi_start__", Value: 0x18, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__amsg_exit", Value: 0x2f4, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xi_end__", Value: 0x38, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_tls_start", Value: 0x0, SectionNumber: 9, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "memcpy", Value: 0x1b18, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_winmain_lpCmdLine", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_oldexcpt_handler", Value: 0x640, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetCurrentThreadId", Value: 0x20c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "malloc", Value: 0x1b20, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "GetCurrentProcessId", Value: 0x1c78, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_CRT_MT", Value: 0xd0, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "TlsGetValue", Value: 0x1bf8, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "TerminateProcess", Value: 0x1c00, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__bss_start__", Value: 0x0, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___C_specific_handler", Value: 0x2ac, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___RUNTIME_PSEUDO_RELOC_LIST_END__", Value: 0x4045e0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "RtlLookupFunctionEntry", Value: 0x1c20, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__size_of_heap_commit__", Value: 0x1000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetLastError", Value: 0x214, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_free", Value: 0x34c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_RtlLookupFunctionEntry", Value: 0x25c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "VirtualProtect", Value: 0x1be8, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_app_type", Value: 0x610, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xp_start__", Value: 0x58, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_LeaveCriticalSection", Value: 0x23c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_pinit", Value: 0x28, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__C_specific_handler", Value: 0x1bb8, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetTickCount", Value: 0x22c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "abort", Value: 0x1b50, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xp_end__", Value: 0x58, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__dll__", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__minor_os_version__", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetSystemTimeAsFileTime", Value: 0x224, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "EnterCriticalSection", Value: 0x1c88, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_MINGW_INSTALL_DEBUG_MATHERR", Value: 0xe0, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__image_base__", Value: 0x400000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_write", Value: 0x38c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "RtlCaptureContext", Value: 0x1c28, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__section_alignment__", Value: 0x1000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__native_dllmain_reason", Value: 0xb4, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "calloc", Value: 0x1b48, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_tls_used", Value: 0x20, SectionNumber: 9, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "UnhandledExceptionFilter", Value: 0x1bf0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_initcharmax", Value: 0x50, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__IAT_end__", Value: 0x39c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "write", Value: 0x1af0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_memcpy", Value: 0x364, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__RUNTIME_PSEUDO_RELOC_LIST__", Value: 0x4045e0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "fprintf", Value: 0x1b38, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "soong_build_number", Value: 0x20, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_RtlAddFunctionTable", Value: 0x24c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "Sleep", Value: 0x1c08, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_pcppinit", Value: 0x8, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__data_end__", Value: 0x120, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_fwrite", Value: 0x354, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__CTOR_LIST__", Value: 0x1ca0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___getmainargs", Value: 0x2bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_head_lib64_libkernel32_a", Value: 0x0, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__bss_end__", Value: 0x9b0, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xi_z", Value: 0x30, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "GetTickCount", Value: 0x1c50, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_head_lib64_libmsvcrt_a", Value: 0x14, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__native_vcclrit_reason", Value: 0xb0, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xc_end__", Value: 0x18, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "RtlAddFunctionTable", Value: 0x1c30, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_EnterCriticalSection", Value: 0x1f4, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_tls_index", Value: 0x5fc, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "signal", Value: 0x1b10, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__native_startup_state", Value: 0x980, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xc_start__", Value: 0x0, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__onexitbegin", Value: 0x970, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetCurrentProcessId", Value: 0x204, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "strncmp", Value: 0x1b00, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___lconv_init", Value: 0x2d4, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_TerminateProcess", Value: 0x27c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___CTOR_LIST__", Value: 0x1ca0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_signal", Value: 0x36c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__rt_psrelocs_size", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_QueryPerformanceCounter", Value: 0x244, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_strlen", Value: 0x374, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_malloc", Value: 0x35c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_winmain_nShowCmd", Value: 0x0, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_pcinit", Value: 0x20, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__file_alignment__", Value: 0x200, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_InitializeCriticalSection", Value: 0x234, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__lconv_init", Value: 0x1b98, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__getmainargs", Value: 0x1ba8, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "InitializeCriticalSection", Value: 0x1c48, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_exit", Value: 0x33c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__major_os_version__", Value: 0x4, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_vfprintf", Value: 0x384, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___initenv", Value: 0x2c4, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__IAT_start__", Value: 0x1ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__cexit", Value: 0x2fc, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_UnhandledExceptionFilter", Value: 0x28c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xl_z", Value: 0x50, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__end__", Value: 0x1000, SectionNumber: 9, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_SetUnhandledExceptionFilter", Value: 0x26c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__onexit", Value: 0x31c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__DTOR_LIST__", Value: 0x1cb0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "RtlVirtualUnwind", Value: 0x1c18, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xi_a", Value: 0x18, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__set_app_type", Value: 0x1b90, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_Sleep", Value: 0x274, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "LeaveCriticalSection", Value: 0x1c40, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xc_a", Value: 0x0, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___setusermatherr", Value: 0x2e4, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__size_of_heap_reserve__", Value: 0x100000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xt_start__", Value: 0x58, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__subsystem__", Value: 0x3, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_amsg_exit", Value: 0x1b80, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_fmode", Value: 0x630, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__security_cookie_complement", Value: 0x110, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_TlsGetValue", Value: 0x284, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "GetCurrentProcess", Value: 0x1c80, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__setusermatherr", Value: 0x1b88, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_fprintf", Value: 0x344, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_VirtualProtect", Value: 0x294, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xl_c", Value: 0x40, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___tls_end__", Value: 0x68, SectionNumber: 9, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__onexitend", Value: 0x978, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "QueryPerformanceCounter", Value: 0x1c38, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_VirtualQuery", Value: 0x29c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__initterm", Value: 0x30c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_initltsdyn_force", Value: 0x5f4, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_dowildcard", Value: 0x40, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___iob_func", Value: 0x2cc, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__dyn_tls_init_callback", Value: 0x10, SectionNumber: 3, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_initterm", Value: 0x1b70, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_newmode", Value: 0x5e0, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "fwrite", Value: 0x1b28, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_strncmp", Value: 0x37c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__major_image_version__", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__loader_flags__", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___chkstk_ms", Value: 0x1ab0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__native_startup_lock", Value: 0x988, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_winmain_hInstance", Value: 0x968, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "GetStartupInfoA", Value: 0x1c60, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "GetCurrentThreadId", Value: 0x1c70, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_onexit", Value: 0x1b60, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__rt_psrelocs_end", Value: 0x5e0, SectionNumber: 3, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "exit", Value: 0x1b40, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__get_invalid_parameter_handler", Value: 0xf0, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__minor_subsystem_version__", Value: 0x2, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__minor_image_version__", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__unlock", Value: 0x324, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___set_app_type", Value: 0x2dc, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_initltsdrot_force", Value: 0x5f8, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_charmax", Value: 0xc0, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "strlen", Value: 0x1b08, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "DeleteCriticalSection", Value: 0x1c90, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_RtlCaptureContext", Value: 0x254, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__RUNTIME_PSEUDO_RELOC_LIST_END__", Value: 0x4045e0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__dllonexit", Value: 0x1bb0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_unlock", Value: 0x1b58, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetCurrentProcess", Value: 0x1fc, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xt_end__", Value: 0x58, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__lib64_libmsvcrt_a_iname", Value: 0x7bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "vfprintf", Value: 0x1af8, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "free", Value: 0x1b30, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__security_cookie", Value: 0x100, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
	},
}

/* Generated from: prebuilts/gcc/linux-x86/host/x86_64-w64-mingw32-4.8/bin/x86_64-w64-mingw32-g++ -o a.exe test2.c

#include <unistd.h>

char symbol1[128] = "PLACEHOLDER1";
char symbol2[128] = "PLACEHOLDER2";

int main() {
  write(STDOUT_FILENO, symbol1, sizeof(symbol1));
  write(STDOUT_FILENO, symbol2, sizeof(symbol2));
}
*/

var peSymbolTable2 = &pe.File{
	FileHeader: pe.FileHeader{
		Machine: pe.IMAGE_FILE_MACHINE_AMD64,
	},
	Sections: []*pe.Section{
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".text", VirtualSize: 0x1ce0, VirtualAddress: 0x1000, Size: 0x1e00, Offset: 0x600, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x60500020}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".data", VirtualSize: 0x1a0, VirtualAddress: 0x3000, Size: 0x200, Offset: 0x2400, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0xc0600040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".rdata", VirtualSize: 0x5e0, VirtualAddress: 0x4000, Size: 0x600, Offset: 0x2600, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x40500040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".pdata", VirtualSize: 0x234, VirtualAddress: 0x5000, Size: 0x400, Offset: 0x2c00, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x40300040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".xdata", VirtualSize: 0x200, VirtualAddress: 0x6000, Size: 0x200, Offset: 0x3000, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x40300040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".bss", VirtualSize: 0x9b0, VirtualAddress: 0x7000, Size: 0x0, Offset: 0x0, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0xc0600080}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".idata", VirtualSize: 0x7c8, VirtualAddress: 0x8000, Size: 0x800, Offset: 0x3200, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0xc0300040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".CRT", VirtualSize: 0x68, VirtualAddress: 0x9000, Size: 0x200, Offset: 0x3a00, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0xc0400040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".tls", VirtualSize: 0x68, VirtualAddress: 0xa000, Size: 0x200, Offset: 0x3c00, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0xc0600040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_aranges", VirtualSize: 0x420, VirtualAddress: 0xb000, Size: 0x600, Offset: 0x3e00, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42500040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_info", VirtualSize: 0xc125, VirtualAddress: 0xc000, Size: 0xc200, Offset: 0x4400, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42100040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_abbrev", VirtualSize: 0x1a80, VirtualAddress: 0x19000, Size: 0x1c00, Offset: 0x10600, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42100040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_line", VirtualSize: 0x2182, VirtualAddress: 0x1b000, Size: 0x2200, Offset: 0x12200, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42100040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_frame", VirtualSize: 0xb28, VirtualAddress: 0x1e000, Size: 0xc00, Offset: 0x14400, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42400040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_str", VirtualSize: 0x300, VirtualAddress: 0x1f000, Size: 0x400, Offset: 0x15000, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42100040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_loc", VirtualSize: 0x2b76, VirtualAddress: 0x20000, Size: 0x2c00, Offset: 0x15400, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42100040}},
		&pe.Section{SectionHeader: pe.SectionHeader{Name: ".debug_ranges", VirtualSize: 0x4f0, VirtualAddress: 0x23000, Size: 0x600, Offset: 0x18000, PointerToRelocations: 0x0, PointerToLineNumbers: 0x0, NumberOfRelocations: 0x0, NumberOfLineNumbers: 0x0, Characteristics: 0x42100040}},
	},
	Symbols: []*pe.Symbol{
		&pe.Symbol{Name: ".file", Value: 0x35, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__mingw_invalidParameterHandler", Value: 0x0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "pre_c_init", Value: 0x10, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "managedapp", Value: 0x8, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "pre_cpp_init", Value: 0x130, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "envp", Value: 0x18, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "argv", Value: 0x20, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "argc", Value: 0x28, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "startinfo", Value: 0x0, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "argret", Value: 0x10, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__tmainCRTStartup", Value: 0x180, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "has_cctor", Value: 0x4, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "mainret", Value: 0xc, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "WinMainCRTStartup", Value: 0x4b0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".l_startw", Value: 0x4b4, SectionNumber: 1, Type: 0x0, StorageClass: 0x6},
		&pe.Symbol{Name: ".l_endw", Value: 0x4c9, SectionNumber: 1, Type: 0x0, StorageClass: 0x6},
		&pe.Symbol{Name: "mainCRTStartup", Value: 0x4d0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".l_start", Value: 0x4d4, SectionNumber: 1, Type: 0x0, StorageClass: 0x6},
		&pe.Symbol{Name: ".l_end", Value: 0x4e9, SectionNumber: 1, Type: 0x0, StorageClass: 0x6},
		&pe.Symbol{Name: ".text", Value: 0x0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x0, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x0, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x0, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x0, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XCAA", Value: 0x8, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XIAA", Value: 0x20, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x0, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x0, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x0, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_ranges", Value: 0x0, SectionNumber: 17, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x0, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x0, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x280, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x0, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x3f, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x4f0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x10, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x30, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x2a0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x4f, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "main", Value: 0x4f0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x4f0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x20, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x30, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x68, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x48, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x2c0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x6c, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "mingw_onexit", Value: 0x540, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "atexit", Value: 0x5f0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x540, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x120, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x30, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x74, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x54, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x27d0, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x433, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x510, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x30, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x3de, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x2e0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x120, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x8c, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__do_global_dtors", Value: 0x610, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "p.68721", Value: 0x120, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__do_global_ctors", Value: 0x650, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__main", Value: 0x6b0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "initialized", Value: 0x30, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x610, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x120, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x30, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x84, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x6c, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x2ed5, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x5ae, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x607, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x60, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x5a3, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x300, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x190, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0xa0, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x6d0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x130, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x40, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x34aa, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x6c2, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x90, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x710, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x21f, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x320, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0xb2, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x6d0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x140, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x40, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x3a06, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x744, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0xb0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x894, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x340, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0xce, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "my_lconv_init", Value: 0x6d0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x6d0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x140, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x50, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x9c, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x90, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XIC", Value: 0x28, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x3ae7, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x76e, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0xd0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x91f, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x360, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x218, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0xe8, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "_setargv", Value: 0x6e0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x6e0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x60, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0xa0, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x9c, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x3ffe, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x808, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x100, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0xab8, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x380, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x248, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x110, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__security_init_cookie", Value: 0x6f0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".data$__security_cookie", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data$__security_cookie_complement", Value: 0x190, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__report_gsfailure", Value: 0x7c0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "GS_ContextRecord", Value: 0x60, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "GS_ExceptionRecord", Value: 0x540, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "GS_ExceptionPointers", Value: 0x0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x6f0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x60, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0xa4, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0xa8, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata", Value: 0x0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x41c8, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x83f, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x6ae, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x130, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0xb56, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x237, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x3a0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x278, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x122, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x8c0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x5e0, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x506e, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xa10, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x160, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0xcde, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x3c0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x158, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__dyn_tls_dtor", Value: 0x8c0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "__dyn_tls_init", Value: 0x8f0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__xd_a", Value: 0x58, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__xd_z", Value: 0x60, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__tlregdtor", Value: 0x960, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x8c0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x5f0, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0xc4, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0xc0, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XLD", Value: 0x48, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XLC", Value: 0x40, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata", Value: 0x10, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XDZ", Value: 0x60, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XDA", Value: 0x58, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".tls", Value: 0x20, SectionNumber: 9, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XLZ", Value: 0x50, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XLA", Value: 0x38, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".tls$ZZZ", Value: 0x60, SectionNumber: 9, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".tls$AAA", Value: 0x0, SectionNumber: 9, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x514c, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xa3a, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x816, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x180, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0xd69, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x250, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x3e0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x330, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x172, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x970, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x600, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XCZ", Value: 0x10, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XCA", Value: 0x0, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XIZ", Value: 0x30, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".CRT$XIA", Value: 0x18, SectionNumber: 8, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x5854, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xbce, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x1b0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0xec8, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x400, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x193, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__mingw_raise_matherr", Value: 0x970, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "stUserMathErr", Value: 0x600, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__mingw_setusermatherr", Value: 0x9c0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_matherr", Value: 0x9d0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x970, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x600, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0xdc, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0xe4, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata", Value: 0x20, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x5a33, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xc27, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0xb6f, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x1d0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0xf53, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x420, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x3d0, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x1ae, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "_fpreset", Value: 0xad0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "fpreset", Value: 0xad0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0xad0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x610, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x100, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x108, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x61bd, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xda1, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x200, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1129, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x440, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x460, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x1c9, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "_decode_pointer", Value: 0xae0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_encode_pointer", Value: 0xaf0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0xae0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x610, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x104, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x114, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x62a8, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xdcb, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x230, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x11c8, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x460, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x490, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x1f0, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__report_error", Value: 0xb00, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "__write_memory.part.0", Value: 0xb70, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "maxSections", Value: 0x624, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "the_secs", Value: 0x628, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "_pei386_runtime_relocator", Value: 0xcf0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "was_init.70054", Value: 0x620, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0xb00, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x620, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata", Value: 0x160, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x10c, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x12c, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x67b9, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0xe52, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0xda1, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x260, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_ranges", Value: 0xe0, SectionNumber: 17, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x12f7, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x26f, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x480, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x4d8, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x202, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x1010, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x630, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x79a7, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x1174, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x290, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x157b, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x4a0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x227, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__mingw_SEH_error_handler", Value: 0x1010, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_init_ehandler", Value: 0x11d0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "was_here.69886", Value: 0x648, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "emu_pdata", Value: 0x760, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "emu_xdata", Value: 0x660, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "_gnu_exception_handler", Value: 0x12c0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x1010, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x640, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x144, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x150, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata", Value: 0x270, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x7a83, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x119e, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x161e, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x2b0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1606, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x278, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x4c0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x5d0, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x24b, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "__mingwthr_run_key_dtors.part.0", Value: 0x1470, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "__mingwthr_cs", Value: 0x900, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "key_dtor_list", Value: 0x8e0, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "___w64_mingwthr_add_key_dtor", Value: 0x14e0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingwthr_cs_init", Value: 0x8e8, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "___w64_mingwthr_remove_key_dtor", Value: 0x1560, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_TLScallback", Value: 0x1600, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x1470, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x8e0, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x168, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x174, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x8f89, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x1402, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x1f13, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x2e0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_ranges", Value: 0x330, SectionNumber: 17, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x185b, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x4e0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x6d8, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x25d, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x16f0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x940, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x98ad, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x15e4, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x310, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1a30, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x500, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x26f, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x16f0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x160, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x940, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x9989, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x160e, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x330, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1aba, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x520, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x298, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "_ValidateImageBase.part.0", Value: 0x16f0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "_ValidateImageBase", Value: 0x1710, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_FindPESection", Value: 0x1730, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_FindPESectionByName", Value: 0x1780, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_GetSectionForAddress", Value: 0x1830, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_GetSectionCount", Value: 0x1880, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_FindPESectionExec", Value: 0x18d0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_GetPEImageBase", Value: 0x1970, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_IsNonwritableInCurrentImage", Value: 0x19c0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_enum_import_library_names", Value: 0x1a20, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x16f0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x160, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x198, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x1a4, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0x9ab9, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x1638, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_loc", Value: 0x25ce, SectionNumber: 16, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x350, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_ranges", Value: 0x360, SectionNumber: 17, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1b4e, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_str", Value: 0x2ac, SectionNumber: 15, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x540, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0x808, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x2aa, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".debug_info", Value: 0xad5d, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x18a1, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1d87, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1ad0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x160, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x380, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0xa98, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x2bc, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x1b10, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x160, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0xae44, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x18b5, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x3b0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1e2a, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x560, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x399, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x1b10, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x160, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0xb9a0, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x1928, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x3d0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1f18, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x580, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b10, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7b8, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x38c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1dc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x6d2, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b18, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7b4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x384, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1d4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x6c6, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b20, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7b0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x37c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1cc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x6bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b28, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7ac, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x374, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1c4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x6b2, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b30, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7a8, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x36c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x6a8, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b38, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7a4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x364, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1b4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x69e, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b40, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7a0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x35c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1ac, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x694, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b48, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x79c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x354, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1a4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x68a, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b50, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x798, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x34c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x19c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x682, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b58, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x794, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x344, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x194, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x678, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b60, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x790, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x33c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x18c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x670, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b68, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x78c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x334, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x184, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x666, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b70, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x788, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x32c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x17c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x65e, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b78, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x784, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x324, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x174, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x654, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b80, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x780, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x31c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x16c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x64a, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b88, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x77c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x314, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x164, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x642, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b90, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x778, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x30c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x15c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x636, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b98, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x774, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x304, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x154, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x62c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1b98, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x770, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2fc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x14c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x622, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1ba0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x76c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2f4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x144, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x614, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1ba8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x768, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x13c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x60a, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1ba8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x764, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2e4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x134, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x5f6, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1bb0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x760, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2dc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x12c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x5e4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1bb8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x75c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2d4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x124, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x5d4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1bc0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x758, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2cc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x11c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x5c6, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1bc8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x754, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2c4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x114, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x5ba, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1bc8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x750, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x10c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x5aa, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1bd0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x74c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2b4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x104, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x59c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1bd8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x748, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2ac, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xfc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x584, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x3b7, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "mingw_get_invalid_parameter_handler", Value: 0x1be0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "handler", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "_get_invalid_parameter_handler", Value: 0x1be0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_set_invalid_parameter_handler", Value: 0x1bf0, SectionNumber: 1, Type: 0x20, StorageClass: 0x3},
		&pe.Symbol{Name: "_set_invalid_parameter_handler", Value: 0x1bf0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: ".text", Value: 0x1be0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x950, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".xdata", Value: 0x1f8, SectionNumber: 5, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".pdata", Value: 0x21c, SectionNumber: 4, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_info", Value: 0xba98, SectionNumber: 11, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_abbrev", Value: 0x1952, SectionNumber: 12, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_aranges", Value: 0x3f0, SectionNumber: 10, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_line", Value: 0x1fa9, SectionNumber: 13, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x5a0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".debug_frame", Value: 0xae0, SectionNumber: 14, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x3c5, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "hname", Value: 0xfc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "fthunk", Value: 0x2ac, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c00, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$2", Value: 0x14, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xfc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2ac, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x474, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x1c00, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x1e4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x394, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x7bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c00, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x734, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x29c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xec, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x574, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c08, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x730, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x294, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xe4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x562, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c10, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x72c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x28c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xdc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x546, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c18, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x728, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x284, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xd4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x538, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c20, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x724, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x27c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xcc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x524, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c28, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x720, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x274, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xc4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x51c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c30, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x71c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x26c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xbc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x4fe, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c38, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x718, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x264, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xb4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x4ea, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c40, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x714, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x25c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xac, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x4d0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c48, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x710, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x254, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xa4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x4bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c50, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x70c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x24c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x9c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x4a6, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c58, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x708, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x244, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x94, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x48c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c60, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x704, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x23c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x8c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x474, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c68, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x700, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x234, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x84, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x458, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c70, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6fc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x22c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x7c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x448, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c78, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6f8, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x224, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x74, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x42e, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c80, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6f4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x21c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x6c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x41c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c88, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6f0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x214, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x64, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x40c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c90, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x20c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x5c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x3f6, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1c98, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6e8, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x204, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x54, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x3e0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1ca0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6e4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x1fc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x4c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x3cc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1ca8, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6e0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x1f4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x44, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x3b4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1cb0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x6dc, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x1ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x3c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$6", Value: 0x39c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x482, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: "hname", Value: 0x3c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "fthunk", Value: 0x1ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".text", Value: 0x1cc0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$2", Value: 0x0, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0x3c, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x1ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x490, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x1cc0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$4", Value: 0xf4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$5", Value: 0x2a4, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".idata$7", Value: 0x738, SectionNumber: 7, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".file", Value: 0x49a, SectionNumber: -2, Type: 0x0, StorageClass: 0x67},
		&pe.Symbol{Name: ".text", Value: 0x1cc0, SectionNumber: 1, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".data", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".bss", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: ".rdata$zzz", Value: 0x5c0, SectionNumber: 3, Type: 0x0, StorageClass: 0x3},
		&pe.Symbol{Name: "__xc_z", Value: 0x10, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___RUNTIME_PSEUDO_RELOC_LIST__", Value: 0x4045e0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetStartupInfoA", Value: 0x21c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "symbol1", Value: 0x20, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_abort", Value: 0x32c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__lib64_libkernel32_a_iname", Value: 0x738, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__data_start__", Value: 0x0, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___DTOR_LIST__", Value: 0x1cd0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__fmode", Value: 0x304, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__lock", Value: 0x314, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_RtlVirtualUnwind", Value: 0x264, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "SetUnhandledExceptionFilter", Value: 0x1c30, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_calloc", Value: 0x334, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_lock", Value: 0x1b88, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "___tls_start__", Value: 0x0, SectionNumber: 9, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__ImageBase", Value: 0x400000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xl_a", Value: 0x38, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "GetLastError", Value: 0x1c88, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "GetSystemTimeAsFileTime", Value: 0x1c78, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_initltssuo_force", Value: 0x5f0, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__rt_psrelocs_start", Value: 0x5e0, SectionNumber: 3, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_cexit", Value: 0x1b98, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___dllonexit", Value: 0x2b4, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__dll_characteristics__", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__size_of_stack_commit__", Value: 0x1000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__iob_func", Value: 0x1bc0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__acmdln", Value: 0x2ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__size_of_stack_reserve__", Value: 0x200000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__major_subsystem_version__", Value: 0x5, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xl_start__", Value: 0x38, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_DeleteCriticalSection", Value: 0x1ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xl_d", Value: 0x48, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__set_invalid_parameter_handler", Value: 0x178, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_tls_end", Value: 0x60, SectionNumber: 9, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "VirtualQuery", Value: 0x1c00, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xi_start__", Value: 0x18, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__amsg_exit", Value: 0x2f4, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xi_end__", Value: 0x38, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_tls_start", Value: 0x0, SectionNumber: 9, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "memcpy", Value: 0x1b38, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_winmain_lpCmdLine", Value: 0x960, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_oldexcpt_handler", Value: 0x640, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetCurrentThreadId", Value: 0x20c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "malloc", Value: 0x1b40, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "GetCurrentProcessId", Value: 0x1c98, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_CRT_MT", Value: 0x150, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "TlsGetValue", Value: 0x1c18, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "TerminateProcess", Value: 0x1c20, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__bss_start__", Value: 0x0, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___C_specific_handler", Value: 0x2ac, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___RUNTIME_PSEUDO_RELOC_LIST_END__", Value: 0x4045e0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "RtlLookupFunctionEntry", Value: 0x1c40, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__size_of_heap_commit__", Value: 0x1000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetLastError", Value: 0x214, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_free", Value: 0x34c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_RtlLookupFunctionEntry", Value: 0x25c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "VirtualProtect", Value: 0x1c08, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_app_type", Value: 0x610, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xp_start__", Value: 0x58, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_LeaveCriticalSection", Value: 0x23c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_pinit", Value: 0x28, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__C_specific_handler", Value: 0x1bd8, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetTickCount", Value: 0x22c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "abort", Value: 0x1b70, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xp_end__", Value: 0x58, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__dll__", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__minor_os_version__", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetSystemTimeAsFileTime", Value: 0x224, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "EnterCriticalSection", Value: 0x1ca8, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_MINGW_INSTALL_DEBUG_MATHERR", Value: 0x160, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__image_base__", Value: 0x400000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_write", Value: 0x38c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "RtlCaptureContext", Value: 0x1c48, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__section_alignment__", Value: 0x1000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__native_dllmain_reason", Value: 0x134, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "calloc", Value: 0x1b68, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_tls_used", Value: 0x20, SectionNumber: 9, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "UnhandledExceptionFilter", Value: 0x1c10, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_initcharmax", Value: 0x50, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__IAT_end__", Value: 0x39c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "write", Value: 0x1b10, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_memcpy", Value: 0x364, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__RUNTIME_PSEUDO_RELOC_LIST__", Value: 0x4045e0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "fprintf", Value: 0x1b58, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_RtlAddFunctionTable", Value: 0x24c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "Sleep", Value: 0x1c28, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_pcppinit", Value: 0x8, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__data_end__", Value: 0x1a0, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_fwrite", Value: 0x354, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__CTOR_LIST__", Value: 0x1cc0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___getmainargs", Value: 0x2bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_head_lib64_libkernel32_a", Value: 0x0, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__bss_end__", Value: 0x9b0, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xi_z", Value: 0x30, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "GetTickCount", Value: 0x1c70, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_head_lib64_libmsvcrt_a", Value: 0x14, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__native_vcclrit_reason", Value: 0x130, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xc_end__", Value: 0x18, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "RtlAddFunctionTable", Value: 0x1c50, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_EnterCriticalSection", Value: 0x1f4, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_tls_index", Value: 0x5fc, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "signal", Value: 0x1b30, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__native_startup_state", Value: 0x980, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xc_start__", Value: 0x0, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__onexitbegin", Value: 0x970, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetCurrentProcessId", Value: 0x204, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "strncmp", Value: 0x1b20, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___lconv_init", Value: 0x2d4, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_TerminateProcess", Value: 0x27c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___CTOR_LIST__", Value: 0x1cc0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_signal", Value: 0x36c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__rt_psrelocs_size", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_QueryPerformanceCounter", Value: 0x244, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_strlen", Value: 0x374, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_malloc", Value: 0x35c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_winmain_nShowCmd", Value: 0x0, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_pcinit", Value: 0x20, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__file_alignment__", Value: 0x200, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_InitializeCriticalSection", Value: 0x234, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__lconv_init", Value: 0x1bb8, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__getmainargs", Value: 0x1bc8, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "InitializeCriticalSection", Value: 0x1c68, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_exit", Value: 0x33c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__major_os_version__", Value: 0x4, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_vfprintf", Value: 0x384, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___initenv", Value: 0x2c4, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__IAT_start__", Value: 0x1ec, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__cexit", Value: 0x2fc, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_UnhandledExceptionFilter", Value: 0x28c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xl_z", Value: 0x50, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__end__", Value: 0x1000, SectionNumber: 9, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_SetUnhandledExceptionFilter", Value: 0x26c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__onexit", Value: 0x31c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__DTOR_LIST__", Value: 0x1cd0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "RtlVirtualUnwind", Value: 0x1c38, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xi_a", Value: 0x18, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "symbol2", Value: 0xa0, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__set_app_type", Value: 0x1bb0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_Sleep", Value: 0x274, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "LeaveCriticalSection", Value: 0x1c60, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xc_a", Value: 0x0, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___setusermatherr", Value: 0x2e4, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__size_of_heap_reserve__", Value: 0x100000, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xt_start__", Value: 0x58, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__subsystem__", Value: 0x3, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_amsg_exit", Value: 0x1ba0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_fmode", Value: 0x630, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__security_cookie_complement", Value: 0x190, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_TlsGetValue", Value: 0x284, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "GetCurrentProcess", Value: 0x1ca0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__setusermatherr", Value: 0x1ba8, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_fprintf", Value: 0x344, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_VirtualProtect", Value: 0x294, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__xl_c", Value: 0x40, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___tls_end__", Value: 0x68, SectionNumber: 9, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__onexitend", Value: 0x978, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "QueryPerformanceCounter", Value: 0x1c58, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_VirtualQuery", Value: 0x29c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__initterm", Value: 0x30c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_initltsdyn_force", Value: 0x5f4, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_dowildcard", Value: 0x40, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___iob_func", Value: 0x2cc, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__dyn_tls_init_callback", Value: 0x10, SectionNumber: 3, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_initterm", Value: 0x1b90, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_newmode", Value: 0x5e0, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "fwrite", Value: 0x1b48, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_strncmp", Value: 0x37c, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__major_image_version__", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__loader_flags__", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___chkstk_ms", Value: 0x1ad0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__native_startup_lock", Value: 0x988, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__mingw_winmain_hInstance", Value: 0x968, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "GetStartupInfoA", Value: 0x1c80, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "GetCurrentThreadId", Value: 0x1c90, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_onexit", Value: 0x1b80, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__rt_psrelocs_end", Value: 0x5e0, SectionNumber: 3, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "exit", Value: 0x1b60, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__get_invalid_parameter_handler", Value: 0x170, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__minor_subsystem_version__", Value: 0x2, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__minor_image_version__", Value: 0x0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp__unlock", Value: 0x324, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp___set_app_type", Value: 0x2dc, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "mingw_initltsdrot_force", Value: 0x5f8, SectionNumber: 6, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "_charmax", Value: 0x140, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "strlen", Value: 0x1b28, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "DeleteCriticalSection", Value: 0x1cb0, SectionNumber: 1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_RtlCaptureContext", Value: 0x254, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__RUNTIME_PSEUDO_RELOC_LIST_END__", Value: 0x4045e0, SectionNumber: -1, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__dllonexit", Value: 0x1bd0, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "_unlock", Value: 0x1b78, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__imp_GetCurrentProcess", Value: 0x1fc, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "___crt_xt_end__", Value: 0x58, SectionNumber: 8, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "__lib64_libmsvcrt_a_iname", Value: 0x7bc, SectionNumber: 7, Type: 0x0, StorageClass: 0x2},
		&pe.Symbol{Name: "vfprintf", Value: 0x1b18, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "free", Value: 0x1b50, SectionNumber: 1, Type: 0x20, StorageClass: 0x2},
		&pe.Symbol{Name: "__security_cookie", Value: 0x180, SectionNumber: 2, Type: 0x0, StorageClass: 0x2},
	},
}
