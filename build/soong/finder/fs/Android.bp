// Copyright 2017 Google Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//
// mock filesystem
//

package {
    default_applicable_licenses: [
        "Android-Apache-2.0",
        "build_soong_finder_fs_license",
    ],
}

license {
    name: "build_soong_finder_fs_license",
    license_kinds: [
        "SPDX-license-identifier-BSD",
    ],
    license_text: ["LICENSE"],
}

bootstrap_go_package {
    name: "soong-finder-fs",
    pkgPath: "android/soong/finder/fs",
    srcs: [
        "fs.go",
        "readdir.go",
        "test.go",
    ],
    testSrcs: [
        "fs_test.go",
        "readdir_test.go",
    ],
    darwin: {
        srcs: [
            "fs_darwin.go",
        ],
    },
    linux: {
        srcs: [
            "fs_linux.go",
        ],
    },
}
