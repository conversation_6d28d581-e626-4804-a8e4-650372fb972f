// Code generated by protoc-gen-go. DO NOT EDIT.
// source: sbox.proto

package sbox_proto

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// A set of commands to run in a sandbox.
type Manifest struct {
	// A list of commands to run in the sandbox.
	Commands []*Command `protobuf:"bytes,1,rep,name=commands" json:"commands,omitempty"`
	// If set, GCC-style dependency files from any command that references __SBOX_DEPFILE__ will be
	// merged into the given output file relative to the $PWD when sbox was started.
	OutputDepfile        *string  `protobuf:"bytes,2,opt,name=output_depfile,json=outputDepfile" json:"output_depfile,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Manifest) Reset()         { *m = Manifest{} }
func (m *Manifest) String() string { return proto.CompactTextString(m) }
func (*Manifest) ProtoMessage()    {}
func (*Manifest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d0425bf0de86ed1, []int{0}
}

func (m *Manifest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Manifest.Unmarshal(m, b)
}
func (m *Manifest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Manifest.Marshal(b, m, deterministic)
}
func (m *Manifest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Manifest.Merge(m, src)
}
func (m *Manifest) XXX_Size() int {
	return xxx_messageInfo_Manifest.Size(m)
}
func (m *Manifest) XXX_DiscardUnknown() {
	xxx_messageInfo_Manifest.DiscardUnknown(m)
}

var xxx_messageInfo_Manifest proto.InternalMessageInfo

func (m *Manifest) GetCommands() []*Command {
	if m != nil {
		return m.Commands
	}
	return nil
}

func (m *Manifest) GetOutputDepfile() string {
	if m != nil && m.OutputDepfile != nil {
		return *m.OutputDepfile
	}
	return ""
}

// SandboxManifest describes a command to run in the sandbox.
type Command struct {
	// A list of copy rules to run before the sandboxed command.  The from field is relative to the
	// $PWD when sbox was run, the to field is relative to the top of the temporary sandbox directory.
	CopyBefore []*Copy `protobuf:"bytes,1,rep,name=copy_before,json=copyBefore" json:"copy_before,omitempty"`
	// If true, change the working directory to the top of the temporary sandbox directory before
	// running the command.  If false, leave the working directory where it was when sbox was started.
	Chdir *bool `protobuf:"varint,2,opt,name=chdir" json:"chdir,omitempty"`
	// The command to run.
	Command *string `protobuf:"bytes,3,req,name=command" json:"command,omitempty"`
	// A list of copy rules to run after the sandboxed command.  The from field is relative to the
	// top of the temporary sandbox directory, the to field is relative to the $PWD when sbox was run.
	CopyAfter []*Copy `protobuf:"bytes,4,rep,name=copy_after,json=copyAfter" json:"copy_after,omitempty"`
	// An optional hash of the input files to ensure the textproto files and the sbox rule reruns
	// when the lists of inputs changes, even if the inputs are not on the command line.
	InputHash *string `protobuf:"bytes,5,opt,name=input_hash,json=inputHash" json:"input_hash,omitempty"`
	// A list of files that will be copied before the sandboxed command, and whose contents should be
	// copied as if they were listed in copy_before.
	RspFiles             []*RspFile `protobuf:"bytes,6,rep,name=rsp_files,json=rspFiles" json:"rsp_files,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *Command) Reset()         { *m = Command{} }
func (m *Command) String() string { return proto.CompactTextString(m) }
func (*Command) ProtoMessage()    {}
func (*Command) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d0425bf0de86ed1, []int{1}
}

func (m *Command) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Command.Unmarshal(m, b)
}
func (m *Command) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Command.Marshal(b, m, deterministic)
}
func (m *Command) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Command.Merge(m, src)
}
func (m *Command) XXX_Size() int {
	return xxx_messageInfo_Command.Size(m)
}
func (m *Command) XXX_DiscardUnknown() {
	xxx_messageInfo_Command.DiscardUnknown(m)
}

var xxx_messageInfo_Command proto.InternalMessageInfo

func (m *Command) GetCopyBefore() []*Copy {
	if m != nil {
		return m.CopyBefore
	}
	return nil
}

func (m *Command) GetChdir() bool {
	if m != nil && m.Chdir != nil {
		return *m.Chdir
	}
	return false
}

func (m *Command) GetCommand() string {
	if m != nil && m.Command != nil {
		return *m.Command
	}
	return ""
}

func (m *Command) GetCopyAfter() []*Copy {
	if m != nil {
		return m.CopyAfter
	}
	return nil
}

func (m *Command) GetInputHash() string {
	if m != nil && m.InputHash != nil {
		return *m.InputHash
	}
	return ""
}

func (m *Command) GetRspFiles() []*RspFile {
	if m != nil {
		return m.RspFiles
	}
	return nil
}

// Copy describes a from-to pair of files to copy.  The paths may be relative, the root that they
// are relative to is specific to the context the Copy is used in and will be different for
// from and to.
type Copy struct {
	From *string `protobuf:"bytes,1,req,name=from" json:"from,omitempty"`
	To   *string `protobuf:"bytes,2,req,name=to" json:"to,omitempty"`
	// If true, make the file executable after copying it.
	Executable           *bool    `protobuf:"varint,3,opt,name=executable" json:"executable,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Copy) Reset()         { *m = Copy{} }
func (m *Copy) String() string { return proto.CompactTextString(m) }
func (*Copy) ProtoMessage()    {}
func (*Copy) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d0425bf0de86ed1, []int{2}
}

func (m *Copy) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Copy.Unmarshal(m, b)
}
func (m *Copy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Copy.Marshal(b, m, deterministic)
}
func (m *Copy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Copy.Merge(m, src)
}
func (m *Copy) XXX_Size() int {
	return xxx_messageInfo_Copy.Size(m)
}
func (m *Copy) XXX_DiscardUnknown() {
	xxx_messageInfo_Copy.DiscardUnknown(m)
}

var xxx_messageInfo_Copy proto.InternalMessageInfo

func (m *Copy) GetFrom() string {
	if m != nil && m.From != nil {
		return *m.From
	}
	return ""
}

func (m *Copy) GetTo() string {
	if m != nil && m.To != nil {
		return *m.To
	}
	return ""
}

func (m *Copy) GetExecutable() bool {
	if m != nil && m.Executable != nil {
		return *m.Executable
	}
	return false
}

// RspFile describes an rspfile that should be copied into the sandbox directory.
type RspFile struct {
	// The path to the rsp file.
	File *string `protobuf:"bytes,1,req,name=file" json:"file,omitempty"`
	// A list of path mappings that should be applied to each file listed in the rsp file.
	PathMappings         []*PathMapping `protobuf:"bytes,2,rep,name=path_mappings,json=pathMappings" json:"path_mappings,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RspFile) Reset()         { *m = RspFile{} }
func (m *RspFile) String() string { return proto.CompactTextString(m) }
func (*RspFile) ProtoMessage()    {}
func (*RspFile) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d0425bf0de86ed1, []int{3}
}

func (m *RspFile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RspFile.Unmarshal(m, b)
}
func (m *RspFile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RspFile.Marshal(b, m, deterministic)
}
func (m *RspFile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RspFile.Merge(m, src)
}
func (m *RspFile) XXX_Size() int {
	return xxx_messageInfo_RspFile.Size(m)
}
func (m *RspFile) XXX_DiscardUnknown() {
	xxx_messageInfo_RspFile.DiscardUnknown(m)
}

var xxx_messageInfo_RspFile proto.InternalMessageInfo

func (m *RspFile) GetFile() string {
	if m != nil && m.File != nil {
		return *m.File
	}
	return ""
}

func (m *RspFile) GetPathMappings() []*PathMapping {
	if m != nil {
		return m.PathMappings
	}
	return nil
}

// PathMapping describes a mapping from a path outside the sandbox to the path inside the sandbox.
type PathMapping struct {
	From                 *string  `protobuf:"bytes,1,req,name=from" json:"from,omitempty"`
	To                   *string  `protobuf:"bytes,2,req,name=to" json:"to,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PathMapping) Reset()         { *m = PathMapping{} }
func (m *PathMapping) String() string { return proto.CompactTextString(m) }
func (*PathMapping) ProtoMessage()    {}
func (*PathMapping) Descriptor() ([]byte, []int) {
	return fileDescriptor_9d0425bf0de86ed1, []int{4}
}

func (m *PathMapping) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PathMapping.Unmarshal(m, b)
}
func (m *PathMapping) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PathMapping.Marshal(b, m, deterministic)
}
func (m *PathMapping) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PathMapping.Merge(m, src)
}
func (m *PathMapping) XXX_Size() int {
	return xxx_messageInfo_PathMapping.Size(m)
}
func (m *PathMapping) XXX_DiscardUnknown() {
	xxx_messageInfo_PathMapping.DiscardUnknown(m)
}

var xxx_messageInfo_PathMapping proto.InternalMessageInfo

func (m *PathMapping) GetFrom() string {
	if m != nil && m.From != nil {
		return *m.From
	}
	return ""
}

func (m *PathMapping) GetTo() string {
	if m != nil && m.To != nil {
		return *m.To
	}
	return ""
}

func init() {
	proto.RegisterType((*Manifest)(nil), "sbox.Manifest")
	proto.RegisterType((*Command)(nil), "sbox.Command")
	proto.RegisterType((*Copy)(nil), "sbox.Copy")
	proto.RegisterType((*RspFile)(nil), "sbox.RspFile")
	proto.RegisterType((*PathMapping)(nil), "sbox.PathMapping")
}

func init() {
	proto.RegisterFile("sbox.proto", fileDescriptor_9d0425bf0de86ed1)
}

var fileDescriptor_9d0425bf0de86ed1 = []byte{
	// 342 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x92, 0x5f, 0x4b, 0xc3, 0x30,
	0x14, 0xc5, 0x69, 0xd7, 0xb9, 0xf6, 0xee, 0x0f, 0x18, 0x7c, 0xc8, 0x8b, 0x32, 0x0a, 0xc2, 0xa6,
	0x30, 0xd0, 0x07, 0xdf, 0x9d, 0x22, 0x22, 0x0c, 0x24, 0xe0, 0x8b, 0x08, 0x25, 0xeb, 0x52, 0x5b,
	0x58, 0x9b, 0x90, 0x64, 0xb0, 0x7d, 0x57, 0x3f, 0x8c, 0xe4, 0xa6, 0xd3, 0x82, 0x2f, 0xbe, 0xdd,
	0x7b, 0x0e, 0xf7, 0xdc, 0x5f, 0xc2, 0x05, 0x30, 0x6b, 0xb9, 0x5f, 0x28, 0x2d, 0xad, 0x24, 0x91,
	0xab, 0xd3, 0x0f, 0x88, 0x57, 0xbc, 0xa9, 0x0a, 0x61, 0x2c, 0x99, 0x43, 0x9c, 0xcb, 0xba, 0xe6,
	0xcd, 0xc6, 0xd0, 0x60, 0xda, 0x9b, 0x0d, 0x6f, 0xc7, 0x0b, 0x1c, 0x78, 0xf0, 0x2a, 0xfb, 0xb1,
	0xc9, 0x25, 0x4c, 0xe4, 0xce, 0xaa, 0x9d, 0xcd, 0x36, 0x42, 0x15, 0xd5, 0x56, 0xd0, 0x70, 0x1a,
	0xcc, 0x12, 0x36, 0xf6, 0xea, 0xa3, 0x17, 0xd3, 0xaf, 0x00, 0x06, 0xed, 0x30, 0xb9, 0x86, 0x61,
	0x2e, 0xd5, 0x21, 0x5b, 0x8b, 0x42, 0x6a, 0xd1, 0x2e, 0x80, 0xe3, 0x02, 0x75, 0x60, 0xe0, 0xec,
	0x25, 0xba, 0xe4, 0x0c, 0xfa, 0x79, 0xb9, 0xa9, 0x34, 0xc6, 0xc6, 0xcc, 0x37, 0x84, 0xc2, 0xa0,
	0x25, 0xa0, 0xbd, 0x69, 0x38, 0x4b, 0xd8, 0xb1, 0x25, 0x73, 0xc0, 0xe9, 0x8c, 0x17, 0x56, 0x68,
	0x1a, 0xfd, 0xc9, 0x4e, 0x9c, 0x7b, 0xef, 0x4c, 0x72, 0x0e, 0x50, 0x35, 0x8e, 0xbc, 0xe4, 0xa6,
	0xa4, 0x7d, 0xc4, 0x4e, 0x50, 0x79, 0xe6, 0xa6, 0x24, 0x57, 0x90, 0x68, 0xa3, 0x32, 0x87, 0x6f,
	0xe8, 0x49, 0xf7, 0x17, 0x98, 0x51, 0x4f, 0xd5, 0x56, 0xb0, 0x58, 0xfb, 0xc2, 0xa4, 0x2f, 0x10,
	0xb9, 0x74, 0x42, 0x20, 0x2a, 0xb4, 0xac, 0x69, 0x80, 0x50, 0x58, 0x93, 0x09, 0x84, 0x56, 0xd2,
	0x10, 0x95, 0xd0, 0x4a, 0x72, 0x01, 0x20, 0xf6, 0x22, 0xdf, 0x59, 0xbe, 0xde, 0x0a, 0xda, 0xc3,
	0x67, 0x75, 0x94, 0xf4, 0x0d, 0x06, 0xed, 0x02, 0x8c, 0x73, 0x5f, 0x7a, 0x8c, 0x73, 0xda, 0x1d,
	0x8c, 0x15, 0xb7, 0x65, 0x56, 0x73, 0xa5, 0xaa, 0xe6, 0xd3, 0xd0, 0x10, 0xd1, 0x4e, 0x3d, 0xda,
	0x2b, 0xb7, 0xe5, 0xca, 0x3b, 0x6c, 0xa4, 0x7e, 0x1b, 0x93, 0xde, 0xc0, 0xb0, 0x63, 0xfe, 0x87,
	0x74, 0x39, 0x7a, 0xc7, 0x33, 0xc9, 0xf0, 0x4c, 0xbe, 0x03, 0x00, 0x00, 0xff, 0xff, 0x83, 0x82,
	0xb0, 0xc3, 0x33, 0x02, 0x00, 0x00,
}
