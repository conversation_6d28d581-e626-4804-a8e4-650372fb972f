// Code generated by protoc-gen-go. DO NOT EDIT.
// source: targeting.proto

package android_bundle_proto

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ScreenDensity_DensityAlias int32

const (
	ScreenDensity_DENSITY_UNSPECIFIED ScreenDensity_DensityAlias = 0
	ScreenDensity_NODPI               ScreenDensity_DensityAlias = 1
	ScreenDensity_LDPI                ScreenDensity_DensityAlias = 2
	ScreenDensity_MDPI                ScreenDensity_DensityAlias = 3
	ScreenDensity_TVDPI               ScreenDensity_DensityAlias = 4
	ScreenDensity_HDPI                ScreenDensity_DensityAlias = 5
	ScreenDensity_XHDPI               ScreenDensity_DensityAlias = 6
	ScreenDensity_XXHDPI              ScreenDensity_DensityAlias = 7
	ScreenDensity_XXXHDPI             ScreenDensity_DensityAlias = 8
)

var ScreenDensity_DensityAlias_name = map[int32]string{
	0: "DENSITY_UNSPECIFIED",
	1: "NODPI",
	2: "LDPI",
	3: "MDPI",
	4: "TVDPI",
	5: "HDPI",
	6: "XHDPI",
	7: "XXHDPI",
	8: "XXXHDPI",
}

var ScreenDensity_DensityAlias_value = map[string]int32{
	"DENSITY_UNSPECIFIED": 0,
	"NODPI":               1,
	"LDPI":                2,
	"MDPI":                3,
	"TVDPI":               4,
	"HDPI":                5,
	"XHDPI":               6,
	"XXHDPI":              7,
	"XXXHDPI":             8,
}

func (x ScreenDensity_DensityAlias) String() string {
	return proto.EnumName(ScreenDensity_DensityAlias_name, int32(x))
}

func (ScreenDensity_DensityAlias) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{4, 0}
}

type TextureCompressionFormat_TextureCompressionFormatAlias int32

const (
	TextureCompressionFormat_UNSPECIFIED_TEXTURE_COMPRESSION_FORMAT TextureCompressionFormat_TextureCompressionFormatAlias = 0
	TextureCompressionFormat_ETC1_RGB8                              TextureCompressionFormat_TextureCompressionFormatAlias = 1
	TextureCompressionFormat_PALETTED                               TextureCompressionFormat_TextureCompressionFormatAlias = 2
	TextureCompressionFormat_THREE_DC                               TextureCompressionFormat_TextureCompressionFormatAlias = 3
	TextureCompressionFormat_ATC                                    TextureCompressionFormat_TextureCompressionFormatAlias = 4
	TextureCompressionFormat_LATC                                   TextureCompressionFormat_TextureCompressionFormatAlias = 5
	TextureCompressionFormat_DXT1                                   TextureCompressionFormat_TextureCompressionFormatAlias = 6
	TextureCompressionFormat_S3TC                                   TextureCompressionFormat_TextureCompressionFormatAlias = 7
	TextureCompressionFormat_PVRTC                                  TextureCompressionFormat_TextureCompressionFormatAlias = 8
	TextureCompressionFormat_ASTC                                   TextureCompressionFormat_TextureCompressionFormatAlias = 9
	TextureCompressionFormat_ETC2                                   TextureCompressionFormat_TextureCompressionFormatAlias = 10
)

var TextureCompressionFormat_TextureCompressionFormatAlias_name = map[int32]string{
	0:  "UNSPECIFIED_TEXTURE_COMPRESSION_FORMAT",
	1:  "ETC1_RGB8",
	2:  "PALETTED",
	3:  "THREE_DC",
	4:  "ATC",
	5:  "LATC",
	6:  "DXT1",
	7:  "S3TC",
	8:  "PVRTC",
	9:  "ASTC",
	10: "ETC2",
}

var TextureCompressionFormat_TextureCompressionFormatAlias_value = map[string]int32{
	"UNSPECIFIED_TEXTURE_COMPRESSION_FORMAT": 0,
	"ETC1_RGB8":                              1,
	"PALETTED":                               2,
	"THREE_DC":                               3,
	"ATC":                                    4,
	"LATC":                                   5,
	"DXT1":                                   6,
	"S3TC":                                   7,
	"PVRTC":                                  8,
	"ASTC":                                   9,
	"ETC2":                                   10,
}

func (x TextureCompressionFormat_TextureCompressionFormatAlias) String() string {
	return proto.EnumName(TextureCompressionFormat_TextureCompressionFormatAlias_name, int32(x))
}

func (TextureCompressionFormat_TextureCompressionFormatAlias) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{10, 0}
}

type Abi_AbiAlias int32

const (
	Abi_UNSPECIFIED_CPU_ARCHITECTURE Abi_AbiAlias = 0
	Abi_ARMEABI                      Abi_AbiAlias = 1
	Abi_ARMEABI_V7A                  Abi_AbiAlias = 2
	Abi_ARM64_V8A                    Abi_AbiAlias = 3
	Abi_X86                          Abi_AbiAlias = 4
	Abi_X86_64                       Abi_AbiAlias = 5
	Abi_MIPS                         Abi_AbiAlias = 6
	Abi_MIPS64                       Abi_AbiAlias = 7
)

var Abi_AbiAlias_name = map[int32]string{
	0: "UNSPECIFIED_CPU_ARCHITECTURE",
	1: "ARMEABI",
	2: "ARMEABI_V7A",
	3: "ARM64_V8A",
	4: "X86",
	5: "X86_64",
	6: "MIPS",
	7: "MIPS64",
}

var Abi_AbiAlias_value = map[string]int32{
	"UNSPECIFIED_CPU_ARCHITECTURE": 0,
	"ARMEABI":                      1,
	"ARMEABI_V7A":                  2,
	"ARM64_V8A":                    3,
	"X86":                          4,
	"X86_64":                       5,
	"MIPS":                         6,
	"MIPS64":                       7,
}

func (x Abi_AbiAlias) String() string {
	return proto.EnumName(Abi_AbiAlias_name, int32(x))
}

func (Abi_AbiAlias) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{11, 0}
}

type Sanitizer_SanitizerAlias int32

const (
	Sanitizer_NONE      Sanitizer_SanitizerAlias = 0
	Sanitizer_HWADDRESS Sanitizer_SanitizerAlias = 1
)

var Sanitizer_SanitizerAlias_name = map[int32]string{
	0: "NONE",
	1: "HWADDRESS",
}

var Sanitizer_SanitizerAlias_value = map[string]int32{
	"NONE":      0,
	"HWADDRESS": 1,
}

func (x Sanitizer_SanitizerAlias) String() string {
	return proto.EnumName(Sanitizer_SanitizerAlias_name, int32(x))
}

func (Sanitizer_SanitizerAlias) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{13, 0}
}

// Targeting on the level of variants.
type VariantTargeting struct {
	SdkVersionTargeting               *SdkVersionTargeting               `protobuf:"bytes,1,opt,name=sdk_version_targeting,json=sdkVersionTargeting,proto3" json:"sdk_version_targeting,omitempty"`
	AbiTargeting                      *AbiTargeting                      `protobuf:"bytes,2,opt,name=abi_targeting,json=abiTargeting,proto3" json:"abi_targeting,omitempty"`
	ScreenDensityTargeting            *ScreenDensityTargeting            `protobuf:"bytes,3,opt,name=screen_density_targeting,json=screenDensityTargeting,proto3" json:"screen_density_targeting,omitempty"`
	MultiAbiTargeting                 *MultiAbiTargeting                 `protobuf:"bytes,4,opt,name=multi_abi_targeting,json=multiAbiTargeting,proto3" json:"multi_abi_targeting,omitempty"`
	TextureCompressionFormatTargeting *TextureCompressionFormatTargeting `protobuf:"bytes,5,opt,name=texture_compression_format_targeting,json=textureCompressionFormatTargeting,proto3" json:"texture_compression_format_targeting,omitempty"`
	XXX_NoUnkeyedLiteral              struct{}                           `json:"-"`
	XXX_unrecognized                  []byte                             `json:"-"`
	XXX_sizecache                     int32                              `json:"-"`
}

func (m *VariantTargeting) Reset()         { *m = VariantTargeting{} }
func (m *VariantTargeting) String() string { return proto.CompactTextString(m) }
func (*VariantTargeting) ProtoMessage()    {}
func (*VariantTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{0}
}

func (m *VariantTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VariantTargeting.Unmarshal(m, b)
}
func (m *VariantTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VariantTargeting.Marshal(b, m, deterministic)
}
func (m *VariantTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VariantTargeting.Merge(m, src)
}
func (m *VariantTargeting) XXX_Size() int {
	return xxx_messageInfo_VariantTargeting.Size(m)
}
func (m *VariantTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_VariantTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_VariantTargeting proto.InternalMessageInfo

func (m *VariantTargeting) GetSdkVersionTargeting() *SdkVersionTargeting {
	if m != nil {
		return m.SdkVersionTargeting
	}
	return nil
}

func (m *VariantTargeting) GetAbiTargeting() *AbiTargeting {
	if m != nil {
		return m.AbiTargeting
	}
	return nil
}

func (m *VariantTargeting) GetScreenDensityTargeting() *ScreenDensityTargeting {
	if m != nil {
		return m.ScreenDensityTargeting
	}
	return nil
}

func (m *VariantTargeting) GetMultiAbiTargeting() *MultiAbiTargeting {
	if m != nil {
		return m.MultiAbiTargeting
	}
	return nil
}

func (m *VariantTargeting) GetTextureCompressionFormatTargeting() *TextureCompressionFormatTargeting {
	if m != nil {
		return m.TextureCompressionFormatTargeting
	}
	return nil
}

// Targeting on the level of individual APKs.
type ApkTargeting struct {
	AbiTargeting                      *AbiTargeting                      `protobuf:"bytes,1,opt,name=abi_targeting,json=abiTargeting,proto3" json:"abi_targeting,omitempty"`
	GraphicsApiTargeting              *GraphicsApiTargeting              `protobuf:"bytes,2,opt,name=graphics_api_targeting,json=graphicsApiTargeting,proto3" json:"graphics_api_targeting,omitempty"`
	LanguageTargeting                 *LanguageTargeting                 `protobuf:"bytes,3,opt,name=language_targeting,json=languageTargeting,proto3" json:"language_targeting,omitempty"`
	ScreenDensityTargeting            *ScreenDensityTargeting            `protobuf:"bytes,4,opt,name=screen_density_targeting,json=screenDensityTargeting,proto3" json:"screen_density_targeting,omitempty"`
	SdkVersionTargeting               *SdkVersionTargeting               `protobuf:"bytes,5,opt,name=sdk_version_targeting,json=sdkVersionTargeting,proto3" json:"sdk_version_targeting,omitempty"`
	TextureCompressionFormatTargeting *TextureCompressionFormatTargeting `protobuf:"bytes,6,opt,name=texture_compression_format_targeting,json=textureCompressionFormatTargeting,proto3" json:"texture_compression_format_targeting,omitempty"`
	MultiAbiTargeting                 *MultiAbiTargeting                 `protobuf:"bytes,7,opt,name=multi_abi_targeting,json=multiAbiTargeting,proto3" json:"multi_abi_targeting,omitempty"`
	SanitizerTargeting                *SanitizerTargeting                `protobuf:"bytes,8,opt,name=sanitizer_targeting,json=sanitizerTargeting,proto3" json:"sanitizer_targeting,omitempty"`
	XXX_NoUnkeyedLiteral              struct{}                           `json:"-"`
	XXX_unrecognized                  []byte                             `json:"-"`
	XXX_sizecache                     int32                              `json:"-"`
}

func (m *ApkTargeting) Reset()         { *m = ApkTargeting{} }
func (m *ApkTargeting) String() string { return proto.CompactTextString(m) }
func (*ApkTargeting) ProtoMessage()    {}
func (*ApkTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{1}
}

func (m *ApkTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApkTargeting.Unmarshal(m, b)
}
func (m *ApkTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApkTargeting.Marshal(b, m, deterministic)
}
func (m *ApkTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApkTargeting.Merge(m, src)
}
func (m *ApkTargeting) XXX_Size() int {
	return xxx_messageInfo_ApkTargeting.Size(m)
}
func (m *ApkTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_ApkTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_ApkTargeting proto.InternalMessageInfo

func (m *ApkTargeting) GetAbiTargeting() *AbiTargeting {
	if m != nil {
		return m.AbiTargeting
	}
	return nil
}

func (m *ApkTargeting) GetGraphicsApiTargeting() *GraphicsApiTargeting {
	if m != nil {
		return m.GraphicsApiTargeting
	}
	return nil
}

func (m *ApkTargeting) GetLanguageTargeting() *LanguageTargeting {
	if m != nil {
		return m.LanguageTargeting
	}
	return nil
}

func (m *ApkTargeting) GetScreenDensityTargeting() *ScreenDensityTargeting {
	if m != nil {
		return m.ScreenDensityTargeting
	}
	return nil
}

func (m *ApkTargeting) GetSdkVersionTargeting() *SdkVersionTargeting {
	if m != nil {
		return m.SdkVersionTargeting
	}
	return nil
}

func (m *ApkTargeting) GetTextureCompressionFormatTargeting() *TextureCompressionFormatTargeting {
	if m != nil {
		return m.TextureCompressionFormatTargeting
	}
	return nil
}

func (m *ApkTargeting) GetMultiAbiTargeting() *MultiAbiTargeting {
	if m != nil {
		return m.MultiAbiTargeting
	}
	return nil
}

func (m *ApkTargeting) GetSanitizerTargeting() *SanitizerTargeting {
	if m != nil {
		return m.SanitizerTargeting
	}
	return nil
}

// Targeting on the module level.
// The semantic of the targeting is the "AND" rule on all immediate values.
type ModuleTargeting struct {
	SdkVersionTargeting    *SdkVersionTargeting      `protobuf:"bytes,1,opt,name=sdk_version_targeting,json=sdkVersionTargeting,proto3" json:"sdk_version_targeting,omitempty"`
	DeviceFeatureTargeting []*DeviceFeatureTargeting `protobuf:"bytes,2,rep,name=device_feature_targeting,json=deviceFeatureTargeting,proto3" json:"device_feature_targeting,omitempty"`
	UserCountriesTargeting *UserCountriesTargeting   `protobuf:"bytes,3,opt,name=user_countries_targeting,json=userCountriesTargeting,proto3" json:"user_countries_targeting,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                  `json:"-"`
	XXX_unrecognized       []byte                    `json:"-"`
	XXX_sizecache          int32                     `json:"-"`
}

func (m *ModuleTargeting) Reset()         { *m = ModuleTargeting{} }
func (m *ModuleTargeting) String() string { return proto.CompactTextString(m) }
func (*ModuleTargeting) ProtoMessage()    {}
func (*ModuleTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{2}
}

func (m *ModuleTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleTargeting.Unmarshal(m, b)
}
func (m *ModuleTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleTargeting.Marshal(b, m, deterministic)
}
func (m *ModuleTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleTargeting.Merge(m, src)
}
func (m *ModuleTargeting) XXX_Size() int {
	return xxx_messageInfo_ModuleTargeting.Size(m)
}
func (m *ModuleTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleTargeting proto.InternalMessageInfo

func (m *ModuleTargeting) GetSdkVersionTargeting() *SdkVersionTargeting {
	if m != nil {
		return m.SdkVersionTargeting
	}
	return nil
}

func (m *ModuleTargeting) GetDeviceFeatureTargeting() []*DeviceFeatureTargeting {
	if m != nil {
		return m.DeviceFeatureTargeting
	}
	return nil
}

func (m *ModuleTargeting) GetUserCountriesTargeting() *UserCountriesTargeting {
	if m != nil {
		return m.UserCountriesTargeting
	}
	return nil
}

// User Countries targeting describing an inclusive/exclusive list of country
// codes that module targets.
type UserCountriesTargeting struct {
	// List of country codes in the two-letter CLDR territory format.
	CountryCodes []string `protobuf:"bytes,1,rep,name=country_codes,json=countryCodes,proto3" json:"country_codes,omitempty"`
	// Indicates if the list above is exclusive.
	Exclude              bool     `protobuf:"varint,2,opt,name=exclude,proto3" json:"exclude,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserCountriesTargeting) Reset()         { *m = UserCountriesTargeting{} }
func (m *UserCountriesTargeting) String() string { return proto.CompactTextString(m) }
func (*UserCountriesTargeting) ProtoMessage()    {}
func (*UserCountriesTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{3}
}

func (m *UserCountriesTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCountriesTargeting.Unmarshal(m, b)
}
func (m *UserCountriesTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCountriesTargeting.Marshal(b, m, deterministic)
}
func (m *UserCountriesTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCountriesTargeting.Merge(m, src)
}
func (m *UserCountriesTargeting) XXX_Size() int {
	return xxx_messageInfo_UserCountriesTargeting.Size(m)
}
func (m *UserCountriesTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCountriesTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_UserCountriesTargeting proto.InternalMessageInfo

func (m *UserCountriesTargeting) GetCountryCodes() []string {
	if m != nil {
		return m.CountryCodes
	}
	return nil
}

func (m *UserCountriesTargeting) GetExclude() bool {
	if m != nil {
		return m.Exclude
	}
	return false
}

type ScreenDensity struct {
	// Types that are valid to be assigned to DensityOneof:
	//	*ScreenDensity_DensityAlias_
	//	*ScreenDensity_DensityDpi
	DensityOneof         isScreenDensity_DensityOneof `protobuf_oneof:"density_oneof"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ScreenDensity) Reset()         { *m = ScreenDensity{} }
func (m *ScreenDensity) String() string { return proto.CompactTextString(m) }
func (*ScreenDensity) ProtoMessage()    {}
func (*ScreenDensity) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{4}
}

func (m *ScreenDensity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScreenDensity.Unmarshal(m, b)
}
func (m *ScreenDensity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScreenDensity.Marshal(b, m, deterministic)
}
func (m *ScreenDensity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScreenDensity.Merge(m, src)
}
func (m *ScreenDensity) XXX_Size() int {
	return xxx_messageInfo_ScreenDensity.Size(m)
}
func (m *ScreenDensity) XXX_DiscardUnknown() {
	xxx_messageInfo_ScreenDensity.DiscardUnknown(m)
}

var xxx_messageInfo_ScreenDensity proto.InternalMessageInfo

type isScreenDensity_DensityOneof interface {
	isScreenDensity_DensityOneof()
}

type ScreenDensity_DensityAlias_ struct {
	DensityAlias ScreenDensity_DensityAlias `protobuf:"varint,1,opt,name=density_alias,json=densityAlias,proto3,enum=android.bundle.ScreenDensity_DensityAlias,oneof"`
}

type ScreenDensity_DensityDpi struct {
	DensityDpi int32 `protobuf:"varint,2,opt,name=density_dpi,json=densityDpi,proto3,oneof"`
}

func (*ScreenDensity_DensityAlias_) isScreenDensity_DensityOneof() {}

func (*ScreenDensity_DensityDpi) isScreenDensity_DensityOneof() {}

func (m *ScreenDensity) GetDensityOneof() isScreenDensity_DensityOneof {
	if m != nil {
		return m.DensityOneof
	}
	return nil
}

func (m *ScreenDensity) GetDensityAlias() ScreenDensity_DensityAlias {
	if x, ok := m.GetDensityOneof().(*ScreenDensity_DensityAlias_); ok {
		return x.DensityAlias
	}
	return ScreenDensity_DENSITY_UNSPECIFIED
}

func (m *ScreenDensity) GetDensityDpi() int32 {
	if x, ok := m.GetDensityOneof().(*ScreenDensity_DensityDpi); ok {
		return x.DensityDpi
	}
	return 0
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*ScreenDensity) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*ScreenDensity_DensityAlias_)(nil),
		(*ScreenDensity_DensityDpi)(nil),
	}
}

// Wrapper message for `int32`.
//
// The JSON representation for `Int32Value` is JSON number.
type Int32Value struct {
	// The int32 value.
	Value                int32    `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Int32Value) Reset()         { *m = Int32Value{} }
func (m *Int32Value) String() string { return proto.CompactTextString(m) }
func (*Int32Value) ProtoMessage()    {}
func (*Int32Value) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{5}
}

func (m *Int32Value) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Int32Value.Unmarshal(m, b)
}
func (m *Int32Value) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Int32Value.Marshal(b, m, deterministic)
}
func (m *Int32Value) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Int32Value.Merge(m, src)
}
func (m *Int32Value) XXX_Size() int {
	return xxx_messageInfo_Int32Value.Size(m)
}
func (m *Int32Value) XXX_DiscardUnknown() {
	xxx_messageInfo_Int32Value.DiscardUnknown(m)
}

var xxx_messageInfo_Int32Value proto.InternalMessageInfo

func (m *Int32Value) GetValue() int32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type SdkVersion struct {
	// Inclusive.
	Min                  *Int32Value `protobuf:"bytes,1,opt,name=min,proto3" json:"min,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SdkVersion) Reset()         { *m = SdkVersion{} }
func (m *SdkVersion) String() string { return proto.CompactTextString(m) }
func (*SdkVersion) ProtoMessage()    {}
func (*SdkVersion) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{6}
}

func (m *SdkVersion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SdkVersion.Unmarshal(m, b)
}
func (m *SdkVersion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SdkVersion.Marshal(b, m, deterministic)
}
func (m *SdkVersion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SdkVersion.Merge(m, src)
}
func (m *SdkVersion) XXX_Size() int {
	return xxx_messageInfo_SdkVersion.Size(m)
}
func (m *SdkVersion) XXX_DiscardUnknown() {
	xxx_messageInfo_SdkVersion.DiscardUnknown(m)
}

var xxx_messageInfo_SdkVersion proto.InternalMessageInfo

func (m *SdkVersion) GetMin() *Int32Value {
	if m != nil {
		return m.Min
	}
	return nil
}

type GraphicsApi struct {
	// Types that are valid to be assigned to ApiOneof:
	//	*GraphicsApi_MinOpenGlVersion
	//	*GraphicsApi_MinVulkanVersion
	ApiOneof             isGraphicsApi_ApiOneof `protobuf_oneof:"api_oneof"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GraphicsApi) Reset()         { *m = GraphicsApi{} }
func (m *GraphicsApi) String() string { return proto.CompactTextString(m) }
func (*GraphicsApi) ProtoMessage()    {}
func (*GraphicsApi) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{7}
}

func (m *GraphicsApi) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GraphicsApi.Unmarshal(m, b)
}
func (m *GraphicsApi) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GraphicsApi.Marshal(b, m, deterministic)
}
func (m *GraphicsApi) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GraphicsApi.Merge(m, src)
}
func (m *GraphicsApi) XXX_Size() int {
	return xxx_messageInfo_GraphicsApi.Size(m)
}
func (m *GraphicsApi) XXX_DiscardUnknown() {
	xxx_messageInfo_GraphicsApi.DiscardUnknown(m)
}

var xxx_messageInfo_GraphicsApi proto.InternalMessageInfo

type isGraphicsApi_ApiOneof interface {
	isGraphicsApi_ApiOneof()
}

type GraphicsApi_MinOpenGlVersion struct {
	MinOpenGlVersion *OpenGlVersion `protobuf:"bytes,1,opt,name=min_open_gl_version,json=minOpenGlVersion,proto3,oneof"`
}

type GraphicsApi_MinVulkanVersion struct {
	MinVulkanVersion *VulkanVersion `protobuf:"bytes,2,opt,name=min_vulkan_version,json=minVulkanVersion,proto3,oneof"`
}

func (*GraphicsApi_MinOpenGlVersion) isGraphicsApi_ApiOneof() {}

func (*GraphicsApi_MinVulkanVersion) isGraphicsApi_ApiOneof() {}

func (m *GraphicsApi) GetApiOneof() isGraphicsApi_ApiOneof {
	if m != nil {
		return m.ApiOneof
	}
	return nil
}

func (m *GraphicsApi) GetMinOpenGlVersion() *OpenGlVersion {
	if x, ok := m.GetApiOneof().(*GraphicsApi_MinOpenGlVersion); ok {
		return x.MinOpenGlVersion
	}
	return nil
}

func (m *GraphicsApi) GetMinVulkanVersion() *VulkanVersion {
	if x, ok := m.GetApiOneof().(*GraphicsApi_MinVulkanVersion); ok {
		return x.MinVulkanVersion
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*GraphicsApi) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*GraphicsApi_MinOpenGlVersion)(nil),
		(*GraphicsApi_MinVulkanVersion)(nil),
	}
}

type VulkanVersion struct {
	Major                int32    `protobuf:"varint,1,opt,name=major,proto3" json:"major,omitempty"`
	Minor                int32    `protobuf:"varint,2,opt,name=minor,proto3" json:"minor,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VulkanVersion) Reset()         { *m = VulkanVersion{} }
func (m *VulkanVersion) String() string { return proto.CompactTextString(m) }
func (*VulkanVersion) ProtoMessage()    {}
func (*VulkanVersion) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{8}
}

func (m *VulkanVersion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VulkanVersion.Unmarshal(m, b)
}
func (m *VulkanVersion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VulkanVersion.Marshal(b, m, deterministic)
}
func (m *VulkanVersion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VulkanVersion.Merge(m, src)
}
func (m *VulkanVersion) XXX_Size() int {
	return xxx_messageInfo_VulkanVersion.Size(m)
}
func (m *VulkanVersion) XXX_DiscardUnknown() {
	xxx_messageInfo_VulkanVersion.DiscardUnknown(m)
}

var xxx_messageInfo_VulkanVersion proto.InternalMessageInfo

func (m *VulkanVersion) GetMajor() int32 {
	if m != nil {
		return m.Major
	}
	return 0
}

func (m *VulkanVersion) GetMinor() int32 {
	if m != nil {
		return m.Minor
	}
	return 0
}

type OpenGlVersion struct {
	// e.g. OpenGL ES 3.2 is represented as { major: 3, minor: 2 }
	Major                int32    `protobuf:"varint,1,opt,name=major,proto3" json:"major,omitempty"`
	Minor                int32    `protobuf:"varint,2,opt,name=minor,proto3" json:"minor,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenGlVersion) Reset()         { *m = OpenGlVersion{} }
func (m *OpenGlVersion) String() string { return proto.CompactTextString(m) }
func (*OpenGlVersion) ProtoMessage()    {}
func (*OpenGlVersion) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{9}
}

func (m *OpenGlVersion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenGlVersion.Unmarshal(m, b)
}
func (m *OpenGlVersion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenGlVersion.Marshal(b, m, deterministic)
}
func (m *OpenGlVersion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenGlVersion.Merge(m, src)
}
func (m *OpenGlVersion) XXX_Size() int {
	return xxx_messageInfo_OpenGlVersion.Size(m)
}
func (m *OpenGlVersion) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenGlVersion.DiscardUnknown(m)
}

var xxx_messageInfo_OpenGlVersion proto.InternalMessageInfo

func (m *OpenGlVersion) GetMajor() int32 {
	if m != nil {
		return m.Major
	}
	return 0
}

func (m *OpenGlVersion) GetMinor() int32 {
	if m != nil {
		return m.Minor
	}
	return 0
}

type TextureCompressionFormat struct {
	Alias                TextureCompressionFormat_TextureCompressionFormatAlias `protobuf:"varint,1,opt,name=alias,proto3,enum=android.bundle.TextureCompressionFormat_TextureCompressionFormatAlias" json:"alias,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                               `json:"-"`
	XXX_unrecognized     []byte                                                 `json:"-"`
	XXX_sizecache        int32                                                  `json:"-"`
}

func (m *TextureCompressionFormat) Reset()         { *m = TextureCompressionFormat{} }
func (m *TextureCompressionFormat) String() string { return proto.CompactTextString(m) }
func (*TextureCompressionFormat) ProtoMessage()    {}
func (*TextureCompressionFormat) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{10}
}

func (m *TextureCompressionFormat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TextureCompressionFormat.Unmarshal(m, b)
}
func (m *TextureCompressionFormat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TextureCompressionFormat.Marshal(b, m, deterministic)
}
func (m *TextureCompressionFormat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TextureCompressionFormat.Merge(m, src)
}
func (m *TextureCompressionFormat) XXX_Size() int {
	return xxx_messageInfo_TextureCompressionFormat.Size(m)
}
func (m *TextureCompressionFormat) XXX_DiscardUnknown() {
	xxx_messageInfo_TextureCompressionFormat.DiscardUnknown(m)
}

var xxx_messageInfo_TextureCompressionFormat proto.InternalMessageInfo

func (m *TextureCompressionFormat) GetAlias() TextureCompressionFormat_TextureCompressionFormatAlias {
	if m != nil {
		return m.Alias
	}
	return TextureCompressionFormat_UNSPECIFIED_TEXTURE_COMPRESSION_FORMAT
}

type Abi struct {
	Alias                Abi_AbiAlias `protobuf:"varint,1,opt,name=alias,proto3,enum=android.bundle.Abi_AbiAlias" json:"alias,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *Abi) Reset()         { *m = Abi{} }
func (m *Abi) String() string { return proto.CompactTextString(m) }
func (*Abi) ProtoMessage()    {}
func (*Abi) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{11}
}

func (m *Abi) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Abi.Unmarshal(m, b)
}
func (m *Abi) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Abi.Marshal(b, m, deterministic)
}
func (m *Abi) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Abi.Merge(m, src)
}
func (m *Abi) XXX_Size() int {
	return xxx_messageInfo_Abi.Size(m)
}
func (m *Abi) XXX_DiscardUnknown() {
	xxx_messageInfo_Abi.DiscardUnknown(m)
}

var xxx_messageInfo_Abi proto.InternalMessageInfo

func (m *Abi) GetAlias() Abi_AbiAlias {
	if m != nil {
		return m.Alias
	}
	return Abi_UNSPECIFIED_CPU_ARCHITECTURE
}

type MultiAbi struct {
	Abi                  []*Abi   `protobuf:"bytes,1,rep,name=abi,proto3" json:"abi,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiAbi) Reset()         { *m = MultiAbi{} }
func (m *MultiAbi) String() string { return proto.CompactTextString(m) }
func (*MultiAbi) ProtoMessage()    {}
func (*MultiAbi) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{12}
}

func (m *MultiAbi) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiAbi.Unmarshal(m, b)
}
func (m *MultiAbi) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiAbi.Marshal(b, m, deterministic)
}
func (m *MultiAbi) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiAbi.Merge(m, src)
}
func (m *MultiAbi) XXX_Size() int {
	return xxx_messageInfo_MultiAbi.Size(m)
}
func (m *MultiAbi) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiAbi.DiscardUnknown(m)
}

var xxx_messageInfo_MultiAbi proto.InternalMessageInfo

func (m *MultiAbi) GetAbi() []*Abi {
	if m != nil {
		return m.Abi
	}
	return nil
}

type Sanitizer struct {
	Alias                Sanitizer_SanitizerAlias `protobuf:"varint,1,opt,name=alias,proto3,enum=android.bundle.Sanitizer_SanitizerAlias" json:"alias,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *Sanitizer) Reset()         { *m = Sanitizer{} }
func (m *Sanitizer) String() string { return proto.CompactTextString(m) }
func (*Sanitizer) ProtoMessage()    {}
func (*Sanitizer) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{13}
}

func (m *Sanitizer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Sanitizer.Unmarshal(m, b)
}
func (m *Sanitizer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Sanitizer.Marshal(b, m, deterministic)
}
func (m *Sanitizer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Sanitizer.Merge(m, src)
}
func (m *Sanitizer) XXX_Size() int {
	return xxx_messageInfo_Sanitizer.Size(m)
}
func (m *Sanitizer) XXX_DiscardUnknown() {
	xxx_messageInfo_Sanitizer.DiscardUnknown(m)
}

var xxx_messageInfo_Sanitizer proto.InternalMessageInfo

func (m *Sanitizer) GetAlias() Sanitizer_SanitizerAlias {
	if m != nil {
		return m.Alias
	}
	return Sanitizer_NONE
}

type DeviceFeature struct {
	FeatureName string `protobuf:"bytes,1,opt,name=feature_name,json=featureName,proto3" json:"feature_name,omitempty"`
	// Equivalent of android:glEsVersion or android:version in <uses-feature>.
	FeatureVersion       int32    `protobuf:"varint,2,opt,name=feature_version,json=featureVersion,proto3" json:"feature_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeviceFeature) Reset()         { *m = DeviceFeature{} }
func (m *DeviceFeature) String() string { return proto.CompactTextString(m) }
func (*DeviceFeature) ProtoMessage()    {}
func (*DeviceFeature) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{14}
}

func (m *DeviceFeature) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeviceFeature.Unmarshal(m, b)
}
func (m *DeviceFeature) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeviceFeature.Marshal(b, m, deterministic)
}
func (m *DeviceFeature) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeviceFeature.Merge(m, src)
}
func (m *DeviceFeature) XXX_Size() int {
	return xxx_messageInfo_DeviceFeature.Size(m)
}
func (m *DeviceFeature) XXX_DiscardUnknown() {
	xxx_messageInfo_DeviceFeature.DiscardUnknown(m)
}

var xxx_messageInfo_DeviceFeature proto.InternalMessageInfo

func (m *DeviceFeature) GetFeatureName() string {
	if m != nil {
		return m.FeatureName
	}
	return ""
}

func (m *DeviceFeature) GetFeatureVersion() int32 {
	if m != nil {
		return m.FeatureVersion
	}
	return 0
}

// Targeting specific for directories under assets/.
type AssetsDirectoryTargeting struct {
	Abi                      *AbiTargeting                      `protobuf:"bytes,1,opt,name=abi,proto3" json:"abi,omitempty"`
	GraphicsApi              *GraphicsApiTargeting              `protobuf:"bytes,2,opt,name=graphics_api,json=graphicsApi,proto3" json:"graphics_api,omitempty"`
	TextureCompressionFormat *TextureCompressionFormatTargeting `protobuf:"bytes,3,opt,name=texture_compression_format,json=textureCompressionFormat,proto3" json:"texture_compression_format,omitempty"`
	Language                 *LanguageTargeting                 `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}                           `json:"-"`
	XXX_unrecognized         []byte                             `json:"-"`
	XXX_sizecache            int32                              `json:"-"`
}

func (m *AssetsDirectoryTargeting) Reset()         { *m = AssetsDirectoryTargeting{} }
func (m *AssetsDirectoryTargeting) String() string { return proto.CompactTextString(m) }
func (*AssetsDirectoryTargeting) ProtoMessage()    {}
func (*AssetsDirectoryTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{15}
}

func (m *AssetsDirectoryTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssetsDirectoryTargeting.Unmarshal(m, b)
}
func (m *AssetsDirectoryTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssetsDirectoryTargeting.Marshal(b, m, deterministic)
}
func (m *AssetsDirectoryTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssetsDirectoryTargeting.Merge(m, src)
}
func (m *AssetsDirectoryTargeting) XXX_Size() int {
	return xxx_messageInfo_AssetsDirectoryTargeting.Size(m)
}
func (m *AssetsDirectoryTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_AssetsDirectoryTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_AssetsDirectoryTargeting proto.InternalMessageInfo

func (m *AssetsDirectoryTargeting) GetAbi() *AbiTargeting {
	if m != nil {
		return m.Abi
	}
	return nil
}

func (m *AssetsDirectoryTargeting) GetGraphicsApi() *GraphicsApiTargeting {
	if m != nil {
		return m.GraphicsApi
	}
	return nil
}

func (m *AssetsDirectoryTargeting) GetTextureCompressionFormat() *TextureCompressionFormatTargeting {
	if m != nil {
		return m.TextureCompressionFormat
	}
	return nil
}

func (m *AssetsDirectoryTargeting) GetLanguage() *LanguageTargeting {
	if m != nil {
		return m.Language
	}
	return nil
}

// Targeting specific for directories under lib/.
type NativeDirectoryTargeting struct {
	Abi                      *Abi                      `protobuf:"bytes,1,opt,name=abi,proto3" json:"abi,omitempty"`
	GraphicsApi              *GraphicsApi              `protobuf:"bytes,2,opt,name=graphics_api,json=graphicsApi,proto3" json:"graphics_api,omitempty"`
	TextureCompressionFormat *TextureCompressionFormat `protobuf:"bytes,3,opt,name=texture_compression_format,json=textureCompressionFormat,proto3" json:"texture_compression_format,omitempty"`
	Sanitizer                *Sanitizer                `protobuf:"bytes,4,opt,name=sanitizer,proto3" json:"sanitizer,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}                  `json:"-"`
	XXX_unrecognized         []byte                    `json:"-"`
	XXX_sizecache            int32                     `json:"-"`
}

func (m *NativeDirectoryTargeting) Reset()         { *m = NativeDirectoryTargeting{} }
func (m *NativeDirectoryTargeting) String() string { return proto.CompactTextString(m) }
func (*NativeDirectoryTargeting) ProtoMessage()    {}
func (*NativeDirectoryTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{16}
}

func (m *NativeDirectoryTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NativeDirectoryTargeting.Unmarshal(m, b)
}
func (m *NativeDirectoryTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NativeDirectoryTargeting.Marshal(b, m, deterministic)
}
func (m *NativeDirectoryTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NativeDirectoryTargeting.Merge(m, src)
}
func (m *NativeDirectoryTargeting) XXX_Size() int {
	return xxx_messageInfo_NativeDirectoryTargeting.Size(m)
}
func (m *NativeDirectoryTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_NativeDirectoryTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_NativeDirectoryTargeting proto.InternalMessageInfo

func (m *NativeDirectoryTargeting) GetAbi() *Abi {
	if m != nil {
		return m.Abi
	}
	return nil
}

func (m *NativeDirectoryTargeting) GetGraphicsApi() *GraphicsApi {
	if m != nil {
		return m.GraphicsApi
	}
	return nil
}

func (m *NativeDirectoryTargeting) GetTextureCompressionFormat() *TextureCompressionFormat {
	if m != nil {
		return m.TextureCompressionFormat
	}
	return nil
}

func (m *NativeDirectoryTargeting) GetSanitizer() *Sanitizer {
	if m != nil {
		return m.Sanitizer
	}
	return nil
}

// Targeting specific for image files under apex/.
type ApexImageTargeting struct {
	MultiAbi             *MultiAbiTargeting `protobuf:"bytes,1,opt,name=multi_abi,json=multiAbi,proto3" json:"multi_abi,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ApexImageTargeting) Reset()         { *m = ApexImageTargeting{} }
func (m *ApexImageTargeting) String() string { return proto.CompactTextString(m) }
func (*ApexImageTargeting) ProtoMessage()    {}
func (*ApexImageTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{17}
}

func (m *ApexImageTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApexImageTargeting.Unmarshal(m, b)
}
func (m *ApexImageTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApexImageTargeting.Marshal(b, m, deterministic)
}
func (m *ApexImageTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApexImageTargeting.Merge(m, src)
}
func (m *ApexImageTargeting) XXX_Size() int {
	return xxx_messageInfo_ApexImageTargeting.Size(m)
}
func (m *ApexImageTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_ApexImageTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_ApexImageTargeting proto.InternalMessageInfo

func (m *ApexImageTargeting) GetMultiAbi() *MultiAbiTargeting {
	if m != nil {
		return m.MultiAbi
	}
	return nil
}

type AbiTargeting struct {
	Value []*Abi `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
	// Targeting of other sibling directories that were in the Bundle.
	// For master splits this is targeting of other master splits.
	Alternatives         []*Abi   `protobuf:"bytes,2,rep,name=alternatives,proto3" json:"alternatives,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AbiTargeting) Reset()         { *m = AbiTargeting{} }
func (m *AbiTargeting) String() string { return proto.CompactTextString(m) }
func (*AbiTargeting) ProtoMessage()    {}
func (*AbiTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{18}
}

func (m *AbiTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AbiTargeting.Unmarshal(m, b)
}
func (m *AbiTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AbiTargeting.Marshal(b, m, deterministic)
}
func (m *AbiTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AbiTargeting.Merge(m, src)
}
func (m *AbiTargeting) XXX_Size() int {
	return xxx_messageInfo_AbiTargeting.Size(m)
}
func (m *AbiTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_AbiTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_AbiTargeting proto.InternalMessageInfo

func (m *AbiTargeting) GetValue() []*Abi {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *AbiTargeting) GetAlternatives() []*Abi {
	if m != nil {
		return m.Alternatives
	}
	return nil
}

type MultiAbiTargeting struct {
	Value []*MultiAbi `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
	// Targeting of other sibling directories that were in the Bundle.
	// For master splits this is targeting of other master splits.
	Alternatives         []*MultiAbi `protobuf:"bytes,2,rep,name=alternatives,proto3" json:"alternatives,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *MultiAbiTargeting) Reset()         { *m = MultiAbiTargeting{} }
func (m *MultiAbiTargeting) String() string { return proto.CompactTextString(m) }
func (*MultiAbiTargeting) ProtoMessage()    {}
func (*MultiAbiTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{19}
}

func (m *MultiAbiTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiAbiTargeting.Unmarshal(m, b)
}
func (m *MultiAbiTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiAbiTargeting.Marshal(b, m, deterministic)
}
func (m *MultiAbiTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiAbiTargeting.Merge(m, src)
}
func (m *MultiAbiTargeting) XXX_Size() int {
	return xxx_messageInfo_MultiAbiTargeting.Size(m)
}
func (m *MultiAbiTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiAbiTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_MultiAbiTargeting proto.InternalMessageInfo

func (m *MultiAbiTargeting) GetValue() []*MultiAbi {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *MultiAbiTargeting) GetAlternatives() []*MultiAbi {
	if m != nil {
		return m.Alternatives
	}
	return nil
}

type ScreenDensityTargeting struct {
	Value []*ScreenDensity `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
	// Targeting of other sibling directories that were in the Bundle.
	// For master splits this is targeting of other master splits.
	Alternatives         []*ScreenDensity `protobuf:"bytes,2,rep,name=alternatives,proto3" json:"alternatives,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ScreenDensityTargeting) Reset()         { *m = ScreenDensityTargeting{} }
func (m *ScreenDensityTargeting) String() string { return proto.CompactTextString(m) }
func (*ScreenDensityTargeting) ProtoMessage()    {}
func (*ScreenDensityTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{20}
}

func (m *ScreenDensityTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScreenDensityTargeting.Unmarshal(m, b)
}
func (m *ScreenDensityTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScreenDensityTargeting.Marshal(b, m, deterministic)
}
func (m *ScreenDensityTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScreenDensityTargeting.Merge(m, src)
}
func (m *ScreenDensityTargeting) XXX_Size() int {
	return xxx_messageInfo_ScreenDensityTargeting.Size(m)
}
func (m *ScreenDensityTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_ScreenDensityTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_ScreenDensityTargeting proto.InternalMessageInfo

func (m *ScreenDensityTargeting) GetValue() []*ScreenDensity {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *ScreenDensityTargeting) GetAlternatives() []*ScreenDensity {
	if m != nil {
		return m.Alternatives
	}
	return nil
}

type LanguageTargeting struct {
	// ISO-639: 2 or 3 letter language code.
	Value []string `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
	// Targeting of other sibling directories that were in the Bundle.
	// For master splits this is targeting of other master splits.
	Alternatives         []string `protobuf:"bytes,2,rep,name=alternatives,proto3" json:"alternatives,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LanguageTargeting) Reset()         { *m = LanguageTargeting{} }
func (m *LanguageTargeting) String() string { return proto.CompactTextString(m) }
func (*LanguageTargeting) ProtoMessage()    {}
func (*LanguageTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{21}
}

func (m *LanguageTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LanguageTargeting.Unmarshal(m, b)
}
func (m *LanguageTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LanguageTargeting.Marshal(b, m, deterministic)
}
func (m *LanguageTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LanguageTargeting.Merge(m, src)
}
func (m *LanguageTargeting) XXX_Size() int {
	return xxx_messageInfo_LanguageTargeting.Size(m)
}
func (m *LanguageTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_LanguageTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_LanguageTargeting proto.InternalMessageInfo

func (m *LanguageTargeting) GetValue() []string {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *LanguageTargeting) GetAlternatives() []string {
	if m != nil {
		return m.Alternatives
	}
	return nil
}

type GraphicsApiTargeting struct {
	Value []*GraphicsApi `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
	// Targeting of other sibling directories that were in the Bundle.
	// For master splits this is targeting of other master splits.
	Alternatives         []*GraphicsApi `protobuf:"bytes,2,rep,name=alternatives,proto3" json:"alternatives,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GraphicsApiTargeting) Reset()         { *m = GraphicsApiTargeting{} }
func (m *GraphicsApiTargeting) String() string { return proto.CompactTextString(m) }
func (*GraphicsApiTargeting) ProtoMessage()    {}
func (*GraphicsApiTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{22}
}

func (m *GraphicsApiTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GraphicsApiTargeting.Unmarshal(m, b)
}
func (m *GraphicsApiTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GraphicsApiTargeting.Marshal(b, m, deterministic)
}
func (m *GraphicsApiTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GraphicsApiTargeting.Merge(m, src)
}
func (m *GraphicsApiTargeting) XXX_Size() int {
	return xxx_messageInfo_GraphicsApiTargeting.Size(m)
}
func (m *GraphicsApiTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_GraphicsApiTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_GraphicsApiTargeting proto.InternalMessageInfo

func (m *GraphicsApiTargeting) GetValue() []*GraphicsApi {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *GraphicsApiTargeting) GetAlternatives() []*GraphicsApi {
	if m != nil {
		return m.Alternatives
	}
	return nil
}

type SdkVersionTargeting struct {
	Value []*SdkVersion `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
	// Targeting of other sibling directories that were in the Bundle.
	// For master splits this is targeting of other master splits.
	Alternatives         []*SdkVersion `protobuf:"bytes,2,rep,name=alternatives,proto3" json:"alternatives,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SdkVersionTargeting) Reset()         { *m = SdkVersionTargeting{} }
func (m *SdkVersionTargeting) String() string { return proto.CompactTextString(m) }
func (*SdkVersionTargeting) ProtoMessage()    {}
func (*SdkVersionTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{23}
}

func (m *SdkVersionTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SdkVersionTargeting.Unmarshal(m, b)
}
func (m *SdkVersionTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SdkVersionTargeting.Marshal(b, m, deterministic)
}
func (m *SdkVersionTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SdkVersionTargeting.Merge(m, src)
}
func (m *SdkVersionTargeting) XXX_Size() int {
	return xxx_messageInfo_SdkVersionTargeting.Size(m)
}
func (m *SdkVersionTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_SdkVersionTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_SdkVersionTargeting proto.InternalMessageInfo

func (m *SdkVersionTargeting) GetValue() []*SdkVersion {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *SdkVersionTargeting) GetAlternatives() []*SdkVersion {
	if m != nil {
		return m.Alternatives
	}
	return nil
}

type TextureCompressionFormatTargeting struct {
	Value []*TextureCompressionFormat `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
	// Targeting of other sibling directories that were in the Bundle.
	// For master splits this is targeting of other master splits.
	Alternatives         []*TextureCompressionFormat `protobuf:"bytes,2,rep,name=alternatives,proto3" json:"alternatives,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *TextureCompressionFormatTargeting) Reset()         { *m = TextureCompressionFormatTargeting{} }
func (m *TextureCompressionFormatTargeting) String() string { return proto.CompactTextString(m) }
func (*TextureCompressionFormatTargeting) ProtoMessage()    {}
func (*TextureCompressionFormatTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{24}
}

func (m *TextureCompressionFormatTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TextureCompressionFormatTargeting.Unmarshal(m, b)
}
func (m *TextureCompressionFormatTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TextureCompressionFormatTargeting.Marshal(b, m, deterministic)
}
func (m *TextureCompressionFormatTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TextureCompressionFormatTargeting.Merge(m, src)
}
func (m *TextureCompressionFormatTargeting) XXX_Size() int {
	return xxx_messageInfo_TextureCompressionFormatTargeting.Size(m)
}
func (m *TextureCompressionFormatTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_TextureCompressionFormatTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_TextureCompressionFormatTargeting proto.InternalMessageInfo

func (m *TextureCompressionFormatTargeting) GetValue() []*TextureCompressionFormat {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *TextureCompressionFormatTargeting) GetAlternatives() []*TextureCompressionFormat {
	if m != nil {
		return m.Alternatives
	}
	return nil
}

type SanitizerTargeting struct {
	Value                []*Sanitizer `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SanitizerTargeting) Reset()         { *m = SanitizerTargeting{} }
func (m *SanitizerTargeting) String() string { return proto.CompactTextString(m) }
func (*SanitizerTargeting) ProtoMessage()    {}
func (*SanitizerTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{25}
}

func (m *SanitizerTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SanitizerTargeting.Unmarshal(m, b)
}
func (m *SanitizerTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SanitizerTargeting.Marshal(b, m, deterministic)
}
func (m *SanitizerTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SanitizerTargeting.Merge(m, src)
}
func (m *SanitizerTargeting) XXX_Size() int {
	return xxx_messageInfo_SanitizerTargeting.Size(m)
}
func (m *SanitizerTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_SanitizerTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_SanitizerTargeting proto.InternalMessageInfo

func (m *SanitizerTargeting) GetValue() []*Sanitizer {
	if m != nil {
		return m.Value
	}
	return nil
}

// Since other atom targeting messages have the "OR" semantic on values
// the DeviceFeatureTargeting represents only one device feature to retain
// that convention.
type DeviceFeatureTargeting struct {
	RequiredFeature      *DeviceFeature `protobuf:"bytes,1,opt,name=required_feature,json=requiredFeature,proto3" json:"required_feature,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *DeviceFeatureTargeting) Reset()         { *m = DeviceFeatureTargeting{} }
func (m *DeviceFeatureTargeting) String() string { return proto.CompactTextString(m) }
func (*DeviceFeatureTargeting) ProtoMessage()    {}
func (*DeviceFeatureTargeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_df45b505afdf471e, []int{26}
}

func (m *DeviceFeatureTargeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeviceFeatureTargeting.Unmarshal(m, b)
}
func (m *DeviceFeatureTargeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeviceFeatureTargeting.Marshal(b, m, deterministic)
}
func (m *DeviceFeatureTargeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeviceFeatureTargeting.Merge(m, src)
}
func (m *DeviceFeatureTargeting) XXX_Size() int {
	return xxx_messageInfo_DeviceFeatureTargeting.Size(m)
}
func (m *DeviceFeatureTargeting) XXX_DiscardUnknown() {
	xxx_messageInfo_DeviceFeatureTargeting.DiscardUnknown(m)
}

var xxx_messageInfo_DeviceFeatureTargeting proto.InternalMessageInfo

func (m *DeviceFeatureTargeting) GetRequiredFeature() *DeviceFeature {
	if m != nil {
		return m.RequiredFeature
	}
	return nil
}

func init() {
	proto.RegisterEnum("android.bundle.ScreenDensity_DensityAlias", ScreenDensity_DensityAlias_name, ScreenDensity_DensityAlias_value)
	proto.RegisterEnum("android.bundle.TextureCompressionFormat_TextureCompressionFormatAlias", TextureCompressionFormat_TextureCompressionFormatAlias_name, TextureCompressionFormat_TextureCompressionFormatAlias_value)
	proto.RegisterEnum("android.bundle.Abi_AbiAlias", Abi_AbiAlias_name, Abi_AbiAlias_value)
	proto.RegisterEnum("android.bundle.Sanitizer_SanitizerAlias", Sanitizer_SanitizerAlias_name, Sanitizer_SanitizerAlias_value)
	proto.RegisterType((*VariantTargeting)(nil), "android.bundle.VariantTargeting")
	proto.RegisterType((*ApkTargeting)(nil), "android.bundle.ApkTargeting")
	proto.RegisterType((*ModuleTargeting)(nil), "android.bundle.ModuleTargeting")
	proto.RegisterType((*UserCountriesTargeting)(nil), "android.bundle.UserCountriesTargeting")
	proto.RegisterType((*ScreenDensity)(nil), "android.bundle.ScreenDensity")
	proto.RegisterType((*Int32Value)(nil), "android.bundle.Int32Value")
	proto.RegisterType((*SdkVersion)(nil), "android.bundle.SdkVersion")
	proto.RegisterType((*GraphicsApi)(nil), "android.bundle.GraphicsApi")
	proto.RegisterType((*VulkanVersion)(nil), "android.bundle.VulkanVersion")
	proto.RegisterType((*OpenGlVersion)(nil), "android.bundle.OpenGlVersion")
	proto.RegisterType((*TextureCompressionFormat)(nil), "android.bundle.TextureCompressionFormat")
	proto.RegisterType((*Abi)(nil), "android.bundle.Abi")
	proto.RegisterType((*MultiAbi)(nil), "android.bundle.MultiAbi")
	proto.RegisterType((*Sanitizer)(nil), "android.bundle.Sanitizer")
	proto.RegisterType((*DeviceFeature)(nil), "android.bundle.DeviceFeature")
	proto.RegisterType((*AssetsDirectoryTargeting)(nil), "android.bundle.AssetsDirectoryTargeting")
	proto.RegisterType((*NativeDirectoryTargeting)(nil), "android.bundle.NativeDirectoryTargeting")
	proto.RegisterType((*ApexImageTargeting)(nil), "android.bundle.ApexImageTargeting")
	proto.RegisterType((*AbiTargeting)(nil), "android.bundle.AbiTargeting")
	proto.RegisterType((*MultiAbiTargeting)(nil), "android.bundle.MultiAbiTargeting")
	proto.RegisterType((*ScreenDensityTargeting)(nil), "android.bundle.ScreenDensityTargeting")
	proto.RegisterType((*LanguageTargeting)(nil), "android.bundle.LanguageTargeting")
	proto.RegisterType((*GraphicsApiTargeting)(nil), "android.bundle.GraphicsApiTargeting")
	proto.RegisterType((*SdkVersionTargeting)(nil), "android.bundle.SdkVersionTargeting")
	proto.RegisterType((*TextureCompressionFormatTargeting)(nil), "android.bundle.TextureCompressionFormatTargeting")
	proto.RegisterType((*SanitizerTargeting)(nil), "android.bundle.SanitizerTargeting")
	proto.RegisterType((*DeviceFeatureTargeting)(nil), "android.bundle.DeviceFeatureTargeting")
}

func init() {
	proto.RegisterFile("targeting.proto", fileDescriptor_df45b505afdf471e)
}

var fileDescriptor_df45b505afdf471e = []byte{
	// 1504 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x58, 0x5b, 0x6f, 0xe3, 0xc4,
	0x17, 0xaf, 0x93, 0xa6, 0x4d, 0x4e, 0x92, 0xd6, 0x3b, 0xe9, 0xbf, 0xff, 0xb0, 0xec, 0x4a, 0x5b,
	0xef, 0x85, 0xee, 0x0a, 0x05, 0xda, 0xae, 0xba, 0x15, 0x97, 0x22, 0xd7, 0x71, 0x9b, 0x48, 0x4d,
	0x9a, 0x75, 0xdc, 0x6c, 0x59, 0x90, 0xbc, 0x4e, 0x3c, 0x0d, 0xa6, 0x89, 0x1d, 0x6c, 0xa7, 0xda,
	0xe5, 0x05, 0x81, 0x90, 0x90, 0x78, 0xe2, 0x8d, 0x77, 0x3e, 0x00, 0x12, 0x4f, 0x08, 0x89, 0x07,
	0x24, 0x3e, 0x0c, 0x7c, 0x0c, 0x34, 0xbe, 0x24, 0x9e, 0xc4, 0x4e, 0xb3, 0x2c, 0xf0, 0x50, 0xe5,
	0xcc, 0xf1, 0x39, 0xbf, 0x73, 0x99, 0x73, 0x66, 0xce, 0x14, 0x56, 0x1d, 0xd5, 0xea, 0x62, 0x47,
	0x37, 0xba, 0xa5, 0x81, 0x65, 0x3a, 0x26, 0x5a, 0x51, 0x0d, 0xcd, 0x32, 0x75, 0xad, 0xd4, 0x1e,
	0x1a, 0x5a, 0x0f, 0x73, 0x7f, 0x26, 0x81, 0x6d, 0xa9, 0x96, 0xae, 0x1a, 0x8e, 0x1c, 0x88, 0xa2,
	0x27, 0xf0, 0x3f, 0x5b, 0xbb, 0x50, 0x2e, 0xb1, 0x65, 0xeb, 0xa6, 0xa1, 0x8c, 0x30, 0x8a, 0xcc,
	0x2d, 0x66, 0x33, 0xbb, 0x7d, 0xbb, 0x44, 0x83, 0x94, 0x9a, 0xda, 0x45, 0xcb, 0x93, 0x1d, 0x61,
	0x48, 0x05, 0x7b, 0x9a, 0x89, 0x78, 0xc8, 0xab, 0x6d, 0x3d, 0x04, 0x98, 0x70, 0x01, 0x6f, 0x4c,
	0x02, 0xf2, 0x6d, 0x7d, 0x8c, 0x94, 0x53, 0x43, 0x2b, 0xf4, 0x0c, 0x8a, 0x76, 0xc7, 0xc2, 0xd8,
	0x50, 0x34, 0x6c, 0xd8, 0xba, 0xf3, 0x22, 0x84, 0x96, 0x74, 0xd1, 0xee, 0x4d, 0xb9, 0xe7, 0xca,
	0x97, 0x3d, 0xf1, 0x31, 0xee, 0xba, 0x1d, 0xc9, 0x47, 0x8f, 0xa1, 0xd0, 0x1f, 0xf6, 0x1c, 0x5d,
	0xa1, 0x5d, 0x5d, 0x74, 0xc1, 0x37, 0x26, 0xc1, 0x6b, 0x44, 0x94, 0xf2, 0xf7, 0x5a, 0x7f, 0x92,
	0x85, 0xbe, 0x62, 0xe0, 0x8e, 0x83, 0x9f, 0x3b, 0x43, 0x0b, 0x2b, 0x1d, 0xb3, 0x3f, 0xb0, 0xb0,
	0xed, 0x66, 0xf6, 0xdc, 0xb4, 0xfa, 0xaa, 0x13, 0x32, 0x92, 0x72, 0x8d, 0x6c, 0x4d, 0x1a, 0x91,
	0x3d, 0x5d, 0x61, 0xac, 0x7a, 0xe8, 0x6a, 0x8e, 0x8d, 0x6e, 0x38, 0x57, 0x89, 0x70, 0x7f, 0xa4,
	0x20, 0xc7, 0x0f, 0x2e, 0x66, 0xec, 0x06, 0xf3, 0xd2, 0xbb, 0xf1, 0x14, 0xd6, 0xbb, 0x96, 0x3a,
	0xf8, 0x44, 0xef, 0xd8, 0x8a, 0x3a, 0x98, 0xde, 0xd9, 0x3b, 0x93, 0x58, 0x47, 0xbe, 0x34, 0x3f,
	0x08, 0x61, 0xae, 0x75, 0x23, 0xb8, 0xa8, 0x01, 0xa8, 0xa7, 0x1a, 0xdd, 0xa1, 0xda, 0xc5, 0x53,
	0x7b, 0x3c, 0xb5, 0x0d, 0xc7, 0xbe, 0x64, 0x68, 0x1b, 0x7a, 0x93, 0xac, 0x99, 0xb5, 0xb3, 0xf8,
	0x8f, 0xd4, 0x4e, 0x6c, 0xe7, 0xa4, 0x5e, 0xb1, 0x73, 0xe6, 0xae, 0xa0, 0xa5, 0x7f, 0xaf, 0x82,
	0xe2, 0x3a, 0x63, 0xf9, 0x15, 0x3a, 0xa3, 0x09, 0x05, 0x5b, 0x35, 0x74, 0x47, 0xff, 0x1c, 0x5b,
	0x21, 0xc8, 0xb4, 0x0b, 0xc9, 0x4d, 0xa5, 0x2b, 0x10, 0x1d, 0x63, 0x22, 0x7b, 0x8a, 0xc7, 0xfd,
	0x98, 0x80, 0xd5, 0x9a, 0xa9, 0x0d, 0x7b, 0xf8, 0x3f, 0x38, 0xd3, 0x9e, 0x41, 0x51, 0xc3, 0x97,
	0x7a, 0x07, 0x2b, 0xe7, 0x58, 0x75, 0xf7, 0x27, 0xdc, 0x04, 0xc9, 0xa8, 0xa2, 0x2a, 0xbb, 0xf2,
	0x87, 0x9e, 0x78, 0xa8, 0xa8, 0xb4, 0x48, 0x3e, 0xb1, 0x30, 0xb4, 0xb1, 0xa5, 0x74, 0xcc, 0xa1,
	0xe1, 0x58, 0x3a, 0xb6, 0xaf, 0x3e, 0xf2, 0x4e, 0x6d, 0x6c, 0x09, 0x81, 0x78, 0xc8, 0xc2, 0x30,
	0x92, 0xcf, 0x3d, 0x81, 0xf5, 0x68, 0x0d, 0x74, 0x1b, 0xf2, 0x9e, 0xd9, 0x17, 0x4a, 0xc7, 0xd4,
	0xb0, 0x5d, 0x64, 0x6e, 0x25, 0x37, 0x33, 0x52, 0xce, 0x67, 0x0a, 0x84, 0x87, 0x8a, 0xb0, 0x8c,
	0x9f, 0x77, 0x7a, 0x43, 0x0d, 0xbb, 0x6d, 0x9f, 0x96, 0x82, 0x25, 0xf7, 0x7d, 0x02, 0xf2, 0x54,
	0x0b, 0xa1, 0xc7, 0x90, 0x0f, 0x9a, 0x4f, 0xed, 0xe9, 0xaa, 0xed, 0xe6, 0x7f, 0x65, 0xfb, 0xc1,
	0xcc, 0xc6, 0x2b, 0xf9, 0xbf, 0x3c, 0xd1, 0xa8, 0x2c, 0x48, 0x39, 0x2d, 0xb4, 0x46, 0x1b, 0x90,
	0x0d, 0x20, 0xb5, 0x81, 0xee, 0xba, 0x90, 0xaa, 0x2c, 0x48, 0xe0, 0x33, 0xcb, 0x03, 0x9d, 0xfb,
	0x02, 0x72, 0x61, 0x08, 0xf4, 0x7f, 0x28, 0x94, 0xc5, 0x7a, 0xb3, 0x2a, 0x7f, 0xa8, 0x9c, 0xd6,
	0x9b, 0x0d, 0x51, 0xa8, 0x1e, 0x56, 0xc5, 0x32, 0xbb, 0x80, 0x32, 0x90, 0xaa, 0x9f, 0x94, 0x1b,
	0x55, 0x96, 0x41, 0x69, 0x58, 0x3c, 0x26, 0x54, 0x82, 0x50, 0x35, 0x42, 0x25, 0xc9, 0x67, 0xb9,
	0x45, 0xc8, 0x45, 0xc2, 0xac, 0x10, 0x2a, 0x45, 0x98, 0x67, 0x2e, 0xb9, 0x84, 0x00, 0x96, 0xce,
	0x3c, 0x7a, 0x19, 0x65, 0x61, 0xf9, 0xcc, 0x5f, 0xa4, 0x0f, 0x56, 0xc7, 0x61, 0x9b, 0x06, 0x36,
	0xcf, 0x39, 0x0e, 0xa0, 0x6a, 0x38, 0x3b, 0xdb, 0x2d, 0xb5, 0x37, 0xc4, 0x68, 0x0d, 0x52, 0x97,
	0x84, 0x70, 0xb3, 0x91, 0x92, 0xbc, 0x05, 0xf7, 0x0e, 0xc0, 0xb8, 0x0c, 0xd1, 0x9b, 0x90, 0xec,
	0xeb, 0x86, 0x5f, 0xaf, 0xd7, 0x27, 0xf3, 0x35, 0x06, 0x93, 0x88, 0x18, 0xf7, 0x0b, 0x03, 0xd9,
	0xd0, 0x61, 0x8b, 0xea, 0x50, 0xe8, 0xeb, 0x86, 0x62, 0x0e, 0xb0, 0xa1, 0x74, 0x7b, 0x41, 0x1f,
	0xf8, 0x68, 0x37, 0x27, 0xd1, 0x4e, 0x06, 0xd8, 0x38, 0xea, 0xf9, 0x96, 0x2b, 0x0b, 0x12, 0xdb,
	0xd7, 0x0d, 0x8a, 0x87, 0x6a, 0x80, 0x08, 0xde, 0xe5, 0xb0, 0x77, 0xa1, 0x1a, 0x23, 0xb8, 0x44,
	0x34, 0x5c, 0xcb, 0x95, 0xa2, 0xe1, 0x28, 0xde, 0x41, 0x16, 0x32, 0xe4, 0xfe, 0xf0, 0x72, 0xf3,
	0x2e, 0xe4, 0xa9, 0xaf, 0x24, 0x3d, 0x7d, 0xf5, 0x53, 0xd3, 0x0a, 0xd2, 0xe3, 0x2e, 0x5c, 0xae,
	0x6e, 0x98, 0x96, 0xb7, 0xe3, 0x92, 0xb7, 0x20, 0xca, 0xb4, 0xa7, 0x2f, 0xa3, 0xfc, 0x73, 0x02,
	0x8a, 0x71, 0x47, 0x25, 0xfa, 0x18, 0x52, 0xe1, 0x92, 0x3d, 0x9c, 0xf7, 0x8c, 0x8d, 0xfd, 0xe0,
	0xd6, 0xa2, 0xe4, 0x81, 0x72, 0xbf, 0x32, 0x70, 0x73, 0xa6, 0x20, 0x7a, 0x00, 0xf7, 0x42, 0xc5,
	0xaa, 0xc8, 0xe2, 0x99, 0x7c, 0x2a, 0x89, 0x8a, 0x70, 0x52, 0x6b, 0x48, 0x62, 0xb3, 0x59, 0x3d,
	0xa9, 0x2b, 0x87, 0x27, 0x52, 0x8d, 0x97, 0xd9, 0x05, 0x94, 0x87, 0x8c, 0x28, 0x0b, 0x5b, 0x8a,
	0x74, 0x74, 0xb0, 0xc7, 0x32, 0x28, 0x07, 0xe9, 0x06, 0x7f, 0x2c, 0xca, 0xb2, 0x58, 0x66, 0x13,
	0x64, 0x25, 0x57, 0x24, 0x51, 0x54, 0xca, 0x02, 0x9b, 0x44, 0xcb, 0x90, 0xe4, 0x65, 0xc1, 0xab,
	0xe8, 0x63, 0x42, 0xa5, 0x08, 0x55, 0x3e, 0x93, 0xb7, 0xd8, 0x25, 0x42, 0x35, 0x77, 0x64, 0x81,
	0x5d, 0x26, 0x55, 0xde, 0x68, 0x49, 0xb2, 0xc0, 0xa6, 0x09, 0x93, 0x6f, 0xca, 0x02, 0x9b, 0x21,
	0x94, 0x28, 0x0b, 0xdb, 0x2c, 0x70, 0xbf, 0x31, 0x90, 0xe4, 0xdb, 0x3a, 0xda, 0xa6, 0x93, 0x14,
	0x35, 0x4c, 0x90, 0x3f, 0x2a, 0xf4, 0xaf, 0x19, 0x48, 0x07, 0x3c, 0x74, 0x0b, 0x6e, 0x84, 0xa3,
	0x14, 0x1a, 0xa7, 0x0a, 0x2f, 0x09, 0x95, 0xaa, 0x2c, 0x0a, 0x24, 0x5c, 0x76, 0x81, 0x34, 0x16,
	0x2f, 0xd5, 0x44, 0xfe, 0x80, 0x74, 0xe9, 0x2a, 0x64, 0xfd, 0x85, 0xd2, 0x7a, 0xc4, 0xb3, 0x09,
	0x12, 0x39, 0x2f, 0xd5, 0x76, 0x1f, 0x2a, 0xad, 0x3d, 0xde, 0x8b, 0xee, 0x6c, 0x6f, 0x97, 0x5d,
	0x74, 0x5b, 0x73, 0x6f, 0x57, 0xd9, 0x7d, 0xe8, 0xc5, 0x57, 0xab, 0x36, 0x9a, 0x5e, 0xc3, 0x12,
	0x6a, 0xf7, 0x21, 0xbb, 0xcc, 0x6d, 0x41, 0x3a, 0xb8, 0xb3, 0xd0, 0x5d, 0x48, 0xaa, 0x6d, 0xdd,
	0x3d, 0xed, 0xb2, 0xdb, 0x85, 0x88, 0x20, 0x24, 0xf2, 0x9d, 0xbb, 0x84, 0xcc, 0xe8, 0x4e, 0x42,
	0xfb, 0x74, 0xe8, 0x9b, 0xb1, 0xb7, 0xd7, 0x98, 0xa2, 0xd2, 0x70, 0x1f, 0x56, 0xe8, 0x0f, 0xc4,
	0xcf, 0xfa, 0x49, 0x5d, 0xf4, 0xf6, 0xb3, 0xf2, 0x84, 0x2f, 0x97, 0xc9, 0x46, 0xb3, 0x0c, 0xf7,
	0x11, 0xe4, 0xa9, 0x4b, 0x04, 0x6d, 0x40, 0x2e, 0xb8, 0x7e, 0x0c, 0xb5, 0xef, 0x9d, 0x23, 0x19,
	0x29, 0xeb, 0xf3, 0xea, 0x6a, 0x1f, 0xa3, 0x37, 0x60, 0x35, 0x10, 0x09, 0xb7, 0x6b, 0x4a, 0x5a,
	0xf1, 0xd9, 0x7e, 0xc3, 0x70, 0xbf, 0x27, 0xa0, 0xc8, 0xdb, 0x36, 0x76, 0xec, 0xb2, 0x6e, 0xe1,
	0x8e, 0x63, 0x5a, 0xa1, 0x09, 0xa7, 0x14, 0x24, 0xe6, 0xea, 0x51, 0x91, 0x08, 0xa2, 0x23, 0xc8,
	0x85, 0x27, 0xc4, 0x97, 0x9a, 0x0b, 0xb3, 0xa1, 0xb9, 0x10, 0x99, 0x70, 0x3d, 0x7e, 0x00, 0xf2,
	0xef, 0xc1, 0xbf, 0x31, 0xf6, 0x14, 0xe3, 0xc6, 0x1e, 0xf4, 0x3e, 0xa4, 0x83, 0x11, 0x32, 0x6e,
	0xf8, 0x9f, 0x9e, 0x3a, 0x47, 0x2a, 0xdc, 0x0f, 0x09, 0x28, 0xd6, 0x55, 0x47, 0xbf, 0xc4, 0x11,
	0x59, 0xbc, 0x1b, 0xce, 0x62, 0x6c, 0x79, 0xa1, 0xfd, 0xc8, 0xe4, 0xbd, 0x3e, 0x23, 0x79, 0x74,
	0xce, 0xce, 0xe7, 0xc8, 0xd9, 0xe6, 0xbc, 0x39, 0x9b, 0x91, 0xaa, 0x47, 0x90, 0x19, 0x8d, 0x61,
	0x7e, 0xae, 0x5e, 0x8b, 0xad, 0x7e, 0x69, 0x2c, 0xcb, 0xc9, 0x80, 0xf8, 0x01, 0x7e, 0x5e, 0xed,
	0x53, 0x73, 0xfa, 0x3e, 0x64, 0x46, 0x73, 0xa6, 0x9f, 0xa3, 0x39, 0xa6, 0xcb, 0x74, 0x30, 0x5d,
	0x72, 0x16, 0xe4, 0xa8, 0x21, 0xf3, 0xfe, 0xf8, 0x76, 0x8d, 0x6d, 0x67, 0x4f, 0x02, 0x3d, 0x82,
	0x9c, 0xda, 0x73, 0xb0, 0x65, 0xb8, 0x3b, 0x67, 0xfb, 0x13, 0x5c, 0xa4, 0x06, 0x25, 0xc8, 0x7d,
	0xc9, 0xc0, 0xb5, 0x29, 0x9f, 0x50, 0x89, 0xb6, 0x5c, 0x8c, 0x8b, 0x22, 0x30, 0xff, 0x5e, 0xa4,
	0xf9, 0x78, 0x35, 0xda, 0x87, 0xef, 0x18, 0x58, 0x8f, 0x7e, 0xb0, 0xa0, 0x1d, 0xda, 0x91, 0x9b,
	0x33, 0xc7, 0xad, 0xc0, 0x1b, 0x3e, 0xd2, 0x9b, 0x2b, 0x74, 0x69, 0x97, 0x6a, 0x70, 0x6d, 0xaa,
	0x49, 0xc2, 0xd3, 0x0e, 0x19, 0x26, 0x7d, 0x6b, 0x5c, 0x84, 0xb5, 0xcc, 0x04, 0xdc, 0xb7, 0x0c,
	0xac, 0x45, 0x1d, 0x15, 0x68, 0x8b, 0x8e, 0x6f, 0x66, 0x8b, 0xf8, 0xf6, 0x3e, 0x88, 0x8c, 0x6e,
	0xa6, 0x26, 0xed, 0xcc, 0x37, 0x0c, 0x14, 0x22, 0x9e, 0x09, 0xe8, 0x6d, 0xda, 0x97, 0xeb, 0xf1,
	0x4f, 0x8b, 0xc0, 0x95, 0xfd, 0x48, 0x57, 0x66, 0x29, 0xd2, 0x9e, 0xfc, 0xc4, 0xc0, 0xc6, 0x95,
	0x47, 0x1d, 0xb9, 0x9f, 0xc2, 0x7e, 0xcd, 0xdf, 0xf8, 0xbe, 0x97, 0xc7, 0x91, 0x5e, 0xce, 0x0f,
	0x43, 0xfb, 0x2c, 0x02, 0x9a, 0x7e, 0xce, 0xa1, 0xb7, 0x68, 0x1f, 0x67, 0x9c, 0x22, 0xfe, 0x8c,
	0xdc, 0x86, 0xf5, 0xe8, 0xe7, 0x14, 0xaa, 0x00, 0x6b, 0xe1, 0xcf, 0x86, 0xba, 0x85, 0xb5, 0xe0,
	0x69, 0x16, 0x37, 0xee, 0x52, 0x08, 0xd2, 0x6a, 0xa0, 0xe6, 0x33, 0x0e, 0x1e, 0x00, 0xea, 0x98,
	0xfd, 0x09, 0xa5, 0xa7, 0x6b, 0xfe, 0x5a, 0xf1, 0xd6, 0x8a, 0xfb, 0x0f, 0xb6, 0xf6, 0x92, 0xfb,
	0xb3, 0xf3, 0x57, 0x00, 0x00, 0x00, 0xff, 0xff, 0x47, 0xe0, 0x85, 0xa8, 0x7a, 0x13, 0x00, 0x00,
}
