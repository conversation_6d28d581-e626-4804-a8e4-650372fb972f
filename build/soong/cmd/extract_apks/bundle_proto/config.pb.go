// Code generated by protoc-gen-go. DO NOT EDIT.
// source: config.proto

package android_bundle_proto

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type BundleConfig_BundleType int32

const (
	BundleConfig_REGULAR    BundleConfig_BundleType = 0
	BundleConfig_APEX       BundleConfig_BundleType = 1
	BundleConfig_ASSET_ONLY BundleConfig_BundleType = 2
)

var BundleConfig_BundleType_name = map[int32]string{
	0: "REGULAR",
	1: "APEX",
	2: "ASSET_ONLY",
}

var BundleConfig_BundleType_value = map[string]int32{
	"REGULAR":    0,
	"APEX":       1,
	"ASSET_ONLY": 2,
}

func (x BundleConfig_BundleType) String() string {
	return proto.EnumName(BundleConfig_BundleType_name, int32(x))
}

func (BundleConfig_BundleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{0, 0}
}

type SplitDimension_Value int32

const (
	SplitDimension_UNSPECIFIED_VALUE          SplitDimension_Value = 0
	SplitDimension_ABI                        SplitDimension_Value = 1
	SplitDimension_SCREEN_DENSITY             SplitDimension_Value = 2
	SplitDimension_LANGUAGE                   SplitDimension_Value = 3
	SplitDimension_TEXTURE_COMPRESSION_FORMAT SplitDimension_Value = 4
	// BEGIN-INTERNAL
	SplitDimension_GRAPHICS_API SplitDimension_Value = 5
)

var SplitDimension_Value_name = map[int32]string{
	0: "UNSPECIFIED_VALUE",
	1: "ABI",
	2: "SCREEN_DENSITY",
	3: "LANGUAGE",
	4: "TEXTURE_COMPRESSION_FORMAT",
	5: "GRAPHICS_API",
}

var SplitDimension_Value_value = map[string]int32{
	"UNSPECIFIED_VALUE":          0,
	"ABI":                        1,
	"SCREEN_DENSITY":             2,
	"LANGUAGE":                   3,
	"TEXTURE_COMPRESSION_FORMAT": 4,
	"GRAPHICS_API":               5,
}

func (x SplitDimension_Value) String() string {
	return proto.EnumName(SplitDimension_Value_name, int32(x))
}

func (SplitDimension_Value) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{9, 0}
}

type BundleConfig struct {
	Bundletool    *Bundletool    `protobuf:"bytes,1,opt,name=bundletool,proto3" json:"bundletool,omitempty"`
	Optimizations *Optimizations `protobuf:"bytes,2,opt,name=optimizations,proto3" json:"optimizations,omitempty"`
	Compression   *Compression   `protobuf:"bytes,3,opt,name=compression,proto3" json:"compression,omitempty"`
	// Resources to be always kept in the master split.
	MasterResources *MasterResources `protobuf:"bytes,4,opt,name=master_resources,json=masterResources,proto3" json:"master_resources,omitempty"`
	ApexConfig      *ApexConfig      `protobuf:"bytes,5,opt,name=apex_config,json=apexConfig,proto3" json:"apex_config,omitempty"`
	// APKs to be signed with the same key as generated APKs.
	UnsignedEmbeddedApkConfig []*UnsignedEmbeddedApkConfig `protobuf:"bytes,6,rep,name=unsigned_embedded_apk_config,json=unsignedEmbeddedApkConfig,proto3" json:"unsigned_embedded_apk_config,omitempty"`
	AssetModulesConfig        *AssetModulesConfig          `protobuf:"bytes,7,opt,name=asset_modules_config,json=assetModulesConfig,proto3" json:"asset_modules_config,omitempty"`
	Type                      BundleConfig_BundleType      `protobuf:"varint,8,opt,name=type,proto3,enum=android.bundle.BundleConfig_BundleType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}                     `json:"-"`
	XXX_unrecognized          []byte                       `json:"-"`
	XXX_sizecache             int32                        `json:"-"`
}

func (m *BundleConfig) Reset()         { *m = BundleConfig{} }
func (m *BundleConfig) String() string { return proto.CompactTextString(m) }
func (*BundleConfig) ProtoMessage()    {}
func (*BundleConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{0}
}

func (m *BundleConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BundleConfig.Unmarshal(m, b)
}
func (m *BundleConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BundleConfig.Marshal(b, m, deterministic)
}
func (m *BundleConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BundleConfig.Merge(m, src)
}
func (m *BundleConfig) XXX_Size() int {
	return xxx_messageInfo_BundleConfig.Size(m)
}
func (m *BundleConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_BundleConfig.DiscardUnknown(m)
}

var xxx_messageInfo_BundleConfig proto.InternalMessageInfo

func (m *BundleConfig) GetBundletool() *Bundletool {
	if m != nil {
		return m.Bundletool
	}
	return nil
}

func (m *BundleConfig) GetOptimizations() *Optimizations {
	if m != nil {
		return m.Optimizations
	}
	return nil
}

func (m *BundleConfig) GetCompression() *Compression {
	if m != nil {
		return m.Compression
	}
	return nil
}

func (m *BundleConfig) GetMasterResources() *MasterResources {
	if m != nil {
		return m.MasterResources
	}
	return nil
}

func (m *BundleConfig) GetApexConfig() *ApexConfig {
	if m != nil {
		return m.ApexConfig
	}
	return nil
}

func (m *BundleConfig) GetUnsignedEmbeddedApkConfig() []*UnsignedEmbeddedApkConfig {
	if m != nil {
		return m.UnsignedEmbeddedApkConfig
	}
	return nil
}

func (m *BundleConfig) GetAssetModulesConfig() *AssetModulesConfig {
	if m != nil {
		return m.AssetModulesConfig
	}
	return nil
}

func (m *BundleConfig) GetType() BundleConfig_BundleType {
	if m != nil {
		return m.Type
	}
	return BundleConfig_REGULAR
}

type Bundletool struct {
	// Version of BundleTool used to build the Bundle.
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Bundletool) Reset()         { *m = Bundletool{} }
func (m *Bundletool) String() string { return proto.CompactTextString(m) }
func (*Bundletool) ProtoMessage()    {}
func (*Bundletool) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{1}
}

func (m *Bundletool) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Bundletool.Unmarshal(m, b)
}
func (m *Bundletool) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Bundletool.Marshal(b, m, deterministic)
}
func (m *Bundletool) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Bundletool.Merge(m, src)
}
func (m *Bundletool) XXX_Size() int {
	return xxx_messageInfo_Bundletool.Size(m)
}
func (m *Bundletool) XXX_DiscardUnknown() {
	xxx_messageInfo_Bundletool.DiscardUnknown(m)
}

var xxx_messageInfo_Bundletool proto.InternalMessageInfo

func (m *Bundletool) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type Compression struct {
	// Glob matching the list of files to leave uncompressed in the APKs.
	// The matching is done against the path of files in the APK, thus excluding
	// the name of the modules, and using forward slash ("/") as a name separator.
	// Examples: "res/raw/**", "assets/**/*.uncompressed", etc.
	UncompressedGlob     []string `protobuf:"bytes,1,rep,name=uncompressed_glob,json=uncompressedGlob,proto3" json:"uncompressed_glob,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Compression) Reset()         { *m = Compression{} }
func (m *Compression) String() string { return proto.CompactTextString(m) }
func (*Compression) ProtoMessage()    {}
func (*Compression) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{2}
}

func (m *Compression) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Compression.Unmarshal(m, b)
}
func (m *Compression) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Compression.Marshal(b, m, deterministic)
}
func (m *Compression) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Compression.Merge(m, src)
}
func (m *Compression) XXX_Size() int {
	return xxx_messageInfo_Compression.Size(m)
}
func (m *Compression) XXX_DiscardUnknown() {
	xxx_messageInfo_Compression.DiscardUnknown(m)
}

var xxx_messageInfo_Compression proto.InternalMessageInfo

func (m *Compression) GetUncompressedGlob() []string {
	if m != nil {
		return m.UncompressedGlob
	}
	return nil
}

// Resources to keep in the master split.
type MasterResources struct {
	// Resource IDs to be kept in master split.
	ResourceIds []int32 `protobuf:"varint,1,rep,packed,name=resource_ids,json=resourceIds,proto3" json:"resource_ids,omitempty"`
	// Resource names to be kept in master split.
	ResourceNames        []string `protobuf:"bytes,2,rep,name=resource_names,json=resourceNames,proto3" json:"resource_names,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterResources) Reset()         { *m = MasterResources{} }
func (m *MasterResources) String() string { return proto.CompactTextString(m) }
func (*MasterResources) ProtoMessage()    {}
func (*MasterResources) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{3}
}

func (m *MasterResources) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterResources.Unmarshal(m, b)
}
func (m *MasterResources) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterResources.Marshal(b, m, deterministic)
}
func (m *MasterResources) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterResources.Merge(m, src)
}
func (m *MasterResources) XXX_Size() int {
	return xxx_messageInfo_MasterResources.Size(m)
}
func (m *MasterResources) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterResources.DiscardUnknown(m)
}

var xxx_messageInfo_MasterResources proto.InternalMessageInfo

func (m *MasterResources) GetResourceIds() []int32 {
	if m != nil {
		return m.ResourceIds
	}
	return nil
}

func (m *MasterResources) GetResourceNames() []string {
	if m != nil {
		return m.ResourceNames
	}
	return nil
}

type Optimizations struct {
	SplitsConfig *SplitsConfig `protobuf:"bytes,1,opt,name=splits_config,json=splitsConfig,proto3" json:"splits_config,omitempty"`
	// This is for uncompressing native libraries on M+ devices (L+ devices on
	// instant apps).
	UncompressNativeLibraries *UncompressNativeLibraries `protobuf:"bytes,2,opt,name=uncompress_native_libraries,json=uncompressNativeLibraries,proto3" json:"uncompress_native_libraries,omitempty"`
	// This is for uncompressing dex files on P+ devices.
	UncompressDexFiles *UncompressDexFiles `protobuf:"bytes,3,opt,name=uncompress_dex_files,json=uncompressDexFiles,proto3" json:"uncompress_dex_files,omitempty"`
	// Configuration for the generation of standalone APKs.
	// If no StandaloneConfig is set, the configuration is inherited from
	// splits_config.
	StandaloneConfig     *StandaloneConfig `protobuf:"bytes,4,opt,name=standalone_config,json=standaloneConfig,proto3" json:"standalone_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Optimizations) Reset()         { *m = Optimizations{} }
func (m *Optimizations) String() string { return proto.CompactTextString(m) }
func (*Optimizations) ProtoMessage()    {}
func (*Optimizations) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{4}
}

func (m *Optimizations) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Optimizations.Unmarshal(m, b)
}
func (m *Optimizations) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Optimizations.Marshal(b, m, deterministic)
}
func (m *Optimizations) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Optimizations.Merge(m, src)
}
func (m *Optimizations) XXX_Size() int {
	return xxx_messageInfo_Optimizations.Size(m)
}
func (m *Optimizations) XXX_DiscardUnknown() {
	xxx_messageInfo_Optimizations.DiscardUnknown(m)
}

var xxx_messageInfo_Optimizations proto.InternalMessageInfo

func (m *Optimizations) GetSplitsConfig() *SplitsConfig {
	if m != nil {
		return m.SplitsConfig
	}
	return nil
}

func (m *Optimizations) GetUncompressNativeLibraries() *UncompressNativeLibraries {
	if m != nil {
		return m.UncompressNativeLibraries
	}
	return nil
}

func (m *Optimizations) GetUncompressDexFiles() *UncompressDexFiles {
	if m != nil {
		return m.UncompressDexFiles
	}
	return nil
}

func (m *Optimizations) GetStandaloneConfig() *StandaloneConfig {
	if m != nil {
		return m.StandaloneConfig
	}
	return nil
}

type UncompressNativeLibraries struct {
	Enabled              bool     `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UncompressNativeLibraries) Reset()         { *m = UncompressNativeLibraries{} }
func (m *UncompressNativeLibraries) String() string { return proto.CompactTextString(m) }
func (*UncompressNativeLibraries) ProtoMessage()    {}
func (*UncompressNativeLibraries) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{5}
}

func (m *UncompressNativeLibraries) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UncompressNativeLibraries.Unmarshal(m, b)
}
func (m *UncompressNativeLibraries) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UncompressNativeLibraries.Marshal(b, m, deterministic)
}
func (m *UncompressNativeLibraries) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UncompressNativeLibraries.Merge(m, src)
}
func (m *UncompressNativeLibraries) XXX_Size() int {
	return xxx_messageInfo_UncompressNativeLibraries.Size(m)
}
func (m *UncompressNativeLibraries) XXX_DiscardUnknown() {
	xxx_messageInfo_UncompressNativeLibraries.DiscardUnknown(m)
}

var xxx_messageInfo_UncompressNativeLibraries proto.InternalMessageInfo

func (m *UncompressNativeLibraries) GetEnabled() bool {
	if m != nil {
		return m.Enabled
	}
	return false
}

type UncompressDexFiles struct {
	Enabled              bool     `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UncompressDexFiles) Reset()         { *m = UncompressDexFiles{} }
func (m *UncompressDexFiles) String() string { return proto.CompactTextString(m) }
func (*UncompressDexFiles) ProtoMessage()    {}
func (*UncompressDexFiles) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{6}
}

func (m *UncompressDexFiles) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UncompressDexFiles.Unmarshal(m, b)
}
func (m *UncompressDexFiles) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UncompressDexFiles.Marshal(b, m, deterministic)
}
func (m *UncompressDexFiles) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UncompressDexFiles.Merge(m, src)
}
func (m *UncompressDexFiles) XXX_Size() int {
	return xxx_messageInfo_UncompressDexFiles.Size(m)
}
func (m *UncompressDexFiles) XXX_DiscardUnknown() {
	xxx_messageInfo_UncompressDexFiles.DiscardUnknown(m)
}

var xxx_messageInfo_UncompressDexFiles proto.InternalMessageInfo

func (m *UncompressDexFiles) GetEnabled() bool {
	if m != nil {
		return m.Enabled
	}
	return false
}

// Optimization configuration used to generate Split APKs.
type SplitsConfig struct {
	SplitDimension       []*SplitDimension `protobuf:"bytes,1,rep,name=split_dimension,json=splitDimension,proto3" json:"split_dimension,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SplitsConfig) Reset()         { *m = SplitsConfig{} }
func (m *SplitsConfig) String() string { return proto.CompactTextString(m) }
func (*SplitsConfig) ProtoMessage()    {}
func (*SplitsConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{7}
}

func (m *SplitsConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SplitsConfig.Unmarshal(m, b)
}
func (m *SplitsConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SplitsConfig.Marshal(b, m, deterministic)
}
func (m *SplitsConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SplitsConfig.Merge(m, src)
}
func (m *SplitsConfig) XXX_Size() int {
	return xxx_messageInfo_SplitsConfig.Size(m)
}
func (m *SplitsConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_SplitsConfig.DiscardUnknown(m)
}

var xxx_messageInfo_SplitsConfig proto.InternalMessageInfo

func (m *SplitsConfig) GetSplitDimension() []*SplitDimension {
	if m != nil {
		return m.SplitDimension
	}
	return nil
}

// Optimization configuration used to generate Standalone APKs.
type StandaloneConfig struct {
	// Device targeting dimensions to shard.
	SplitDimension []*SplitDimension `protobuf:"bytes,1,rep,name=split_dimension,json=splitDimension,proto3" json:"split_dimension,omitempty"`
	// Whether 64 bit libraries should be stripped from Standalone APKs.
	Strip_64BitLibraries bool     `protobuf:"varint,2,opt,name=strip_64_bit_libraries,json=strip64BitLibraries,proto3" json:"strip_64_bit_libraries,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StandaloneConfig) Reset()         { *m = StandaloneConfig{} }
func (m *StandaloneConfig) String() string { return proto.CompactTextString(m) }
func (*StandaloneConfig) ProtoMessage()    {}
func (*StandaloneConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{8}
}

func (m *StandaloneConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StandaloneConfig.Unmarshal(m, b)
}
func (m *StandaloneConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StandaloneConfig.Marshal(b, m, deterministic)
}
func (m *StandaloneConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StandaloneConfig.Merge(m, src)
}
func (m *StandaloneConfig) XXX_Size() int {
	return xxx_messageInfo_StandaloneConfig.Size(m)
}
func (m *StandaloneConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_StandaloneConfig.DiscardUnknown(m)
}

var xxx_messageInfo_StandaloneConfig proto.InternalMessageInfo

func (m *StandaloneConfig) GetSplitDimension() []*SplitDimension {
	if m != nil {
		return m.SplitDimension
	}
	return nil
}

func (m *StandaloneConfig) GetStrip_64BitLibraries() bool {
	if m != nil {
		return m.Strip_64BitLibraries
	}
	return false
}

type SplitDimension struct {
	Value SplitDimension_Value `protobuf:"varint,1,opt,name=value,proto3,enum=android.bundle.SplitDimension_Value" json:"value,omitempty"`
	// If set to 'true', indicates that APKs should *not* be split by this
	// dimension.
	Negate bool `protobuf:"varint,2,opt,name=negate,proto3" json:"negate,omitempty"`
	// Optional transformation to be applied to asset directories where
	// the targeting is encoded in the directory name (e.g: assets/foo#tcf_etc1)
	SuffixStripping      *SuffixStripping `protobuf:"bytes,3,opt,name=suffix_stripping,json=suffixStripping,proto3" json:"suffix_stripping,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SplitDimension) Reset()         { *m = SplitDimension{} }
func (m *SplitDimension) String() string { return proto.CompactTextString(m) }
func (*SplitDimension) ProtoMessage()    {}
func (*SplitDimension) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{9}
}

func (m *SplitDimension) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SplitDimension.Unmarshal(m, b)
}
func (m *SplitDimension) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SplitDimension.Marshal(b, m, deterministic)
}
func (m *SplitDimension) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SplitDimension.Merge(m, src)
}
func (m *SplitDimension) XXX_Size() int {
	return xxx_messageInfo_SplitDimension.Size(m)
}
func (m *SplitDimension) XXX_DiscardUnknown() {
	xxx_messageInfo_SplitDimension.DiscardUnknown(m)
}

var xxx_messageInfo_SplitDimension proto.InternalMessageInfo

func (m *SplitDimension) GetValue() SplitDimension_Value {
	if m != nil {
		return m.Value
	}
	return SplitDimension_UNSPECIFIED_VALUE
}

func (m *SplitDimension) GetNegate() bool {
	if m != nil {
		return m.Negate
	}
	return false
}

func (m *SplitDimension) GetSuffixStripping() *SuffixStripping {
	if m != nil {
		return m.SuffixStripping
	}
	return nil
}

type SuffixStripping struct {
	// If set to 'true', indicates that the targeting suffix should be removed
	// from assets paths for this dimension when splits (or asset slices) are
	// generated.
	// This only applies to assets.
	// For example a folder with path "assets/level1_textures#tcf_etc1"
	// would be outputted to "assets/level1_textures". File contents are
	// unchanged.
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// The default suffix to be used for the cases where separate slices can't
	// be generated for this dimension. In the case of standalone/universal APKs
	// generation, stripping the suffix can lead to file name collisions. This
	// default suffix defines the directories to retain. The others are
	// discarded: standalone/universal APKs will contain only directories
	// targeted at this value for the dimension.
	//
	// If not set or empty, the fallback directory in each directory group will be
	// used (for example, if both "assets/level1_textures#tcf_etc1" and
	// "assets/level1_textures" are present and the default suffix is empty,
	// then only "assets/level1_textures" will be used).
	DefaultSuffix        string   `protobuf:"bytes,2,opt,name=default_suffix,json=defaultSuffix,proto3" json:"default_suffix,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuffixStripping) Reset()         { *m = SuffixStripping{} }
func (m *SuffixStripping) String() string { return proto.CompactTextString(m) }
func (*SuffixStripping) ProtoMessage()    {}
func (*SuffixStripping) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{10}
}

func (m *SuffixStripping) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuffixStripping.Unmarshal(m, b)
}
func (m *SuffixStripping) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuffixStripping.Marshal(b, m, deterministic)
}
func (m *SuffixStripping) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuffixStripping.Merge(m, src)
}
func (m *SuffixStripping) XXX_Size() int {
	return xxx_messageInfo_SuffixStripping.Size(m)
}
func (m *SuffixStripping) XXX_DiscardUnknown() {
	xxx_messageInfo_SuffixStripping.DiscardUnknown(m)
}

var xxx_messageInfo_SuffixStripping proto.InternalMessageInfo

func (m *SuffixStripping) GetEnabled() bool {
	if m != nil {
		return m.Enabled
	}
	return false
}

func (m *SuffixStripping) GetDefaultSuffix() string {
	if m != nil {
		return m.DefaultSuffix
	}
	return ""
}

// Configuration for processing APEX bundles.
// https://source.android.com/devices/tech/ota/apex
type ApexConfig struct {
	// Configuration for processing of APKs embedded in an APEX image.
	ApexEmbeddedApkConfig []*ApexEmbeddedApkConfig `protobuf:"bytes,1,rep,name=apex_embedded_apk_config,json=apexEmbeddedApkConfig,proto3" json:"apex_embedded_apk_config,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                 `json:"-"`
	XXX_unrecognized      []byte                   `json:"-"`
	XXX_sizecache         int32                    `json:"-"`
}

func (m *ApexConfig) Reset()         { *m = ApexConfig{} }
func (m *ApexConfig) String() string { return proto.CompactTextString(m) }
func (*ApexConfig) ProtoMessage()    {}
func (*ApexConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{11}
}

func (m *ApexConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApexConfig.Unmarshal(m, b)
}
func (m *ApexConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApexConfig.Marshal(b, m, deterministic)
}
func (m *ApexConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApexConfig.Merge(m, src)
}
func (m *ApexConfig) XXX_Size() int {
	return xxx_messageInfo_ApexConfig.Size(m)
}
func (m *ApexConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ApexConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ApexConfig proto.InternalMessageInfo

func (m *ApexConfig) GetApexEmbeddedApkConfig() []*ApexEmbeddedApkConfig {
	if m != nil {
		return m.ApexEmbeddedApkConfig
	}
	return nil
}

type ApexEmbeddedApkConfig struct {
	// Android package name of the APK.
	PackageName string `protobuf:"bytes,1,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	// Path to the APK within the APEX system image.
	Path                 string   `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApexEmbeddedApkConfig) Reset()         { *m = ApexEmbeddedApkConfig{} }
func (m *ApexEmbeddedApkConfig) String() string { return proto.CompactTextString(m) }
func (*ApexEmbeddedApkConfig) ProtoMessage()    {}
func (*ApexEmbeddedApkConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{12}
}

func (m *ApexEmbeddedApkConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApexEmbeddedApkConfig.Unmarshal(m, b)
}
func (m *ApexEmbeddedApkConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApexEmbeddedApkConfig.Marshal(b, m, deterministic)
}
func (m *ApexEmbeddedApkConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApexEmbeddedApkConfig.Merge(m, src)
}
func (m *ApexEmbeddedApkConfig) XXX_Size() int {
	return xxx_messageInfo_ApexEmbeddedApkConfig.Size(m)
}
func (m *ApexEmbeddedApkConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ApexEmbeddedApkConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ApexEmbeddedApkConfig proto.InternalMessageInfo

func (m *ApexEmbeddedApkConfig) GetPackageName() string {
	if m != nil {
		return m.PackageName
	}
	return ""
}

func (m *ApexEmbeddedApkConfig) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

type UnsignedEmbeddedApkConfig struct {
	// Path to the APK inside the module (e.g. if the path inside the bundle
	// is split/assets/example.apk, this will be assets/example.apk).
	Path                 string   `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnsignedEmbeddedApkConfig) Reset()         { *m = UnsignedEmbeddedApkConfig{} }
func (m *UnsignedEmbeddedApkConfig) String() string { return proto.CompactTextString(m) }
func (*UnsignedEmbeddedApkConfig) ProtoMessage()    {}
func (*UnsignedEmbeddedApkConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{13}
}

func (m *UnsignedEmbeddedApkConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnsignedEmbeddedApkConfig.Unmarshal(m, b)
}
func (m *UnsignedEmbeddedApkConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnsignedEmbeddedApkConfig.Marshal(b, m, deterministic)
}
func (m *UnsignedEmbeddedApkConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnsignedEmbeddedApkConfig.Merge(m, src)
}
func (m *UnsignedEmbeddedApkConfig) XXX_Size() int {
	return xxx_messageInfo_UnsignedEmbeddedApkConfig.Size(m)
}
func (m *UnsignedEmbeddedApkConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_UnsignedEmbeddedApkConfig.DiscardUnknown(m)
}

var xxx_messageInfo_UnsignedEmbeddedApkConfig proto.InternalMessageInfo

func (m *UnsignedEmbeddedApkConfig) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

type AssetModulesConfig struct {
	// App versionCodes that will be updated with these asset modules.
	// Only relevant for asset-only bundles.
	AppVersion []int64 `protobuf:"varint,1,rep,packed,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	// Version tag for the asset upload.
	// Only relevant for asset-only bundles.
	AssetVersionTag      string   `protobuf:"bytes,2,opt,name=asset_version_tag,json=assetVersionTag,proto3" json:"asset_version_tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AssetModulesConfig) Reset()         { *m = AssetModulesConfig{} }
func (m *AssetModulesConfig) String() string { return proto.CompactTextString(m) }
func (*AssetModulesConfig) ProtoMessage()    {}
func (*AssetModulesConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_3eaf2c85e69e9ea4, []int{14}
}

func (m *AssetModulesConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssetModulesConfig.Unmarshal(m, b)
}
func (m *AssetModulesConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssetModulesConfig.Marshal(b, m, deterministic)
}
func (m *AssetModulesConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssetModulesConfig.Merge(m, src)
}
func (m *AssetModulesConfig) XXX_Size() int {
	return xxx_messageInfo_AssetModulesConfig.Size(m)
}
func (m *AssetModulesConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_AssetModulesConfig.DiscardUnknown(m)
}

var xxx_messageInfo_AssetModulesConfig proto.InternalMessageInfo

func (m *AssetModulesConfig) GetAppVersion() []int64 {
	if m != nil {
		return m.AppVersion
	}
	return nil
}

func (m *AssetModulesConfig) GetAssetVersionTag() string {
	if m != nil {
		return m.AssetVersionTag
	}
	return ""
}

func init() {
	proto.RegisterEnum("android.bundle.BundleConfig_BundleType", BundleConfig_BundleType_name, BundleConfig_BundleType_value)
	proto.RegisterEnum("android.bundle.SplitDimension_Value", SplitDimension_Value_name, SplitDimension_Value_value)
	proto.RegisterType((*BundleConfig)(nil), "android.bundle.BundleConfig")
	proto.RegisterType((*Bundletool)(nil), "android.bundle.Bundletool")
	proto.RegisterType((*Compression)(nil), "android.bundle.Compression")
	proto.RegisterType((*MasterResources)(nil), "android.bundle.MasterResources")
	proto.RegisterType((*Optimizations)(nil), "android.bundle.Optimizations")
	proto.RegisterType((*UncompressNativeLibraries)(nil), "android.bundle.UncompressNativeLibraries")
	proto.RegisterType((*UncompressDexFiles)(nil), "android.bundle.UncompressDexFiles")
	proto.RegisterType((*SplitsConfig)(nil), "android.bundle.SplitsConfig")
	proto.RegisterType((*StandaloneConfig)(nil), "android.bundle.StandaloneConfig")
	proto.RegisterType((*SplitDimension)(nil), "android.bundle.SplitDimension")
	proto.RegisterType((*SuffixStripping)(nil), "android.bundle.SuffixStripping")
	proto.RegisterType((*ApexConfig)(nil), "android.bundle.ApexConfig")
	proto.RegisterType((*ApexEmbeddedApkConfig)(nil), "android.bundle.ApexEmbeddedApkConfig")
	proto.RegisterType((*UnsignedEmbeddedApkConfig)(nil), "android.bundle.UnsignedEmbeddedApkConfig")
	proto.RegisterType((*AssetModulesConfig)(nil), "android.bundle.AssetModulesConfig")
}

func init() {
	proto.RegisterFile("config.proto", fileDescriptor_3eaf2c85e69e9ea4)
}

var fileDescriptor_3eaf2c85e69e9ea4 = []byte{
	// 1001 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0xdb, 0x6e, 0xdb, 0x46,
	0x10, 0x0d, 0x75, 0xb1, 0xe5, 0x91, 0x2c, 0xd1, 0xdb, 0x38, 0x50, 0x2e, 0x4d, 0x5c, 0xa2, 0x41,
	0xdd, 0xb4, 0x50, 0x01, 0x3b, 0xcd, 0x83, 0x83, 0x3e, 0xd0, 0x32, 0xad, 0x2a, 0xd0, 0x0d, 0x4b,
	0xc9, 0x4d, 0x5a, 0xa0, 0x8b, 0x95, 0xb8, 0x52, 0xb7, 0xa6, 0x48, 0x82, 0x4b, 0x1a, 0x4a, 0xfb,
	0x09, 0x7d, 0xe9, 0x8f, 0xf4, 0xa7, 0xfa, 0x25, 0x05, 0x97, 0xa4, 0x2c, 0x51, 0x52, 0x9e, 0xfa,
	0x24, 0xce, 0xec, 0x39, 0xb3, 0x3b, 0xb3, 0x67, 0x67, 0x04, 0x95, 0x89, 0xeb, 0x4c, 0xf9, 0xac,
	0xe1, 0xf9, 0x6e, 0xe0, 0xa2, 0x2a, 0x75, 0x2c, 0xdf, 0xe5, 0x56, 0x63, 0x1c, 0x3a, 0x96, 0xcd,
	0xb4, 0xbf, 0x8a, 0x50, 0xb9, 0x94, 0x9f, 0x4d, 0x09, 0x43, 0x17, 0x00, 0xf1, 0x52, 0xe0, 0xba,
	0x76, 0x5d, 0x39, 0x51, 0x4e, 0xcb, 0x67, 0x4f, 0x1a, 0xeb, 0xac, 0xc6, 0xe5, 0x12, 0x81, 0x57,
	0xd0, 0xa8, 0x09, 0x87, 0xae, 0x17, 0xf0, 0x39, 0xff, 0x83, 0x06, 0xdc, 0x75, 0x44, 0x3d, 0x27,
	0xe9, 0x9f, 0x67, 0xe9, 0xfd, 0x55, 0x10, 0x5e, 0xe7, 0xa0, 0x1f, 0xa0, 0x3c, 0x71, 0xe7, 0x9e,
	0xcf, 0x84, 0xe0, 0xae, 0x53, 0xcf, 0xcb, 0x10, 0x4f, 0xb3, 0x21, 0x9a, 0xf7, 0x10, 0xbc, 0x8a,
	0x47, 0xef, 0x40, 0x9d, 0x53, 0x11, 0x30, 0x9f, 0xf8, 0x4c, 0xb8, 0xa1, 0x3f, 0x61, 0xa2, 0x5e,
	0x90, 0x31, 0x5e, 0x64, 0x63, 0x74, 0x25, 0x0e, 0xa7, 0x30, 0x5c, 0x9b, 0xaf, 0x3b, 0xd0, 0x5b,
	0x28, 0x53, 0x8f, 0x2d, 0x48, 0x5c, 0xc1, 0x7a, 0x71, 0x7b, 0x31, 0x74, 0x8f, 0x2d, 0xe2, 0xe2,
	0x61, 0xa0, 0xcb, 0x6f, 0xf4, 0x3b, 0x3c, 0x0b, 0x1d, 0xc1, 0x67, 0x0e, 0xb3, 0x08, 0x9b, 0x8f,
	0x99, 0x65, 0x31, 0x8b, 0x50, 0xef, 0x36, 0x8d, 0xb6, 0x77, 0x92, 0x3f, 0x2d, 0x9f, 0x7d, 0x9d,
	0x8d, 0x36, 0x4a, 0x38, 0x46, 0x42, 0xd1, 0xbd, 0xdb, 0x24, 0xf8, 0xe3, 0x70, 0xd7, 0x12, 0x1a,
	0xc2, 0x43, 0x2a, 0x04, 0x0b, 0xc8, 0xdc, 0xb5, 0x42, 0x9b, 0x89, 0x74, 0x8f, 0x7d, 0x79, 0x62,
	0x6d, 0xe3, 0xc4, 0x11, 0xb6, 0x1b, 0x43, 0x93, 0xe0, 0x88, 0x6e, 0xf8, 0xd0, 0x5b, 0x28, 0x04,
	0x1f, 0x3d, 0x56, 0x2f, 0x9d, 0x28, 0xa7, 0xd5, 0xb3, 0xaf, 0xb6, 0x8b, 0x20, 0xc6, 0x26, 0xc6,
	0xf0, 0xa3, 0xc7, 0xb0, 0x24, 0x69, 0xe7, 0x00, 0xf7, 0x3e, 0x54, 0x86, 0x7d, 0x6c, 0xb4, 0x46,
	0x1d, 0x1d, 0xab, 0x0f, 0x50, 0x09, 0x0a, 0xfa, 0xc0, 0x78, 0xaf, 0x2a, 0xa8, 0x0a, 0xa0, 0x9b,
	0xa6, 0x31, 0x24, 0xfd, 0x5e, 0xe7, 0x83, 0x9a, 0xd3, 0xbe, 0x4d, 0x49, 0x52, 0x4e, 0x75, 0xd8,
	0xbf, 0x63, 0xbe, 0x54, 0x41, 0x24, 0xa4, 0x03, 0x9c, 0x9a, 0xef, 0x0a, 0x25, 0x45, 0xcd, 0x69,
	0x17, 0x50, 0x5e, 0x91, 0x01, 0xfa, 0x06, 0x8e, 0x42, 0x27, 0x95, 0x02, 0xb3, 0xc8, 0xcc, 0x76,
	0xc7, 0x75, 0xe5, 0x24, 0x7f, 0x7a, 0x80, 0xd5, 0xd5, 0x85, 0x96, 0xed, 0x8e, 0xb5, 0x5f, 0xa0,
	0x96, 0xb9, 0x7e, 0xf4, 0x05, 0x54, 0x52, 0xc9, 0x10, 0x6e, 0x09, 0x49, 0x2d, 0xe2, 0x72, 0xea,
	0x6b, 0x5b, 0x02, 0xbd, 0x84, 0xea, 0x12, 0xe2, 0xd0, 0x39, 0x8b, 0x14, 0x1e, 0xc5, 0x3f, 0x4c,
	0xbd, 0xbd, 0xc8, 0xa9, 0xfd, 0x9b, 0x83, 0xc3, 0x35, 0x8d, 0x23, 0x1d, 0x0e, 0x85, 0x67, 0xf3,
	0x60, 0x79, 0x33, 0xf1, 0xc3, 0x7a, 0x96, 0xad, 0xa9, 0x29, 0x41, 0xc9, 0x9d, 0x54, 0xc4, 0x8a,
	0x85, 0x38, 0x3c, 0xbd, 0xcf, 0x82, 0x38, 0x34, 0xe0, 0x77, 0x8c, 0xd8, 0x7c, 0xec, 0x53, 0x9f,
	0xb3, 0xf4, 0xa9, 0x6d, 0x91, 0x53, 0x4a, 0xe9, 0x49, 0x46, 0x27, 0x25, 0x44, 0x72, 0xda, 0xb1,
	0x14, 0xc9, 0x69, 0x65, 0x2b, 0x8b, 0x2d, 0xc8, 0x94, 0xdb, 0x4c, 0x24, 0x6f, 0x51, 0xdb, 0xbd,
	0xc7, 0x15, 0x5b, 0x5c, 0x47, 0x48, 0x8c, 0xc2, 0x0d, 0x1f, 0xea, 0xc2, 0x91, 0x08, 0xa8, 0x63,
	0x51, 0xdb, 0x75, 0x58, 0x5a, 0x87, 0xf8, 0x69, 0x9e, 0x6c, 0xd4, 0x61, 0x09, 0x4c, 0x6a, 0xa1,
	0x8a, 0x8c, 0x47, 0xfb, 0x1e, 0x1e, 0xef, 0x4c, 0x2e, 0x92, 0x0e, 0x73, 0xe8, 0xd8, 0x66, 0x96,
	0xac, 0x74, 0x09, 0xa7, 0xa6, 0xd6, 0x00, 0xb4, 0x79, 0xde, 0x4f, 0xe0, 0x7f, 0x82, 0xca, 0xea,
	0xa5, 0xa0, 0x16, 0xd4, 0xe4, 0xb5, 0x10, 0x8b, 0xcf, 0x99, 0x23, 0xc5, 0xa9, 0xc8, 0x97, 0xfc,
	0x7c, 0xeb, 0x5d, 0x5e, 0xa5, 0x28, 0x5c, 0x15, 0x6b, 0xb6, 0xf6, 0xb7, 0x02, 0x6a, 0x36, 0xcd,
	0xff, 0x2d, 0x3a, 0x3a, 0x87, 0x47, 0x22, 0xf0, 0xb9, 0x47, 0xde, 0xbc, 0x26, 0x63, 0x1e, 0x64,
	0x84, 0x52, 0xc2, 0x9f, 0xc9, 0xd5, 0x37, 0xaf, 0x2f, 0x79, 0xb0, 0xac, 0x9a, 0xf6, 0x4f, 0x0e,
	0xaa, 0xeb, 0x71, 0xd1, 0x05, 0x14, 0xef, 0xa8, 0x1d, 0x32, 0x59, 0x96, 0xea, 0xd9, 0x97, 0x9f,
	0x3e, 0x46, 0xe3, 0x26, 0xc2, 0xe2, 0x98, 0x82, 0x1e, 0xc1, 0x9e, 0xc3, 0x66, 0x34, 0x60, 0xc9,
	0x9e, 0x89, 0x15, 0xb5, 0x68, 0x11, 0x4e, 0xa7, 0x7c, 0x41, 0xe4, 0x21, 0x3c, 0xee, 0xcc, 0x12,
	0x69, 0x6d, 0xb4, 0x68, 0x53, 0xe2, 0xcc, 0x14, 0x86, 0x6b, 0x62, 0xdd, 0xa1, 0xfd, 0x09, 0x45,
	0xb9, 0x27, 0x3a, 0x86, 0xa3, 0x51, 0xcf, 0x1c, 0x18, 0xcd, 0xf6, 0x75, 0xdb, 0xb8, 0x22, 0x37,
	0x7a, 0x67, 0x64, 0xa8, 0x0f, 0xd0, 0x3e, 0xe4, 0xf5, 0xcb, 0xb6, 0xaa, 0x20, 0x04, 0x55, 0xb3,
	0x89, 0x0d, 0xa3, 0x47, 0xae, 0x8c, 0x9e, 0xd9, 0x1e, 0x7e, 0x50, 0x73, 0xa8, 0x02, 0xa5, 0x8e,
	0xde, 0x6b, 0x8d, 0xf4, 0x96, 0xa1, 0xe6, 0xd1, 0x73, 0x78, 0x32, 0x34, 0xde, 0x0f, 0x47, 0xd8,
	0x20, 0xcd, 0x7e, 0x77, 0x80, 0x0d, 0xd3, 0x6c, 0xf7, 0x7b, 0xe4, 0xba, 0x8f, 0xbb, 0xfa, 0x50,
	0x2d, 0x20, 0x15, 0x2a, 0x2d, 0xac, 0x0f, 0x7e, 0x6c, 0x37, 0x4d, 0xa2, 0x0f, 0xda, 0x6a, 0x51,
	0xc3, 0x50, 0xcb, 0x1c, 0x70, 0xb7, 0x90, 0xa2, 0xde, 0x61, 0xb1, 0x29, 0x0d, 0xed, 0x80, 0xc4,
	0x49, 0x24, 0x4d, 0xed, 0x30, 0xf1, 0xc6, 0x91, 0x34, 0x1b, 0xe0, 0x7e, 0xa0, 0xa0, 0x5f, 0xa1,
	0x2e, 0x27, 0xd0, 0xb6, 0x01, 0x12, 0x0b, 0xe3, 0xe5, 0xb6, 0x71, 0xb4, 0x39, 0x3c, 0x8e, 0xe9,
	0x36, 0xb7, 0xd6, 0x83, 0xe3, 0xad, 0xf8, 0xa8, 0x19, 0x7a, 0x74, 0x72, 0x4b, 0x67, 0x71, 0xa3,
	0x93, 0xc9, 0x1c, 0xe0, 0x72, 0xe2, 0x8b, 0xda, 0x1c, 0x42, 0x50, 0xf0, 0x68, 0xf0, 0x5b, 0x92,
	0x86, 0xfc, 0xd6, 0xbe, 0x8b, 0x1e, 0xe5, 0xae, 0x29, 0x95, 0x12, 0x94, 0x15, 0x02, 0x05, 0xb4,
	0x39, 0x8d, 0xd0, 0x8b, 0x68, 0xf0, 0x7a, 0x24, 0xed, 0xfe, 0x51, 0xa6, 0xf9, 0x68, 0xb8, 0x7a,
	0x37, 0xb1, 0x07, 0xbd, 0x82, 0xa3, 0x78, 0xe0, 0x25, 0x10, 0x12, 0xd0, 0x59, 0x72, 0x90, 0x9a,
	0x5c, 0x48, 0x80, 0x43, 0x3a, 0xbb, 0x7c, 0x05, 0x68, 0xe2, 0xce, 0x33, 0x65, 0xfa, 0xf9, 0x61,
	0x62, 0x93, 0xd8, 0x26, 0xf2, 0xef, 0xd1, 0x78, 0x4f, 0xfe, 0x9c, 0xff, 0x17, 0x00, 0x00, 0xff,
	0xff, 0x6b, 0x05, 0xbf, 0x99, 0x35, 0x09, 0x00, 0x00,
}
