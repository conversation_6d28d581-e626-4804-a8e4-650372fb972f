// Code generated by protoc-gen-go. DO NOT EDIT.
// source: commands.proto

package android_bundle_proto

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type DeliveryType int32

const (
	DeliveryType_UNKNOWN_DELIVERY_TYPE DeliveryType = 0
	DeliveryType_INSTALL_TIME          DeliveryType = 1
	DeliveryType_ON_DEMAND             DeliveryType = 2
	DeliveryType_FAST_FOLLOW           DeliveryType = 3
)

var DeliveryType_name = map[int32]string{
	0: "UNKNOWN_DELIVERY_TYPE",
	1: "INSTALL_TIME",
	2: "ON_DEMAND",
	3: "FAST_FOLLOW",
}

var DeliveryType_value = map[string]int32{
	"UNKNOWN_DELIVERY_TYPE": 0,
	"INSTALL_TIME":          1,
	"ON_DEMAND":             2,
	"FAST_FOLLOW":           3,
}

func (x DeliveryType) String() string {
	return proto.EnumName(DeliveryType_name, int32(x))
}

func (DeliveryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{0}
}

type SystemApkMetadata_SystemApkType int32

const (
	SystemApkMetadata_UNSPECIFIED_VALUE SystemApkMetadata_SystemApkType = 0
	// Uncompressed APK for system image.
	SystemApkMetadata_SYSTEM SystemApkMetadata_SystemApkType = 1
	// Stub APK for compressed APK in the system image
	// (contains only android manifest).
	SystemApkMetadata_SYSTEM_STUB SystemApkMetadata_SystemApkType = 2
	// Compressed APK for system image.
	SystemApkMetadata_SYSTEM_COMPRESSED SystemApkMetadata_SystemApkType = 3
)

var SystemApkMetadata_SystemApkType_name = map[int32]string{
	0: "UNSPECIFIED_VALUE",
	1: "SYSTEM",
	2: "SYSTEM_STUB",
	3: "SYSTEM_COMPRESSED",
}

var SystemApkMetadata_SystemApkType_value = map[string]int32{
	"UNSPECIFIED_VALUE": 0,
	"SYSTEM":            1,
	"SYSTEM_STUB":       2,
	"SYSTEM_COMPRESSED": 3,
}

func (x SystemApkMetadata_SystemApkType) String() string {
	return proto.EnumName(SystemApkMetadata_SystemApkType_name, int32(x))
}

func (SystemApkMetadata_SystemApkType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{10, 0}
}

// Describes the output of the "build-apks" command.
type BuildApksResult struct {
	// The package name of this app.
	PackageName string `protobuf:"bytes,4,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	// List of the created variants.
	Variant []*Variant `protobuf:"bytes,1,rep,name=variant,proto3" json:"variant,omitempty"`
	// Metadata about BundleTool used to build the APKs.
	Bundletool *Bundletool `protobuf:"bytes,2,opt,name=bundletool,proto3" json:"bundletool,omitempty"`
	// List of the created asset slices.
	AssetSliceSet []*AssetSliceSet `protobuf:"bytes,3,rep,name=asset_slice_set,json=assetSliceSet,proto3" json:"asset_slice_set,omitempty"`
	// Information about local testing mode.
	LocalTestingInfo     *LocalTestingInfo `protobuf:"bytes,5,opt,name=local_testing_info,json=localTestingInfo,proto3" json:"local_testing_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BuildApksResult) Reset()         { *m = BuildApksResult{} }
func (m *BuildApksResult) String() string { return proto.CompactTextString(m) }
func (*BuildApksResult) ProtoMessage()    {}
func (*BuildApksResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{0}
}

func (m *BuildApksResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuildApksResult.Unmarshal(m, b)
}
func (m *BuildApksResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuildApksResult.Marshal(b, m, deterministic)
}
func (m *BuildApksResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuildApksResult.Merge(m, src)
}
func (m *BuildApksResult) XXX_Size() int {
	return xxx_messageInfo_BuildApksResult.Size(m)
}
func (m *BuildApksResult) XXX_DiscardUnknown() {
	xxx_messageInfo_BuildApksResult.DiscardUnknown(m)
}

var xxx_messageInfo_BuildApksResult proto.InternalMessageInfo

func (m *BuildApksResult) GetPackageName() string {
	if m != nil {
		return m.PackageName
	}
	return ""
}

func (m *BuildApksResult) GetVariant() []*Variant {
	if m != nil {
		return m.Variant
	}
	return nil
}

func (m *BuildApksResult) GetBundletool() *Bundletool {
	if m != nil {
		return m.Bundletool
	}
	return nil
}

func (m *BuildApksResult) GetAssetSliceSet() []*AssetSliceSet {
	if m != nil {
		return m.AssetSliceSet
	}
	return nil
}

func (m *BuildApksResult) GetLocalTestingInfo() *LocalTestingInfo {
	if m != nil {
		return m.LocalTestingInfo
	}
	return nil
}

// Variant is a group of APKs that covers a part of the device configuration
// space. APKs from multiple variants are never combined on one device.
type Variant struct {
	// Variant-level targeting.
	// This targeting is fairly high-level and each APK has its own targeting as
	// well.
	Targeting *VariantTargeting `protobuf:"bytes,1,opt,name=targeting,proto3" json:"targeting,omitempty"`
	// Set of APKs, one set per module.
	ApkSet []*ApkSet `protobuf:"bytes,2,rep,name=apk_set,json=apkSet,proto3" json:"apk_set,omitempty"`
	// Number of the variant, starting at 0 (unless overridden).
	// A device will receive APKs from the first variant that matches the device
	// configuration, with higher variant numbers having priority over lower
	// variant numbers.
	VariantNumber        uint32   `protobuf:"varint,3,opt,name=variant_number,json=variantNumber,proto3" json:"variant_number,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Variant) Reset()         { *m = Variant{} }
func (m *Variant) String() string { return proto.CompactTextString(m) }
func (*Variant) ProtoMessage()    {}
func (*Variant) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{1}
}

func (m *Variant) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Variant.Unmarshal(m, b)
}
func (m *Variant) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Variant.Marshal(b, m, deterministic)
}
func (m *Variant) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Variant.Merge(m, src)
}
func (m *Variant) XXX_Size() int {
	return xxx_messageInfo_Variant.Size(m)
}
func (m *Variant) XXX_DiscardUnknown() {
	xxx_messageInfo_Variant.DiscardUnknown(m)
}

var xxx_messageInfo_Variant proto.InternalMessageInfo

func (m *Variant) GetTargeting() *VariantTargeting {
	if m != nil {
		return m.Targeting
	}
	return nil
}

func (m *Variant) GetApkSet() []*ApkSet {
	if m != nil {
		return m.ApkSet
	}
	return nil
}

func (m *Variant) GetVariantNumber() uint32 {
	if m != nil {
		return m.VariantNumber
	}
	return 0
}

// Represents a module.
// For pre-L devices multiple modules (possibly all) may be merged into one.
type ApkSet struct {
	ModuleMetadata *ModuleMetadata `protobuf:"bytes,1,opt,name=module_metadata,json=moduleMetadata,proto3" json:"module_metadata,omitempty"`
	// APKs.
	ApkDescription       []*ApkDescription `protobuf:"bytes,2,rep,name=apk_description,json=apkDescription,proto3" json:"apk_description,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ApkSet) Reset()         { *m = ApkSet{} }
func (m *ApkSet) String() string { return proto.CompactTextString(m) }
func (*ApkSet) ProtoMessage()    {}
func (*ApkSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{2}
}

func (m *ApkSet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApkSet.Unmarshal(m, b)
}
func (m *ApkSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApkSet.Marshal(b, m, deterministic)
}
func (m *ApkSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApkSet.Merge(m, src)
}
func (m *ApkSet) XXX_Size() int {
	return xxx_messageInfo_ApkSet.Size(m)
}
func (m *ApkSet) XXX_DiscardUnknown() {
	xxx_messageInfo_ApkSet.DiscardUnknown(m)
}

var xxx_messageInfo_ApkSet proto.InternalMessageInfo

func (m *ApkSet) GetModuleMetadata() *ModuleMetadata {
	if m != nil {
		return m.ModuleMetadata
	}
	return nil
}

func (m *ApkSet) GetApkDescription() []*ApkDescription {
	if m != nil {
		return m.ApkDescription
	}
	return nil
}

type ModuleMetadata struct {
	// Module name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Indicates the delivery type (e.g. on-demand) of the module.
	DeliveryType DeliveryType `protobuf:"varint,6,opt,name=delivery_type,json=deliveryType,proto3,enum=android.bundle.DeliveryType" json:"delivery_type,omitempty"`
	// Indicates whether this module is marked "instant".
	IsInstant bool `protobuf:"varint,3,opt,name=is_instant,json=isInstant,proto3" json:"is_instant,omitempty"`
	// Names of the modules that this module directly depends on.
	// Each module implicitly depends on the base module.
	Dependencies []string `protobuf:"bytes,4,rep,name=dependencies,proto3" json:"dependencies,omitempty"`
	// The targeting that makes a conditional module installed.
	// Relevant only for Split APKs.
	Targeting *ModuleTargeting `protobuf:"bytes,5,opt,name=targeting,proto3" json:"targeting,omitempty"`
	// Deprecated. Please use delivery_type.
	OnDemandDeprecated   bool     `protobuf:"varint,2,opt,name=on_demand_deprecated,json=onDemandDeprecated,proto3" json:"on_demand_deprecated,omitempty"` // Deprecated: Do not use.
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModuleMetadata) Reset()         { *m = ModuleMetadata{} }
func (m *ModuleMetadata) String() string { return proto.CompactTextString(m) }
func (*ModuleMetadata) ProtoMessage()    {}
func (*ModuleMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{3}
}

func (m *ModuleMetadata) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleMetadata.Unmarshal(m, b)
}
func (m *ModuleMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleMetadata.Marshal(b, m, deterministic)
}
func (m *ModuleMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleMetadata.Merge(m, src)
}
func (m *ModuleMetadata) XXX_Size() int {
	return xxx_messageInfo_ModuleMetadata.Size(m)
}
func (m *ModuleMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleMetadata proto.InternalMessageInfo

func (m *ModuleMetadata) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ModuleMetadata) GetDeliveryType() DeliveryType {
	if m != nil {
		return m.DeliveryType
	}
	return DeliveryType_UNKNOWN_DELIVERY_TYPE
}

func (m *ModuleMetadata) GetIsInstant() bool {
	if m != nil {
		return m.IsInstant
	}
	return false
}

func (m *ModuleMetadata) GetDependencies() []string {
	if m != nil {
		return m.Dependencies
	}
	return nil
}

func (m *ModuleMetadata) GetTargeting() *ModuleTargeting {
	if m != nil {
		return m.Targeting
	}
	return nil
}

// Deprecated: Do not use.
func (m *ModuleMetadata) GetOnDemandDeprecated() bool {
	if m != nil {
		return m.OnDemandDeprecated
	}
	return false
}

// Set of asset slices belonging to a single asset module.
type AssetSliceSet struct {
	// Module level metadata.
	AssetModuleMetadata *AssetModuleMetadata `protobuf:"bytes,1,opt,name=asset_module_metadata,json=assetModuleMetadata,proto3" json:"asset_module_metadata,omitempty"`
	// Asset slices.
	ApkDescription       []*ApkDescription `protobuf:"bytes,2,rep,name=apk_description,json=apkDescription,proto3" json:"apk_description,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AssetSliceSet) Reset()         { *m = AssetSliceSet{} }
func (m *AssetSliceSet) String() string { return proto.CompactTextString(m) }
func (*AssetSliceSet) ProtoMessage()    {}
func (*AssetSliceSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{4}
}

func (m *AssetSliceSet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssetSliceSet.Unmarshal(m, b)
}
func (m *AssetSliceSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssetSliceSet.Marshal(b, m, deterministic)
}
func (m *AssetSliceSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssetSliceSet.Merge(m, src)
}
func (m *AssetSliceSet) XXX_Size() int {
	return xxx_messageInfo_AssetSliceSet.Size(m)
}
func (m *AssetSliceSet) XXX_DiscardUnknown() {
	xxx_messageInfo_AssetSliceSet.DiscardUnknown(m)
}

var xxx_messageInfo_AssetSliceSet proto.InternalMessageInfo

func (m *AssetSliceSet) GetAssetModuleMetadata() *AssetModuleMetadata {
	if m != nil {
		return m.AssetModuleMetadata
	}
	return nil
}

func (m *AssetSliceSet) GetApkDescription() []*ApkDescription {
	if m != nil {
		return m.ApkDescription
	}
	return nil
}

type AssetModuleMetadata struct {
	// Module name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Indicates the delivery type for persistent install.
	DeliveryType DeliveryType `protobuf:"varint,4,opt,name=delivery_type,json=deliveryType,proto3,enum=android.bundle.DeliveryType" json:"delivery_type,omitempty"`
	// Metadata for instant installs.
	InstantMetadata *InstantMetadata `protobuf:"bytes,3,opt,name=instant_metadata,json=instantMetadata,proto3" json:"instant_metadata,omitempty"`
	// Deprecated. Use delivery_type.
	OnDemandDeprecated   bool     `protobuf:"varint,2,opt,name=on_demand_deprecated,json=onDemandDeprecated,proto3" json:"on_demand_deprecated,omitempty"` // Deprecated: Do not use.
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AssetModuleMetadata) Reset()         { *m = AssetModuleMetadata{} }
func (m *AssetModuleMetadata) String() string { return proto.CompactTextString(m) }
func (*AssetModuleMetadata) ProtoMessage()    {}
func (*AssetModuleMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{5}
}

func (m *AssetModuleMetadata) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssetModuleMetadata.Unmarshal(m, b)
}
func (m *AssetModuleMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssetModuleMetadata.Marshal(b, m, deterministic)
}
func (m *AssetModuleMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssetModuleMetadata.Merge(m, src)
}
func (m *AssetModuleMetadata) XXX_Size() int {
	return xxx_messageInfo_AssetModuleMetadata.Size(m)
}
func (m *AssetModuleMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_AssetModuleMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_AssetModuleMetadata proto.InternalMessageInfo

func (m *AssetModuleMetadata) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AssetModuleMetadata) GetDeliveryType() DeliveryType {
	if m != nil {
		return m.DeliveryType
	}
	return DeliveryType_UNKNOWN_DELIVERY_TYPE
}

func (m *AssetModuleMetadata) GetInstantMetadata() *InstantMetadata {
	if m != nil {
		return m.InstantMetadata
	}
	return nil
}

// Deprecated: Do not use.
func (m *AssetModuleMetadata) GetOnDemandDeprecated() bool {
	if m != nil {
		return m.OnDemandDeprecated
	}
	return false
}

type InstantMetadata struct {
	// Indicates whether this module is marked "instant".
	IsInstant bool `protobuf:"varint,1,opt,name=is_instant,json=isInstant,proto3" json:"is_instant,omitempty"`
	// Indicates the delivery type for instant install.
	DeliveryType DeliveryType `protobuf:"varint,3,opt,name=delivery_type,json=deliveryType,proto3,enum=android.bundle.DeliveryType" json:"delivery_type,omitempty"`
	// Deprecated. Use delivery_type.
	OnDemandDeprecated   bool     `protobuf:"varint,2,opt,name=on_demand_deprecated,json=onDemandDeprecated,proto3" json:"on_demand_deprecated,omitempty"` // Deprecated: Do not use.
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InstantMetadata) Reset()         { *m = InstantMetadata{} }
func (m *InstantMetadata) String() string { return proto.CompactTextString(m) }
func (*InstantMetadata) ProtoMessage()    {}
func (*InstantMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{6}
}

func (m *InstantMetadata) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InstantMetadata.Unmarshal(m, b)
}
func (m *InstantMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InstantMetadata.Marshal(b, m, deterministic)
}
func (m *InstantMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InstantMetadata.Merge(m, src)
}
func (m *InstantMetadata) XXX_Size() int {
	return xxx_messageInfo_InstantMetadata.Size(m)
}
func (m *InstantMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_InstantMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_InstantMetadata proto.InternalMessageInfo

func (m *InstantMetadata) GetIsInstant() bool {
	if m != nil {
		return m.IsInstant
	}
	return false
}

func (m *InstantMetadata) GetDeliveryType() DeliveryType {
	if m != nil {
		return m.DeliveryType
	}
	return DeliveryType_UNKNOWN_DELIVERY_TYPE
}

// Deprecated: Do not use.
func (m *InstantMetadata) GetOnDemandDeprecated() bool {
	if m != nil {
		return m.OnDemandDeprecated
	}
	return false
}

type ApkDescription struct {
	Targeting *ApkTargeting `protobuf:"bytes,1,opt,name=targeting,proto3" json:"targeting,omitempty"`
	// Path to the APK file.
	// BEGIN-INTERNAL
	// The path may be a blobkey if the proto is not constructed by bundletool.
	// END-INTERNAL
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	// Types that are valid to be assigned to ApkMetadataOneofValue:
	//	*ApkDescription_SplitApkMetadata
	//	*ApkDescription_StandaloneApkMetadata
	//	*ApkDescription_InstantApkMetadata
	//	*ApkDescription_SystemApkMetadata
	//	*ApkDescription_AssetSliceMetadata
	//	*ApkDescription_ApexApkMetadata
	ApkMetadataOneofValue isApkDescription_ApkMetadataOneofValue `protobuf_oneof:"apk_metadata_oneof_value"`
	XXX_NoUnkeyedLiteral  struct{}                               `json:"-"`
	XXX_unrecognized      []byte                                 `json:"-"`
	XXX_sizecache         int32                                  `json:"-"`
}

func (m *ApkDescription) Reset()         { *m = ApkDescription{} }
func (m *ApkDescription) String() string { return proto.CompactTextString(m) }
func (*ApkDescription) ProtoMessage()    {}
func (*ApkDescription) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{7}
}

func (m *ApkDescription) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApkDescription.Unmarshal(m, b)
}
func (m *ApkDescription) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApkDescription.Marshal(b, m, deterministic)
}
func (m *ApkDescription) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApkDescription.Merge(m, src)
}
func (m *ApkDescription) XXX_Size() int {
	return xxx_messageInfo_ApkDescription.Size(m)
}
func (m *ApkDescription) XXX_DiscardUnknown() {
	xxx_messageInfo_ApkDescription.DiscardUnknown(m)
}

var xxx_messageInfo_ApkDescription proto.InternalMessageInfo

func (m *ApkDescription) GetTargeting() *ApkTargeting {
	if m != nil {
		return m.Targeting
	}
	return nil
}

func (m *ApkDescription) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

type isApkDescription_ApkMetadataOneofValue interface {
	isApkDescription_ApkMetadataOneofValue()
}

type ApkDescription_SplitApkMetadata struct {
	SplitApkMetadata *SplitApkMetadata `protobuf:"bytes,3,opt,name=split_apk_metadata,json=splitApkMetadata,proto3,oneof"`
}

type ApkDescription_StandaloneApkMetadata struct {
	StandaloneApkMetadata *StandaloneApkMetadata `protobuf:"bytes,4,opt,name=standalone_apk_metadata,json=standaloneApkMetadata,proto3,oneof"`
}

type ApkDescription_InstantApkMetadata struct {
	InstantApkMetadata *SplitApkMetadata `protobuf:"bytes,5,opt,name=instant_apk_metadata,json=instantApkMetadata,proto3,oneof"`
}

type ApkDescription_SystemApkMetadata struct {
	SystemApkMetadata *SystemApkMetadata `protobuf:"bytes,6,opt,name=system_apk_metadata,json=systemApkMetadata,proto3,oneof"`
}

type ApkDescription_AssetSliceMetadata struct {
	AssetSliceMetadata *SplitApkMetadata `protobuf:"bytes,7,opt,name=asset_slice_metadata,json=assetSliceMetadata,proto3,oneof"`
}

type ApkDescription_ApexApkMetadata struct {
	ApexApkMetadata *ApexApkMetadata `protobuf:"bytes,8,opt,name=apex_apk_metadata,json=apexApkMetadata,proto3,oneof"`
}

func (*ApkDescription_SplitApkMetadata) isApkDescription_ApkMetadataOneofValue() {}

func (*ApkDescription_StandaloneApkMetadata) isApkDescription_ApkMetadataOneofValue() {}

func (*ApkDescription_InstantApkMetadata) isApkDescription_ApkMetadataOneofValue() {}

func (*ApkDescription_SystemApkMetadata) isApkDescription_ApkMetadataOneofValue() {}

func (*ApkDescription_AssetSliceMetadata) isApkDescription_ApkMetadataOneofValue() {}

func (*ApkDescription_ApexApkMetadata) isApkDescription_ApkMetadataOneofValue() {}

func (m *ApkDescription) GetApkMetadataOneofValue() isApkDescription_ApkMetadataOneofValue {
	if m != nil {
		return m.ApkMetadataOneofValue
	}
	return nil
}

func (m *ApkDescription) GetSplitApkMetadata() *SplitApkMetadata {
	if x, ok := m.GetApkMetadataOneofValue().(*ApkDescription_SplitApkMetadata); ok {
		return x.SplitApkMetadata
	}
	return nil
}

func (m *ApkDescription) GetStandaloneApkMetadata() *StandaloneApkMetadata {
	if x, ok := m.GetApkMetadataOneofValue().(*ApkDescription_StandaloneApkMetadata); ok {
		return x.StandaloneApkMetadata
	}
	return nil
}

func (m *ApkDescription) GetInstantApkMetadata() *SplitApkMetadata {
	if x, ok := m.GetApkMetadataOneofValue().(*ApkDescription_InstantApkMetadata); ok {
		return x.InstantApkMetadata
	}
	return nil
}

func (m *ApkDescription) GetSystemApkMetadata() *SystemApkMetadata {
	if x, ok := m.GetApkMetadataOneofValue().(*ApkDescription_SystemApkMetadata); ok {
		return x.SystemApkMetadata
	}
	return nil
}

func (m *ApkDescription) GetAssetSliceMetadata() *SplitApkMetadata {
	if x, ok := m.GetApkMetadataOneofValue().(*ApkDescription_AssetSliceMetadata); ok {
		return x.AssetSliceMetadata
	}
	return nil
}

func (m *ApkDescription) GetApexApkMetadata() *ApexApkMetadata {
	if x, ok := m.GetApkMetadataOneofValue().(*ApkDescription_ApexApkMetadata); ok {
		return x.ApexApkMetadata
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*ApkDescription) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*ApkDescription_SplitApkMetadata)(nil),
		(*ApkDescription_StandaloneApkMetadata)(nil),
		(*ApkDescription_InstantApkMetadata)(nil),
		(*ApkDescription_SystemApkMetadata)(nil),
		(*ApkDescription_AssetSliceMetadata)(nil),
		(*ApkDescription_ApexApkMetadata)(nil),
	}
}

// Holds data specific to Split APKs.
type SplitApkMetadata struct {
	SplitId string `protobuf:"bytes,1,opt,name=split_id,json=splitId,proto3" json:"split_id,omitempty"`
	// Indicates whether this APK is the master split of the module.
	IsMasterSplit        bool     `protobuf:"varint,2,opt,name=is_master_split,json=isMasterSplit,proto3" json:"is_master_split,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SplitApkMetadata) Reset()         { *m = SplitApkMetadata{} }
func (m *SplitApkMetadata) String() string { return proto.CompactTextString(m) }
func (*SplitApkMetadata) ProtoMessage()    {}
func (*SplitApkMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{8}
}

func (m *SplitApkMetadata) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SplitApkMetadata.Unmarshal(m, b)
}
func (m *SplitApkMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SplitApkMetadata.Marshal(b, m, deterministic)
}
func (m *SplitApkMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SplitApkMetadata.Merge(m, src)
}
func (m *SplitApkMetadata) XXX_Size() int {
	return xxx_messageInfo_SplitApkMetadata.Size(m)
}
func (m *SplitApkMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_SplitApkMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_SplitApkMetadata proto.InternalMessageInfo

func (m *SplitApkMetadata) GetSplitId() string {
	if m != nil {
		return m.SplitId
	}
	return ""
}

func (m *SplitApkMetadata) GetIsMasterSplit() bool {
	if m != nil {
		return m.IsMasterSplit
	}
	return false
}

// Holds data specific to Standalone APKs.
type StandaloneApkMetadata struct {
	// Names of the modules fused in this standalone APK.
	FusedModuleName      []string `protobuf:"bytes,1,rep,name=fused_module_name,json=fusedModuleName,proto3" json:"fused_module_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StandaloneApkMetadata) Reset()         { *m = StandaloneApkMetadata{} }
func (m *StandaloneApkMetadata) String() string { return proto.CompactTextString(m) }
func (*StandaloneApkMetadata) ProtoMessage()    {}
func (*StandaloneApkMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{9}
}

func (m *StandaloneApkMetadata) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StandaloneApkMetadata.Unmarshal(m, b)
}
func (m *StandaloneApkMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StandaloneApkMetadata.Marshal(b, m, deterministic)
}
func (m *StandaloneApkMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StandaloneApkMetadata.Merge(m, src)
}
func (m *StandaloneApkMetadata) XXX_Size() int {
	return xxx_messageInfo_StandaloneApkMetadata.Size(m)
}
func (m *StandaloneApkMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_StandaloneApkMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_StandaloneApkMetadata proto.InternalMessageInfo

func (m *StandaloneApkMetadata) GetFusedModuleName() []string {
	if m != nil {
		return m.FusedModuleName
	}
	return nil
}

// Holds data specific to system APKs.
type SystemApkMetadata struct {
	// Names of the modules fused in this system APK.
	FusedModuleName []string `protobuf:"bytes,1,rep,name=fused_module_name,json=fusedModuleName,proto3" json:"fused_module_name,omitempty"`
	// Indicates whether the APK is uncompressed system APK, stub APK or
	// compressed system APK.
	SystemApkType        SystemApkMetadata_SystemApkType `protobuf:"varint,2,opt,name=system_apk_type,json=systemApkType,proto3,enum=android.bundle.SystemApkMetadata_SystemApkType" json:"system_apk_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *SystemApkMetadata) Reset()         { *m = SystemApkMetadata{} }
func (m *SystemApkMetadata) String() string { return proto.CompactTextString(m) }
func (*SystemApkMetadata) ProtoMessage()    {}
func (*SystemApkMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{10}
}

func (m *SystemApkMetadata) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SystemApkMetadata.Unmarshal(m, b)
}
func (m *SystemApkMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SystemApkMetadata.Marshal(b, m, deterministic)
}
func (m *SystemApkMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SystemApkMetadata.Merge(m, src)
}
func (m *SystemApkMetadata) XXX_Size() int {
	return xxx_messageInfo_SystemApkMetadata.Size(m)
}
func (m *SystemApkMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_SystemApkMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_SystemApkMetadata proto.InternalMessageInfo

func (m *SystemApkMetadata) GetFusedModuleName() []string {
	if m != nil {
		return m.FusedModuleName
	}
	return nil
}

func (m *SystemApkMetadata) GetSystemApkType() SystemApkMetadata_SystemApkType {
	if m != nil {
		return m.SystemApkType
	}
	return SystemApkMetadata_UNSPECIFIED_VALUE
}

// Holds data specific to APEX APKs.
type ApexApkMetadata struct {
	// Configuration for processing of APKs embedded in an APEX image.
	ApexEmbeddedApkConfig []*ApexEmbeddedApkConfig `protobuf:"bytes,1,rep,name=apex_embedded_apk_config,json=apexEmbeddedApkConfig,proto3" json:"apex_embedded_apk_config,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                 `json:"-"`
	XXX_unrecognized      []byte                   `json:"-"`
	XXX_sizecache         int32                    `json:"-"`
}

func (m *ApexApkMetadata) Reset()         { *m = ApexApkMetadata{} }
func (m *ApexApkMetadata) String() string { return proto.CompactTextString(m) }
func (*ApexApkMetadata) ProtoMessage()    {}
func (*ApexApkMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{11}
}

func (m *ApexApkMetadata) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApexApkMetadata.Unmarshal(m, b)
}
func (m *ApexApkMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApexApkMetadata.Marshal(b, m, deterministic)
}
func (m *ApexApkMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApexApkMetadata.Merge(m, src)
}
func (m *ApexApkMetadata) XXX_Size() int {
	return xxx_messageInfo_ApexApkMetadata.Size(m)
}
func (m *ApexApkMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_ApexApkMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_ApexApkMetadata proto.InternalMessageInfo

func (m *ApexApkMetadata) GetApexEmbeddedApkConfig() []*ApexEmbeddedApkConfig {
	if m != nil {
		return m.ApexEmbeddedApkConfig
	}
	return nil
}

type LocalTestingInfo struct {
	// Indicates if the bundle is built in local testing mode.
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// The local testing path, as specified in the base manifest.
	// This refers to the relative path on the external directory of the app where
	// APKs will be pushed for local testing.
	// Set only if local testing is enabled.
	LocalTestingPath     string   `protobuf:"bytes,2,opt,name=local_testing_path,json=localTestingPath,proto3" json:"local_testing_path,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LocalTestingInfo) Reset()         { *m = LocalTestingInfo{} }
func (m *LocalTestingInfo) String() string { return proto.CompactTextString(m) }
func (*LocalTestingInfo) ProtoMessage()    {}
func (*LocalTestingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_0dff099eb2e3dfdb, []int{12}
}

func (m *LocalTestingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LocalTestingInfo.Unmarshal(m, b)
}
func (m *LocalTestingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LocalTestingInfo.Marshal(b, m, deterministic)
}
func (m *LocalTestingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LocalTestingInfo.Merge(m, src)
}
func (m *LocalTestingInfo) XXX_Size() int {
	return xxx_messageInfo_LocalTestingInfo.Size(m)
}
func (m *LocalTestingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LocalTestingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LocalTestingInfo proto.InternalMessageInfo

func (m *LocalTestingInfo) GetEnabled() bool {
	if m != nil {
		return m.Enabled
	}
	return false
}

func (m *LocalTestingInfo) GetLocalTestingPath() string {
	if m != nil {
		return m.LocalTestingPath
	}
	return ""
}

func init() {
	proto.RegisterEnum("android.bundle.DeliveryType", DeliveryType_name, DeliveryType_value)
	proto.RegisterEnum("android.bundle.SystemApkMetadata_SystemApkType", SystemApkMetadata_SystemApkType_name, SystemApkMetadata_SystemApkType_value)
	proto.RegisterType((*BuildApksResult)(nil), "android.bundle.BuildApksResult")
	proto.RegisterType((*Variant)(nil), "android.bundle.Variant")
	proto.RegisterType((*ApkSet)(nil), "android.bundle.ApkSet")
	proto.RegisterType((*ModuleMetadata)(nil), "android.bundle.ModuleMetadata")
	proto.RegisterType((*AssetSliceSet)(nil), "android.bundle.AssetSliceSet")
	proto.RegisterType((*AssetModuleMetadata)(nil), "android.bundle.AssetModuleMetadata")
	proto.RegisterType((*InstantMetadata)(nil), "android.bundle.InstantMetadata")
	proto.RegisterType((*ApkDescription)(nil), "android.bundle.ApkDescription")
	proto.RegisterType((*SplitApkMetadata)(nil), "android.bundle.SplitApkMetadata")
	proto.RegisterType((*StandaloneApkMetadata)(nil), "android.bundle.StandaloneApkMetadata")
	proto.RegisterType((*SystemApkMetadata)(nil), "android.bundle.SystemApkMetadata")
	proto.RegisterType((*ApexApkMetadata)(nil), "android.bundle.ApexApkMetadata")
	proto.RegisterType((*LocalTestingInfo)(nil), "android.bundle.LocalTestingInfo")
}

func init() {
	proto.RegisterFile("commands.proto", fileDescriptor_0dff099eb2e3dfdb)
}

var fileDescriptor_0dff099eb2e3dfdb = []byte{
	// 1104 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0xcf, 0x6f, 0xe2, 0x46,
	0x14, 0x5e, 0x03, 0x0b, 0xe1, 0x05, 0xb0, 0x33, 0x1b, 0xba, 0xde, 0x68, 0x77, 0xcb, 0xba, 0x4a,
	0x85, 0xa2, 0x2a, 0xab, 0xa6, 0x3d, 0xad, 0xd4, 0x4a, 0x10, 0x9c, 0x96, 0x2d, 0x90, 0xc8, 0x26,
	0x89, 0x92, 0x4a, 0x1d, 0x4d, 0x98, 0x49, 0xd6, 0xc2, 0xbf, 0xca, 0x98, 0x28, 0xf9, 0x57, 0x7a,
	0xa9, 0x7a, 0xec, 0xb1, 0xd7, 0xfe, 0x51, 0x3d, 0xf5, 0xde, 0xca, 0x63, 0x03, 0xb6, 0xb1, 0xd4,
	0x64, 0xd5, 0x13, 0x7e, 0x6f, 0xbe, 0xf9, 0xe6, 0xbd, 0xf7, 0xbd, 0x79, 0x0c, 0x34, 0x26, 0x9e,
	0xe3, 0x10, 0x97, 0xf2, 0x7d, 0x7f, 0xe6, 0x05, 0x1e, 0x6a, 0x10, 0x97, 0xce, 0x3c, 0x8b, 0xee,
	0x5f, 0xcd, 0x5d, 0x6a, 0xb3, 0x9d, 0xda, 0xc4, 0x73, 0xaf, 0xad, 0x9b, 0x68, 0x75, 0x47, 0x0e,
	0xc8, 0xec, 0x86, 0x05, 0x96, 0x1b, 0x3b, 0xb4, 0x3f, 0x0b, 0x20, 0x77, 0xe7, 0x96, 0x4d, 0x3b,
	0xfe, 0x94, 0x1b, 0x8c, 0xcf, 0xed, 0x00, 0xbd, 0x81, 0x9a, 0x4f, 0x26, 0x53, 0x72, 0xc3, 0xb0,
	0x4b, 0x1c, 0xa6, 0x96, 0x5a, 0x52, 0xbb, 0x6a, 0x6c, 0xc6, 0xbe, 0x11, 0x71, 0x18, 0xfa, 0x12,
	0x2a, 0xb7, 0x64, 0x66, 0x11, 0x37, 0x50, 0xa5, 0x56, 0xb1, 0xbd, 0x79, 0xf0, 0x7c, 0x3f, 0x7d,
	0xee, 0xfe, 0x59, 0xb4, 0x6c, 0x2c, 0x70, 0xe8, 0x1d, 0x40, 0xb4, 0x14, 0x78, 0x9e, 0xad, 0x16,
	0x5a, 0x52, 0x7b, 0xf3, 0x60, 0x27, 0xbb, 0xab, 0xbb, 0x44, 0x18, 0x09, 0x34, 0xd2, 0x41, 0x26,
	0x9c, 0xb3, 0x00, 0x73, 0xdb, 0x9a, 0x30, 0xcc, 0x59, 0xa0, 0x16, 0xc5, 0xb1, 0xaf, 0xb2, 0x04,
	0x9d, 0x10, 0x66, 0x86, 0x28, 0x93, 0x05, 0x46, 0x9d, 0x24, 0x4d, 0x34, 0x02, 0x64, 0x7b, 0x13,
	0x62, 0xe3, 0x80, 0xf1, 0xb0, 0x06, 0xd8, 0x72, 0xaf, 0x3d, 0xf5, 0xa9, 0x08, 0xa5, 0x95, 0x65,
	0x1a, 0x84, 0xc8, 0x71, 0x04, 0xec, 0xbb, 0xd7, 0x9e, 0xa1, 0xd8, 0x19, 0x8f, 0xf6, 0x9b, 0x04,
	0x95, 0x38, 0x4f, 0xf4, 0x2d, 0x54, 0x97, 0xb5, 0x55, 0xa5, 0x7c, 0xca, 0x18, 0x3b, 0x5e, 0xe0,
	0x8c, 0xd5, 0x16, 0xf4, 0x16, 0x2a, 0xc4, 0x9f, 0x8a, 0xd4, 0x0a, 0x22, 0xb5, 0x4f, 0xd6, 0x52,
	0xf3, 0xa7, 0x61, 0x4e, 0x65, 0x22, 0x7e, 0xd1, 0x2e, 0x34, 0xe2, 0xd2, 0x62, 0x77, 0xee, 0x5c,
	0xb1, 0x99, 0x5a, 0x6c, 0x49, 0xed, 0xba, 0x51, 0x8f, 0xbd, 0x23, 0xe1, 0xd4, 0x7e, 0x91, 0xa0,
	0x1c, 0xed, 0x44, 0xdf, 0x81, 0xec, 0x78, 0x74, 0x6e, 0x33, 0xec, 0xb0, 0x80, 0x50, 0x12, 0x90,
	0x38, 0xd0, 0xd7, 0xd9, 0xa3, 0x86, 0x02, 0x36, 0x8c, 0x51, 0x46, 0xc3, 0x49, 0xd9, 0x21, 0x51,
	0x18, 0x2b, 0x65, 0x7c, 0x32, 0xb3, 0xfc, 0xc0, 0xf2, 0xdc, 0x38, 0xe6, 0xd7, 0x39, 0x31, 0xf7,
	0x56, 0x28, 0xa3, 0x41, 0x52, 0xb6, 0xf6, 0x6b, 0x01, 0x1a, 0xe9, 0xb3, 0x10, 0x82, 0x92, 0x68,
	0x3a, 0x49, 0x34, 0x9d, 0xf8, 0x46, 0x1d, 0xa8, 0x53, 0x66, 0x5b, 0xb7, 0x6c, 0x76, 0x8f, 0x83,
	0x7b, 0x9f, 0xa9, 0xe5, 0x96, 0xd4, 0x6e, 0x1c, 0xbc, 0xcc, 0x9e, 0xd6, 0x8b, 0x41, 0xe3, 0x7b,
	0x9f, 0x19, 0x35, 0x9a, 0xb0, 0xd0, 0x2b, 0x00, 0x8b, 0x63, 0xcb, 0xe5, 0x41, 0xd8, 0xb3, 0x61,
	0xa5, 0x36, 0x8c, 0xaa, 0xc5, 0xfb, 0x91, 0x03, 0x69, 0x50, 0xa3, 0xcc, 0x67, 0x2e, 0x65, 0xee,
	0xc4, 0x62, 0x5c, 0x2d, 0xb5, 0x8a, 0xed, 0xaa, 0x91, 0xf2, 0xa1, 0x6f, 0x92, 0x0a, 0x47, 0x4d,
	0xf3, 0x69, 0x7e, 0xe1, 0x72, 0x05, 0xfe, 0x1a, 0xb6, 0x3d, 0x17, 0x53, 0x16, 0x5e, 0x56, 0x4c,
	0x99, 0x3f, 0x63, 0x13, 0x12, 0x30, 0x2a, 0x6e, 0xc2, 0x46, 0xb7, 0xa0, 0x4a, 0x06, 0xf2, 0xdc,
	0x9e, 0x58, 0xee, 0x2d, 0x57, 0xb5, 0x3f, 0x24, 0xa8, 0xa7, 0x7a, 0x1a, 0x9d, 0x43, 0x33, 0xba,
	0x0b, 0xf9, 0x5a, 0x7e, 0x96, 0x7b, 0x23, 0x32, 0x82, 0x3e, 0x23, 0xeb, 0xce, 0xff, 0x4f, 0xd5,
	0xbf, 0x24, 0x78, 0x96, 0x73, 0xea, 0xc3, 0xa4, 0x2d, 0x3d, 0x5a, 0xda, 0xf7, 0xa0, 0xc4, 0xba,
	0xae, 0x6a, 0x51, 0xcc, 0x97, 0x27, 0x96, 0x7b, 0x59, 0x07, 0xd9, 0x4a, 0x3b, 0x3e, 0x52, 0xa4,
	0xdf, 0x25, 0x90, 0x33, 0xd4, 0x99, 0x86, 0x93, 0xb2, 0x0d, 0xb7, 0x96, 0x77, 0xf1, 0xd1, 0x79,
	0x7f, 0x5c, 0xac, 0xff, 0x94, 0xa0, 0x91, 0xd6, 0x0f, 0xbd, 0x5b, 0x1f, 0x5d, 0x2f, 0x73, 0x24,
	0xcf, 0xed, 0x6a, 0x04, 0x25, 0x9f, 0x04, 0x1f, 0xc4, 0xa1, 0x55, 0x43, 0x7c, 0xa3, 0x13, 0x40,
	0xdc, 0xb7, 0xad, 0x00, 0x87, 0xed, 0x94, 0x91, 0x64, 0x6d, 0x26, 0x9a, 0x21, 0xb2, 0xe3, 0x4f,
	0x17, 0x85, 0xfb, 0xfe, 0x89, 0xa1, 0xf0, 0x8c, 0x0f, 0x61, 0x78, 0x1e, 0x96, 0x8d, 0x12, 0xdb,
	0x73, 0x59, 0x9a, 0xb6, 0x24, 0x68, 0x77, 0xd7, 0x68, 0x97, 0xf0, 0x34, 0x77, 0x93, 0xe7, 0x2d,
	0xa0, 0x31, 0x6c, 0x2f, 0x7a, 0x28, 0xc5, 0xfe, 0xf4, 0xc1, 0x41, 0xa3, 0x78, 0x7f, 0x92, 0xd5,
	0x84, 0x67, 0xfc, 0x9e, 0x07, 0xcc, 0x49, 0x93, 0x96, 0x05, 0xe9, 0x9b, 0x35, 0x52, 0x01, 0x4d,
	0xb3, 0x6e, 0xf1, 0xac, 0x33, 0x0c, 0x35, 0xf9, 0x5f, 0xb8, 0x64, 0xad, 0x3c, 0x3c, 0xd4, 0xd5,
	0xbf, 0xe2, 0x92, 0x75, 0x08, 0x5b, 0xc4, 0x67, 0x77, 0xe9, 0x40, 0x37, 0xf2, 0x6f, 0x51, 0xc7,
	0x67, 0x77, 0x69, 0x46, 0x99, 0xa4, 0x5d, 0xdd, 0x1d, 0x50, 0x93, 0x4c, 0xd8, 0x73, 0x99, 0x77,
	0x8d, 0x6f, 0x89, 0x3d, 0x67, 0xda, 0x29, 0x28, 0xd9, 0xa0, 0xd0, 0x0b, 0xd8, 0x88, 0x5a, 0xc6,
	0xa2, 0xf1, 0x78, 0xa8, 0x08, 0xbb, 0x4f, 0xd1, 0xe7, 0x20, 0x5b, 0x1c, 0x3b, 0x84, 0x07, 0x6c,
	0x86, 0x85, 0x33, 0xea, 0x70, 0xa3, 0x6e, 0xf1, 0xa1, 0xf0, 0x0a, 0x36, 0xad, 0x0f, 0xcd, 0x5c,
	0xd1, 0xd1, 0x1e, 0x6c, 0x5d, 0xcf, 0x39, 0xa3, 0x8b, 0x81, 0x19, 0xcf, 0xa0, 0x70, 0xc0, 0xcb,
	0x62, 0x21, 0x1a, 0x53, 0xe1, 0xbb, 0xe6, 0x7d, 0x69, 0xa3, 0xa0, 0x14, 0xb5, 0xbf, 0x25, 0xd8,
	0x5a, 0x53, 0xe3, 0x31, 0x3c, 0xe8, 0x1c, 0xe4, 0x84, 0xf2, 0xe2, 0x82, 0x17, 0xc4, 0x05, 0x7f,
	0xfb, 0x9f, 0xaa, 0xaf, 0x3c, 0xe2, 0xce, 0xd7, 0x79, 0xd2, 0xd4, 0x2e, 0xa1, 0x9e, 0x5a, 0x47,
	0x4d, 0xd8, 0x3a, 0x1d, 0x99, 0x27, 0xfa, 0x61, 0xff, 0xa8, 0xaf, 0xf7, 0xf0, 0x59, 0x67, 0x70,
	0xaa, 0x2b, 0x4f, 0x10, 0x40, 0xd9, 0xbc, 0x30, 0xc7, 0xfa, 0x50, 0x91, 0x90, 0x0c, 0x9b, 0xd1,
	0x37, 0x36, 0xc7, 0xa7, 0x5d, 0xa5, 0x10, 0xee, 0x89, 0x1d, 0x87, 0xc7, 0xc3, 0x13, 0x43, 0x37,
	0x4d, 0xbd, 0xa7, 0x14, 0xb5, 0x9f, 0x41, 0xce, 0x48, 0x8b, 0x7e, 0x0a, 0x75, 0x64, 0x77, 0x98,
	0x39, 0x57, 0x8c, 0x52, 0x46, 0x45, 0x3a, 0xd1, 0x8b, 0x32, 0x7e, 0xf8, 0xed, 0xe6, 0x75, 0x87,
	0x1e, 0xc3, 0x3b, 0xfe, 0xf4, 0x50, 0x80, 0x8d, 0x26, 0xc9, 0x73, 0x6b, 0x97, 0xa0, 0x64, 0xdf,
	0x59, 0x48, 0x85, 0x0a, 0x73, 0xc9, 0x95, 0xcd, 0x68, 0x3c, 0x36, 0x17, 0x26, 0xfa, 0x22, 0xfb,
	0x7e, 0x4b, 0x8c, 0x9e, 0xd4, 0xeb, 0xec, 0x84, 0x04, 0x1f, 0xf6, 0x7e, 0x84, 0x5a, 0x72, 0x7a,
	0xa2, 0x17, 0xd0, 0x3c, 0x1d, 0xfd, 0x30, 0x3a, 0x3e, 0x1f, 0xe1, 0x9e, 0x3e, 0xe8, 0x9f, 0xe9,
	0xc6, 0x05, 0x1e, 0x5f, 0x9c, 0x84, 0xd5, 0x52, 0xa0, 0xd6, 0x1f, 0x99, 0xe3, 0xce, 0x60, 0x80,
	0xc7, 0xfd, 0xa1, 0xae, 0x48, 0xa8, 0x0e, 0xd5, 0xe3, 0x10, 0x37, 0xec, 0x8c, 0x7a, 0x4a, 0x21,
	0x2c, 0xe1, 0x51, 0xc7, 0x1c, 0xe3, 0xa3, 0xe3, 0xc1, 0xe0, 0xf8, 0x5c, 0x29, 0x76, 0xf7, 0x00,
	0x4d, 0x3c, 0x27, 0x93, 0xfb, 0xe5, 0x76, 0x6c, 0xe3, 0xc8, 0xc6, 0xe2, 0x8d, 0x7d, 0x55, 0x16,
	0x3f, 0x5f, 0xfd, 0x1b, 0x00, 0x00, 0xff, 0xff, 0xb1, 0xe5, 0xcb, 0x87, 0xab, 0x0b, 0x00, 0x00,
}
