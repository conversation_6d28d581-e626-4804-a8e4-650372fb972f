// Copyright 2019 Google Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//
// Sample project for creating an extended androidmk
//

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

blueprint_go_binary {
    name: "partner_androidmk",
    srcs: [
        "androidmk/androidmk.go",
    ],
    testSrcs: [
        "androidmk/androidmk_test.go",
    ],
    deps: [
        "androidmk-lib",
        "partner-bpfix-extensions",
    ],
}

blueprint_go_binary {
    name: "partner_bpfix",
    srcs: [
        "bpfix/bpfix.go",
    ],
    deps: [
        "bpfix-cmd",
        "partner-bpfix-extensions",
    ],
}

bootstrap_go_package {
    name: "partner-bpfix-extensions",
    pkgPath: "android/soong/partner/bpfix/extensions",
    srcs: ["bpfix/extensions/headers.go"],
    deps: ["bpfix-lib"],
}
