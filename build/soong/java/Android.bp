package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

bootstrap_go_package {
    name: "soong-java",
    pkgPath: "android/soong/java",
    deps: [
        "blueprint",
        "blueprint-pathtools",
        "soong",
        "soong-android",
        "soong-cc",
        "soong-dexpreopt",
        "soong-genrule",
        "soong-java-config",
        "soong-python",
        "soong-remoteexec",
        "soong-tradefed",
    ],
    srcs: [
        "aapt2.go",
        "aar.go",
        "android_manifest.go",
        "android_resources.go",
        "androidmk.go",
        "app_builder.go",
        "app.go",
        "app_import.go",
        "app_set.go",
        "base.go",
        "boot_jars.go",
        "bootclasspath.go",
        "bootclasspath_fragment.go",
        "builder.go",
        "classpath_element.go",
        "classpath_fragment.go",
        "device_host_converter.go",
        "dex.go",
        "dexpreopt.go",
        "dexpreopt_bootjars.go",
        "dexpreopt_config.go",
        "droiddoc.go",
        "droidstubs.go",
        "gen.go",
        "genrule.go",
        "hiddenapi.go",
        "hiddenapi_modular.go",
        "hiddenapi_monolithic.go",
        "hiddenapi_singleton.go",
        "jacoco.go",
        "java.go",
        "jdeps.go",
        "java_resources.go",
        "kotlin.go",
        "lint.go",
        "legacy_core_platform_api_usage.go",
        "platform_bootclasspath.go",
        "platform_compat_config.go",
        "plugin.go",
        "prebuilt_apis.go",
        "proto.go",
        "robolectric.go",
        "rro.go",
        "sdk.go",
        "sdk_library.go",
        "sdk_library_external.go",
        "support_libraries.go",
        "system_modules.go",
        "systemserver_classpath_fragment.go",
        "testing.go",
        "tradefed.go",
    ],
    testSrcs: [
        "androidmk_test.go",
        "app_import_test.go",
        "app_set_test.go",
        "app_test.go",
        "bootclasspath_fragment_test.go",
        "device_host_converter_test.go",
        "dexpreopt_test.go",
        "dexpreopt_bootjars_test.go",
        "droiddoc_test.go",
        "droidstubs_test.go",
        "hiddenapi_singleton_test.go",
        "jacoco_test.go",
        "java_test.go",
        "jdeps_test.go",
        "kotlin_test.go",
        "lint_test.go",
        "platform_bootclasspath_test.go",
        "platform_compat_config_test.go",
        "plugin_test.go",
        "rro_test.go",
        "sdk_test.go",
        "system_modules_test.go",
        "systemserver_classpath_fragment_test.go",
    ],
    pluginFor: ["soong_build"],
}
