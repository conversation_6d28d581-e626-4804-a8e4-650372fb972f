// Copyright 2015 Google Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//
// androidmk Android.mk to Blueprints translator
//

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

blueprint_go_binary {
    name: "androidmk",
    srcs: [
        "cmd/androidmk.go",
    ],
    deps: [
        "androidmk-lib",
    ],
}

bootstrap_go_package {
    name: "androidmk-lib",
    pkgPath: "android/soong/androidmk/androidmk",
    srcs: [
        "androidmk/android.go",
        "androidmk/androidmk.go",
        "androidmk/values.go",
    ],
    testSrcs: [
        "androidmk/androidmk_test.go",
    ],
    deps: [
        "androidmk-parser",
        "blueprint-parser",
        "bpfix-lib",
    ],
}

bootstrap_go_package {
    name: "androidmk-parser",
    pkgPath: "android/soong/androidmk/parser",
    srcs: [
        "parser/ast.go",
        "parser/make_strings.go",
        "parser/parser.go",
        "parser/scope.go",
    ],
    testSrcs: [
        "parser/make_strings_test.go",
        "parser/parser_test.go",
    ],
}
